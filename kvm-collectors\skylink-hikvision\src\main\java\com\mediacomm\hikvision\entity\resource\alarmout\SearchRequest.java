package com.mediacomm.hikvision.entity.resource.alarmout;

import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 *
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchRequest {
  private String name;
  private ArrayList<String> regionIndexCodes;
  private Boolean isSubRegion;
  private Integer pageNo;
  private Integer pageSize;
  private ArrayList<String> authCodes;
  private ArrayList<Expressions> expressions;
  private String orderBy;
  private String orderType;
}
