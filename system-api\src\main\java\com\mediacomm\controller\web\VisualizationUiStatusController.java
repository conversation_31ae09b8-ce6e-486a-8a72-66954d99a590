package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.VisualizationUiStatus;
import com.mediacomm.handler.websocket.WebSocketHandler;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.VisualizationUiStatusService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.WebSocketTopic;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * .
 */
@Tag(name = "控件管理")
@RestController
@RequestMapping(ResUrlDef.VIS_UI)
public class VisualizationUiStatusController
        extends SkyLinkController<VisualizationUiStatus, VisualizationUiStatusService> {

  @Autowired
  private WebSocketHandler handler;

  @Operation(summary = "保存控件信息")
  @PostMapping
  public Result<String> updateUiStatus(@RequestBody VisualizationUiStatus uiStatus) {
    if (StringUtils.isEmpty(uiStatus.getId())) {
      return Result.failure("The ID cannot be empty.", ResponseCode.EX_FAILURE_400);
    }
    VisualizationUiStatus existData = service.getById(uiStatus.getId());
    if (existData == null) {
      service.save(uiStatus);
    } else {
      service.updateById(uiStatus);
    }
    Map<String, Object> notice = new HashMap<>(2);
    notice.put("type", WebSocketTopic.EVENT_CHANGE_VIS_UI);
    notice.put("message", uiStatus);
    handler.sendMessage(ResUrlDef.WS_TERMINAL_NOTICE, JsonUtils.encode(notice));
    return Result.ok();
  }

  @Operation(summary = "获取控件信息")
  @GetMapping("/{id}")
  public Result<VisualizationUiStatus> getUiStatus(@PathVariable String id) {
    return Result.ok(service.getById(id));
  }

  @Operation(summary = "删除控件信息")
  @DeleteMapping("/{id}")
  public Result<String> deleteUiStatus(@PathVariable String id) {
    service.removeById(id);
    return Result.ok();
  }

}
