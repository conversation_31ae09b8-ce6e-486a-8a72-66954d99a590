package com.mediacomm.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.entity.dao.PositionGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PositionGroupVo extends PositionGroup {
  private String levelTitle;

  /**
   * .
   */
  public PositionGroupVo(PositionGroup group) {
    setPositionId(group.getPositionId());
    setPositionLevel(group.getPositionLevel());
    setName(group.getName());
    setParentId(group.getParentId());
    setFinishUseTime(group.getFinishUseTime());
    setMaxMeetPersonnel(group.getMaxMeetPersonnel());
    setStartUseTime(group.getStartUseTime());
    setSeqInParent(group.getSeqInParent());
  }
}
