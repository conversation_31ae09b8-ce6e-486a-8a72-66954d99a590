package com.mediacomm.config.websocket;

import com.mediacomm.handler.websocket.WebSocketHandler;
import com.mediacomm.handler.websocket.WebSocketInterceptor;
import com.mediacomm.system.variable.ResUrlDef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket config.
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

  @Autowired
  private WebSocketHandler webSocketHandler;
  @Autowired
  private WebSocketInterceptor webSocketInterceptor;

  @Override
  public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
    registry.addHandler(webSocket<PERSON><PERSON><PERSON>, ResUrlDef.WS_BACKSTAGE_NOTICE,
            ResUrlDef.WS_TERMINAL_NOTICE, ResUrlDef.WS_FILE_NOTICE)
        .addInterceptors(webSocketInterceptor)
        .setAllowedOrigins("*"); // 支持跨域
  }
}
