package com.mediacomm.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.entity.dao.ExtOption;
import com.mediacomm.system.variable.sysenum.ExtPropertySourceType;
import java.io.Serializable;
import java.util.Collection;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExtPropertyVo implements Serializable {
  private String name;
  private String unit;
  private boolean required;
  private String paramType;
  private String displayName;
  private String defaultValue;
  private RangeVo range;
  private Collection<ExtOption.EnumOption> enumOptions;
  private ExtPropertySourceType enumSourceType; // 选项数据来源类型
  private String enumSource; // 选项数据来源
  private String enumLabelField; // 选项数据关键字段名
  private String enumValueField; // 选项数据内容字段名
  private boolean internal; // 内部属性(true表不可修改)
}
