package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mediacomm.entity.dao.KvmAssetGroup;
import com.mediacomm.entity.vo.KvmAssetGroupVo;
import java.util.Collection;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * .
 */
@Mapper
public interface KvmAssetGroupMapper extends BaseMapper<KvmAssetGroup> {

  /**
   * 根据分组类型及所在房间Id查找分组及对应的房间信息.
   *
   * @param roomId 房间Id.
   * @param type Tx或Rx.
   * @return Collection.
   */
  @Select("select kg.asset_group_id, kg.name, kg.room_id, kg.background_color, kg.typeface_color, kg.parent_group_id, "
          + "kg.seq, pg.name as room_name from kvm_asset_group as kg JOIN position_group as pg "
          + "on kg.room_id = #{roomId} and kg.group_type = #{type} and pg.position_id = #{roomId}")
  Collection<KvmAssetGroupVo> findByRoomIdAndType(@Param("roomId") Integer roomId,
                                                  @Param("type") String type);

  /**
   * 根据分组类型及父分组Id查找分组及对应的房间的信息.
   */
  @Select("select kg.asset_group_id, kg.name, kg.room_id, kg.background_color, kg.typeface_color, kg.parent_group_id, "
          + "kg.seq, pg.name as room_name from kvm_asset_group as kg JOIN position_group as pg "
          + "on kg.room_id = #{roomId} and kg.group_type = #{type} and parent_group_id = #{pid} "
          + "and pg.position_id = #{roomId} ORDER BY kg.seq")
  Collection<KvmAssetGroupVo> findChildGroup(@Param("roomId") Integer roomId,
                                             @Param("pid") Integer pid,
                                             @Param("type") String type);

  /**
   * 根据分组类型查找分组及对应的房间信息.
   *
   * @param type Tx或Rx.
   * @return Collection.
   */
  @Select("select kg.asset_group_id, kg.name, kg.room_id, kg.background_color, kg.typeface_color, kg.parent_group_id, "
          + "kg.seq, pg.name as room_name from kvm_asset_group as kg JOIN position_group as pg "
          + "on kg.room_id = pg.position_id and kg.group_type = #{type}")
  Collection<KvmAssetGroupVo> findByType(@Param("type") String type);

  @Insert({"<script>",
      "insert into asset_group_merge (aid, gid, seq_in_group) VALUES ",
      "<foreach collection='assetIds' item='assetId' index='idx' separator=','>",
      "(#{assetId}, #{groupId}, #{idx}+1)",
      "</foreach>",
      "</script>"})
  boolean insertToMerge(Integer groupId, Collection<String> assetIds);

  @Delete({"<script>",
      "delete from asset_group_merge where gid = #{groupId} and aid in ",
      "<foreach collection='assetIds' item='assetId' open='(' separator=',' close=')'>",
      "#{assetId}",
      "</foreach>",
      "</script>"})
  boolean delFromMerge(Integer groupId, Collection<String> assetIds);

  @Delete("delete from asset_group_merge where gid = #{groupId}")
  boolean delAllFromMerge(Integer groupId);
}
