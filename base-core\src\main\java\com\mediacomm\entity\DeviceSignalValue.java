package com.mediacomm.entity;

import com.mediacomm.entity.dao.AiThreshold;
import com.mediacomm.entity.dao.DiThreshold;
import com.mediacomm.entity.dao.Signal;
import com.mediacomm.entity.dao.SignalValue;
import com.mediacomm.entity.dao.StatusThreshold;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import com.mediacomm.util.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Data
@Slf4j
public class DeviceSignalValue {
  private String masterId;
  private String deviceId;
  private String signalId;
  private String deviceName;
  private String signalName;
  private long updateTime;
  private Integer expire;
  private SubSystemType.AlarmLevel alarmLevel = SubSystemType.AlarmLevel.LEVEL_0;
  private Integer thresholdSeq;
  private String statusName;
  private SignalValue signalValue;
  private String suggestion = "";
  private String measurement;
  private int fixed = 3;

  public void setSignalValue(Signal signal, SignalValue value) {
    if (signal.getSignalType() != value.getSignalType()) {
      log.error("Signal {} type is {}, signal value type is {}", signal.getSignalId(),
              signal.getSignalType(), value);
      return;
    }
    statusName = signal.getNormalDesc();
    alarmLevel = SubSystemType.AlarmLevel.LEVEL_0;
    this.signalValue = value;
    switch (signal.getSignalType()) {
      case BOOL -> setBoolValue(signal);
      case STATUS -> setStatusValue(signal);
      case ANALOG -> setAnalogValue(signal);
      case STRING -> log.debug("SignalType is string.");
      default -> log.error("SignalType is not support:{}", JsonUtils.encode(signal));
    }
  }

  private void setBoolValue(Signal signal) {
    boolean b = signalValue.getBitValue();
    for (DiThreshold threshold : signal.getDiThresholds()) {
      if (threshold.isDiValue() == b) {
        thresholdSeq = threshold.isDiValue() ? 1 : 0;
        alarmLevel = threshold.getAlarmLevel();
        statusName = threshold.getThresholdName();
        suggestion = threshold.getThresholdDesc();
      }
    }
  }

  private void setStatusValue(Signal signal) {
    int status = signalValue.getStatusValue();
    for (StatusThreshold threshold : signal.getStatusThresholds()) {
      if (threshold.getStatusValue() == status) {
        thresholdSeq = threshold.getStatusValue();
        alarmLevel = threshold.getAlarmLevel();
        statusName = threshold.getThresholdName();
        suggestion = threshold.getThresholdDesc();
      }
    }
  }

  private void setAnalogValue(Signal signal) {
    double dValue = signalValue.getFloatValue();
    for (AiThreshold threshold : signal.getAiThresholds()) {
      if (threshold.getLowerThreshold() != null && threshold.getUpperThreshold() != null) {
        if (dValue >= threshold.getLowerThreshold() && dValue < threshold.getUpperThreshold()) {
          thresholdSeq = threshold.getThresholdSeq();
          alarmLevel = threshold.getAlarmLevel();
          statusName = threshold.getThresholdName();
          suggestion = threshold.getThresholdDesc();
        } else if (dValue >= threshold.getLowerThreshold()) {
          thresholdSeq = threshold.getThresholdSeq();
          alarmLevel = threshold.getAlarmLevel();
          statusName = threshold.getThresholdName();
          suggestion = threshold.getThresholdDesc();
        } else if (dValue < threshold.getUpperThreshold()) {
          thresholdSeq = threshold.getThresholdSeq();
          alarmLevel = threshold.getAlarmLevel();
          statusName = threshold.getThresholdName();
          suggestion = threshold.getThresholdDesc();
        }
      }
    }
  }
}
