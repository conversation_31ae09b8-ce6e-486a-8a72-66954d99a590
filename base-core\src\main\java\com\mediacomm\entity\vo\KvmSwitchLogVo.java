package com.mediacomm.entity.vo;

import com.mediacomm.entity.dao.KvmSwitchLog;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KvmSwitchLogVo extends KvmSwitchLog {
  private String masterName;

  public KvmSwitchLogVo(KvmSwitchLog kvmSwitchLog) {
    setMasterId(kvmSwitchLog.getMasterId());
    setSwitchMode(kvmSwitchLog.getSwitchMode());
    setSwitchTime(kvmSwitchLog.getSwitchTime());
    setCpu(kvmSwitchLog.getCpu());
    setCon(kvmSwitchLog.getCon());
    setDisconnect(kvmSwitchLog.isDisconnect());
    setFromUser(kvmSwitchLog.getFromUser());
    setSwitchTime(kvmSwitchLog.getSwitchTime());
  }
}
