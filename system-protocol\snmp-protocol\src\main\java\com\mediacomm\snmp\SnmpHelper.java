package com.mediacomm.snmp;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.snmp4j.CommunityTarget;
import org.snmp4j.PDU;
import org.snmp4j.Snmp;
import org.snmp4j.TransportMapping;
import org.snmp4j.event.ResponseEvent;
import org.snmp4j.mp.SnmpConstants;
import org.snmp4j.smi.Address;
import org.snmp4j.smi.GenericAddress;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.OctetString;
import org.snmp4j.smi.VariableBinding;
import org.snmp4j.transport.DefaultUdpTransportMapping;

@Slf4j
public class SnmpHelper {
  private static final int TIMEOUT = 1000;
  private static final int RETRIES = 1;
  private CommunityTarget<Address> target;

  public SnmpHelper(String host, int version) {
    Address address = GenericAddress.parse(String.format("udp:%s/161", host));
    target = new CommunityTarget<>(address, new OctetString("public"));
    target.setTimeout(TIMEOUT);
    target.setRetries(RETRIES);
    target.setVersion(version);// SnmpConstants.version1
  }

  public SnmpHelper(String host) {
    Address address = GenericAddress.parse(String.format("udp:%s/161", host));
    target = new CommunityTarget<>();
    target.setAddress(address);
    target.setTimeout(TIMEOUT);
    target.setCommunity(new OctetString("public"));
    target.setRetries(RETRIES);
    target.setVersion(SnmpConstants.version1);
  }

  public SnmpHelper(String host, int version, String community) {
    Address address = GenericAddress.parse(String.format("udp:%s/161", host));
    target = new CommunityTarget<>(address, new OctetString(community));
    target.setTimeout(TIMEOUT);
    target.setRetries(RETRIES);
    target.setVersion(version);// SnmpConstants.version1
  }

  public SnmpHelper(String host, String community) {
    Address address = GenericAddress.parse(String.format("udp:%s/161", host));
    target = new CommunityTarget<>(address, new OctetString(community));
    target.setTimeout(TIMEOUT);
    target.setRetries(RETRIES);
    target.setVersion(SnmpConstants.version1);
  }

  public List<VariableBinding> simpleGet(List<String> oids) {
    TransportMapping<?> transport = null;
    Snmp protocal = null;
    List<VariableBinding> variables = new ArrayList<>();
    try {
      transport = new DefaultUdpTransportMapping();
      transport.listen();
      protocal = new Snmp(transport);

      for (String oid : oids) {
        PDU request = new PDU();
        request.add(new VariableBinding(new OID(oid)));
        request.setType(PDU.GET);
        ResponseEvent responseEvent = protocal.send(request, target);
        PDU response = responseEvent.getResponse();

        if (response != null && response.getErrorStatus() == PDU.noError) {
          variables.add(response.getVariableBindings().stream().findFirst().get());
        }
      }
    } catch (IOException e) {
      log.error(e.getMessage(), e);
     return null;
    } finally {
      try {
        if (protocal != null) {
          protocal.close();
        }
        if (transport!= null) {
          transport.close();
        }
      } catch (IOException e) {
        log.error(e.getMessage(), e);
      }
    }
    return variables;
  }

  public List<VariableBinding> simpleGetNext(List<String> oids) {
    TransportMapping<?> transport = null;
    Snmp protocal = null;
    List<VariableBinding> variables = new ArrayList<>();
    try {
      transport = new DefaultUdpTransportMapping();
      transport.listen();
      protocal = new Snmp(transport);

      for (String oid : oids) {
        PDU request = new PDU();
        request.add(new VariableBinding(new OID(oid)));
        request.setType(PDU.GETNEXT);
        ResponseEvent<Address> responseEvent = protocal.send(request, target);
        PDU response = responseEvent.getResponse();

        if (response != null && response.getErrorStatus() == PDU.noError) {
          variables.add(response.getVariableBindings().stream().findFirst().get());
        }
      }
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      return null;
    } finally {
      try {
        if (protocal != null) {
          protocal.close();
        }
        if (transport!= null) {
          transport.close();
        }
      } catch (IOException e) {
        log.error(e.getMessage(), e);
      }
    }
    return variables;
  }

  public List<VariableBinding> get(String startOid, String endOid) {
    TransportMapping<?> transport = null;
    Snmp protocal = null;
    List<VariableBinding> variables = new ArrayList<>();
    try {
      transport = new DefaultUdpTransportMapping();
      transport.listen();
      protocal = new Snmp(transport);
      PDU request = new PDU();
      request.add(new VariableBinding(new OID(startOid)));
      request.setType(PDU.GETNEXT);

      ResponseEvent responseEvent = protocal.send(request, target);
      PDU response = responseEvent.getResponse();

      while (response != null) {
        if (response.getErrorStatus() != PDU.noError) {
          return variables;
        }
        if (response.getVariableBindings().stream().findFirst().get()
            .getOid().compareTo(new OID(endOid)) > 0) {
          break;
        }
        variables.add(response.getVariableBindings().stream().findFirst().get());
        request = new PDU();
        request.add(new VariableBinding(response.getVariableBindings().stream().findFirst().get()
            .getOid()));
        request.setType(PDU.GETNEXT);
        responseEvent = protocal.send(request, target);
        response = responseEvent.getResponse();
      }
      return variables;
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      return null;
    } finally {
      try {
        if (protocal != null) {
          protocal.close();
        }
        if (transport!= null) {
          transport.close();
        }
      } catch (IOException e) {
        log.error(e.getMessage(), e);
      }
    }
  }

  public List<VariableBinding> getSubTree(String startOid) {
    List<VariableBinding> variables = new ArrayList<>();
    String endOid = startOid.substring(0, startOid.lastIndexOf('.') + 1)
      + (Integer.parseInt(startOid.substring(startOid.lastIndexOf('.') + 1)) + 1);
    TransportMapping<?> transport = null;
    Snmp protocal = null;
    try {
      transport = new DefaultUdpTransportMapping();
      transport.listen();
      protocal = new Snmp(transport);
      PDU request = new PDU();
      request.setType(PDU.GETNEXT);
      request.add(new VariableBinding(new OID(startOid)));

      ResponseEvent responseEvent = protocal.send(request, target);
      PDU response = responseEvent.getResponse();

      while (response != null) {
        if (response.getErrorStatus() != PDU.noError) {
          return variables;
        }
        if (response.getVariableBindings().stream().findFirst().get()
            .getOid().compareTo(new OID(endOid)) >= 0) {
          break;
        }
        variables.add(response.getVariableBindings().stream().findFirst().get());
        request = new PDU();
        request.add(new VariableBinding(response.getVariableBindings().stream().findFirst().get()
            .getOid()));
        request.setType(PDU.GETNEXT);
        responseEvent = protocal.send(request, target);
        response = responseEvent.getResponse();
      }
      return variables;
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      return null;
    } finally {
      try {
        if (protocal != null) {
          protocal.close();
        }
        if (transport!= null) {
          transport.close();
        }
      } catch (IOException e) {
        log.error(e.getMessage(), e);
      }
    }
  }

  public List<VariableBinding> getSubTreeFinishWithValue(String startOid, String finishValue) {
    String endOid = startOid.substring(0, startOid.lastIndexOf('.') + 1)
      + (Integer.parseInt(startOid.substring(startOid.lastIndexOf('.') + 1)) + 1);
    TransportMapping<?> transport = null;
    Snmp protocal = null;
    List<VariableBinding> variables = new ArrayList<>();
    try {
      transport = new DefaultUdpTransportMapping();
      transport.listen();
      protocal = new Snmp(transport);
      PDU request = new PDU();
      request.add(new VariableBinding(new OID(startOid)));
      request.setType(PDU.GETNEXT);

      ResponseEvent responseEvent = protocal.send(request, target);
      PDU response = responseEvent.getResponse();

      while (response != null) {
        if (response.getErrorStatus() != PDU.noError) {
          return variables;
        }
        if (response.getVariableBindings().stream().findFirst().get()
            .getOid().compareTo(new OID(endOid)) >= 0) {
          break;
        }
        VariableBinding variableBinding = response.getVariableBindings().stream().findFirst().get();
        if (variableBinding.getVariable().toString().equals(finishValue)) {
          break;
        }
        variables.add(variableBinding);
        request = new PDU();
        request.add(new VariableBinding(response.getVariableBindings().stream().findFirst().get()
            .getOid()));
        request.setType(PDU.GETNEXT);
        responseEvent = protocal.send(request, target);
        response = responseEvent.getResponse();
      }
      return variables;
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      return null;
    } finally {
      try {
        if (protocal != null) {
          protocal.close();
        }
        if (transport!= null) {
          transport.close();
        }
      } catch (IOException e) {
        log.error(e.getMessage(), e);
      }
    }
  }

  public List<VariableBinding> getSubTree(String startOid, int count) {
    TransportMapping<?> transport = null;
    Snmp protocal = null;
    List<VariableBinding> variables = new ArrayList<>();
    try {
      transport = new DefaultUdpTransportMapping();
      transport.listen();
      protocal = new Snmp(transport);
      PDU request = new PDU();
      request.add(new VariableBinding(new OID(startOid)));
      request.setType(PDU.GETNEXT);

      ResponseEvent responseEvent = protocal.send(request, target);
      PDU response = responseEvent.getResponse();

      while (response != null) {
        if (response.getErrorStatus() != PDU.noError) {
          return variables;
        }
        if (variables.size() >= count) {
          break;
        }
        variables.add(response.getVariableBindings().stream().findFirst().get());
        request = new PDU();
        request.add(new VariableBinding(response.getVariableBindings().stream().findFirst().get()
            .getOid()));
        request.setType(PDU.GETNEXT);
        responseEvent = protocal.send(request, target);
        response = responseEvent.getResponse();
      }
      return variables;
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      return null;
    } finally {
      try {
        if (protocal != null) {
          protocal.close();
        }
        if (transport!= null) {
          transport.close();
        }
      } catch (IOException e) {
        log.error(e.getMessage(), e);
      }
    }
  }

  public SnmpRunnable simpleGetSync(List<String> oids, SnmpSyncCallback cbk) {
    return new SnmpRunnable(cbk) {
      @Override
      protected void doRequest(Snmp protocal) throws IOException {
        for (String oid : oids) {
          PDU request = new PDU();
          request.add(new VariableBinding(new OID(oid)));
          request.setType(PDU.GET);
          ResponseEvent responseEvent = protocal.send(request, target);
          PDU response = responseEvent.getResponse();

          if (response != null && response.getErrorStatus() == PDU.noError) {
            variables.add(response.getVariableBindings().stream().findFirst().get());
          }
        }
      }
    };
  }

  public SnmpRunnable simpleGetNext(List<String> oids, SnmpSyncCallback cbk) {
    return new SnmpRunnable(cbk) {
      @Override
      protected void doRequest(Snmp protocal) throws IOException {
        for (String oid : oids) {
          PDU request = new PDU();
          request.add(new VariableBinding(new OID(oid)));
          request.setType(PDU.GETNEXT);
          ResponseEvent responseEvent = protocal.send(request, target);
          PDU response = responseEvent.getResponse();

          if (response != null && response.getErrorStatus() == PDU.noError) {
            variables.add(response.getVariableBindings().stream().findFirst().get());
          }
        }
      }
    };
  }

  public SnmpRunnable get(String startOid, String endOid, SnmpSyncCallback cbk) {
    return new SnmpRunnable(cbk) {
      @Override
      protected void doRequest(Snmp protocal) throws IOException {
        PDU request = new PDU();
        request.add(new VariableBinding(new OID(startOid)));
        request.setType(PDU.GETNEXT);

        ResponseEvent responseEvent = protocal.send(request, target);
        PDU response = responseEvent.getResponse();

        while (response != null) {
          if (response.getErrorStatus() != PDU.noError) {
            return;
          }
          if (response.getVariableBindings().stream().findFirst().get().getOid().compareTo(new OID(endOid)) > 0) {
            break;
          }
          request = new PDU();
          request.add(new VariableBinding(response.getVariableBindings().stream().findFirst().get().getOid()));
          request.setType(PDU.GETNEXT);
          responseEvent = protocal.send(request, target);
          response = responseEvent.getResponse();
        }
      }
    };
  }

  public SnmpRunnable getSubTree(String startOid, SnmpSyncCallback cbk) {
    return new SnmpRunnable(cbk) {
      @Override
      protected void doRequest(Snmp protocal) throws IOException {
        String endOid = startOid.substring(0, startOid.lastIndexOf('.') + 1)
          + (Integer.parseInt(startOid.substring(startOid.lastIndexOf('.') + 1)) + 1);
        PDU request = new PDU();
        request.setType(PDU.GETNEXT);
        request.add(new VariableBinding(new OID(startOid)));

        ResponseEvent responseEvent = protocal.send(request, target);
        PDU response = responseEvent.getResponse();

        while (response != null) {
          if (response.getErrorStatus() != PDU.noError) {
            return;
          }
          if (response.getVariableBindings().stream().findFirst().get().getOid().compareTo(new OID(endOid)) >= 0) {
            break;
          }
          variables.add(response.getVariableBindings().stream().findFirst().get());
          request = new PDU();
          request.add(new VariableBinding(response.getVariableBindings().stream().findFirst().get().getOid()));
          request.setType(PDU.GETNEXT);
          responseEvent = protocal.send(request, target);
          response = responseEvent.getResponse();
        }
      }
    };
  }

  public SnmpRunnable getSubTreeFinishWithValue(String startOid, String finishValue, SnmpSyncCallback cbk) {
    return new SnmpRunnable(cbk) {
      @Override
      protected void doRequest(Snmp protocal) throws IOException {
        String endOid = startOid.substring(0, startOid.lastIndexOf('.') + 1)
          + (Integer.parseInt(startOid.substring(startOid.lastIndexOf('.') + 1)) + 1);
        PDU request = new PDU();
        request.add(new VariableBinding(new OID(startOid)));
        request.setType(PDU.GETNEXT);

        ResponseEvent responseEvent = protocal.send(request, target);
        PDU response = responseEvent.getResponse();

        while (response != null) {
          if (response.getErrorStatus() != PDU.noError) {
            return;
          }
          if (response.getVariableBindings().stream().findFirst().get().getOid().compareTo(new OID(endOid)) >= 0) {
            break;
          }
          VariableBinding variableBinding = response.getVariableBindings().stream().findFirst().get();
          if (variableBinding.getVariable().toString().equals(finishValue)) {
            break;
          }
          variables.add(variableBinding);
          request = new PDU();
          request.add(new VariableBinding(response.getVariableBindings().stream().findFirst().get().getOid()));
          request.setType(PDU.GETNEXT);
          responseEvent = protocal.send(request, target);
          response = responseEvent.getResponse();
        }
      }
    };
  }

  public SnmpRunnable getSubTreeFinishWithValue(String startOid, String finishValue, int count, SnmpSyncCallback cbk) {
    return new SnmpRunnable(cbk) {
      @Override
      protected void doRequest(Snmp protocal) throws IOException {
        int finishedCount = 0;
        String endOid = startOid.substring(0, startOid.lastIndexOf('.') + 1)
          + (Integer.parseInt(startOid.substring(startOid.lastIndexOf('.') + 1)) + 1);
        PDU request = new PDU();
        request.add(new VariableBinding(new OID(startOid)));
        request.setType(PDU.GETNEXT);

        ResponseEvent responseEvent = protocal.send(request, target);
        PDU response = responseEvent.getResponse();

        while (response != null) {
          if (response.getErrorStatus() != PDU.noError) {
            return;
          }
          if (response.getVariableBindings().stream().findFirst().get().getOid().compareTo(new OID(endOid)) >= 0) {
            break;
          }

          VariableBinding variableBinding = response.getVariableBindings().stream().findFirst().get();
          if (variableBinding.getVariable().toString().equals(finishValue)) {
            finishedCount++;
            if (finishedCount >= count) {
              break;
            }
          }else {
            finishedCount = 0;
          }
          variables.add(variableBinding);
          request = new PDU();
          request.add(new VariableBinding(response.getVariableBindings().stream().findFirst().get().getOid()));
          request.setType(PDU.GETNEXT);
          responseEvent = protocal.send(request, target);
          response = responseEvent.getResponse();
        }
      }
    };
  }

  public SnmpRunnable getSubTree(String startOid, int count, SnmpSyncCallback cbk) {
    return new SnmpRunnable(cbk) {
      @Override
      protected void doRequest(Snmp protocal) throws IOException {
        PDU request = new PDU();
        request.add(new VariableBinding(new OID(startOid)));
        request.setType(PDU.GETNEXT);

        ResponseEvent responseEvent = protocal.send(request, target);
        PDU response = responseEvent.getResponse();

        while (response != null) {
          if (response.getErrorStatus() != PDU.noError) {
            return;
          }
          if (variables.size() >= count) {
            break;
          }
          variables.add(response.getVariableBindings().stream().findFirst().get());
          request = new PDU();
          request.add(new VariableBinding(response.getVariableBindings().stream().findFirst().get().getOid()));
          request.setType(PDU.GETNEXT);
          responseEvent = protocal.send(request, target);
          response = responseEvent.getResponse();
        }
      }
    };
  }
}
