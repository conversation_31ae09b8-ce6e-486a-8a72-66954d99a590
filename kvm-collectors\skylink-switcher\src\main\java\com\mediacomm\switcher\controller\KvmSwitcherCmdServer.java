package com.mediacomm.switcher.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import com.mediacomm.config.rabbitmqtt.IMqttSender;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.EnvDevice;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.RegistrationStatus;
import com.mediacomm.entity.message.reqeust.SwitcherDevice;
import com.mediacomm.entity.message.reqeust.body.IdRequestBody;
import com.mediacomm.entity.message.reqeust.body.PositionRequestBody;
import com.mediacomm.entity.message.reqeust.body.RegisterRequestBody;
import com.mediacomm.entity.message.reqeust.body.ScanRequestBody;
import com.mediacomm.entity.message.reqeust.body.SwitcherDevicesBody;
import com.mediacomm.entity.vo.EnvDeviceVo;
import com.mediacomm.switcher.domain.SwitcherResponse;
import com.mediacomm.switcher.domain.SwitcherRpcConstants;
import com.mediacomm.switcher.domain.SwitcherRpcRequestBody;
import com.mediacomm.switcher.domain.SwitcherUdpBody;
import com.mediacomm.switcher.domain.SwitcherUdpMsgType;
import com.mediacomm.switcher.util.update.InspectSwitcherUtil;
import com.mediacomm.system.base.kvm.CmdServer;
import com.mediacomm.system.service.EnvDeviceService;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.SkyLinkStringUtil;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;

/**
 * KvmSwitcherCmdServer.
 */
@Slf4j
@Controller
public class KvmSwitcherCmdServer extends CmdServer {
  @Resource
  protected EnvDeviceService envDeviceService;
  @Resource
  private IMqttSender iMqttSender;
  @Value("${server.udp-broadcast-ip}")
  private String udpBroadcastIp;
  private static final int UDP_PORT = 20001;
  private static final int UDP_RECEIVE_PORT = 20002;
  private static final int SO_TIMEOUT = 200;
  private static final int RECEIVE_BUFFER_SIZE = 1024;
  private static final int SERVER_PORT = 1883;
  /**
   * 通过广播获取的所有的switcher设备Table(sn, mac, UdpResponse).
   */
  private Table<String, String, SwitcherUdpBody<SwitcherDevice>> allSwitcherDeviceTable =
      HashBasedTable.create();

  /**
   * 扫描设备.
   */
  public String scanDevices(String msg) {
    MqRequest<ScanRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    if (request.getBody() == null) {
      request.setBody(new ScanRequestBody(udpBroadcastIp, UDP_PORT));
    }
    SwitcherUdpBody<Void> udpRequest = new SwitcherUdpBody<>();
    String msgId = SkyLinkStringUtil.uuid();
    udpRequest.setMsgId(msgId);
    udpRequest.setMsgType(SwitcherUdpMsgType.SWITCHER_SCAN);
    String data = JsonUtils.encode(udpRequest);
    allSwitcherDeviceTable.clear();
    List<SwitcherDevice> switcherDevices = new ArrayList<>();
    udpBroadcaster(data, rec -> {
      if (Strings.isNullOrEmpty(rec)) {
        return;
      }
      SwitcherUdpBody<SwitcherDevice> dev =
          JsonUtils.decode(rec, new TypeReference<>() {
          });
      if (dev != null && dev.getMsgId().equals(msgId) && dev.getBody() != null) {
        switcherDevices.add(dev.getBody());
        allSwitcherDeviceTable.put(dev.getBody().getSn(), dev.getBody().getMac(), dev);
      }
    }, SO_TIMEOUT);
    return Result.okStr(switcherDevices);
  }

  public void udpBroadcaster(String msg, Consumer<String> consumer, int timeout) {
    if (consumer == null) {
      return;
    }
    try (DatagramSocket socket = new DatagramSocket(UDP_RECEIVE_PORT)) {
      socket.setSoTimeout(timeout);
      socket.setBroadcast(true);
      byte[] buffer = msg.getBytes(StandardCharsets.UTF_8);
      InetAddress broadcastAddress = InetAddress.getByName(udpBroadcastIp);
      DatagramPacket packet = new DatagramPacket(buffer, buffer.length, broadcastAddress, UDP_PORT);
      socket.send(packet);
      while (true) {
        byte[] receiveBuffer = new byte[RECEIVE_BUFFER_SIZE];
        DatagramPacket receivePacket = new DatagramPacket(receiveBuffer, receiveBuffer.length);
        socket.receive(receivePacket);
        String receivedMsg = new String(receivePacket.getData(), 0, receivePacket.getLength(),
            StandardCharsets.UTF_8);
        consumer.accept(receivedMsg);
        log.info("Received message: {}", receivedMsg);
      }
    } catch (IOException ex) {
      log.error("Error broadcasting message: ", ex);
    }
  }

  /**
   * 设置设备ip.
   */
  public String setIp(String msg) {
    MqRequest<SwitcherDevicesBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null || request.getBody() == null || request.getBody().getDevices() == null
        || request.getBody().getDevices().isEmpty()) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    // 检查设置ip的设备是否已注册，未注册才可修改
    List<SwitcherDevice> validDevices = new ArrayList<>();
    request.getBody().getDevices().forEach(device -> {
      if (!Strings.isNullOrEmpty(device.getSn()) && !Strings.isNullOrEmpty(device.getMac()) &&
          !Strings.isNullOrEmpty(device.getIp()) && !Strings.isNullOrEmpty(device.getMask()) &&
          !Strings.isNullOrEmpty(device.getGateway())) {
        EnvDeviceVo envDeviceVo = envDeviceService.oneBySnAndMac(device.getSn(), device.getMac());
        if (envDeviceVo == null) {
          validDevices.add(device);
        }
      }
    });
    List<SwitcherDevice> devices = new ArrayList<>();
    SwitcherUdpBody<SwitcherDevicesBody> udpRequest = new SwitcherUdpBody<>();
    List<List<SwitcherDevice>> partition = Lists.partition(validDevices, 30);
    for (List<SwitcherDevice> switcherDevices : partition) {
      String msgId = SkyLinkStringUtil.uuid();
      udpRequest.setMsgId(msgId);
      udpRequest.setMsgType(SwitcherUdpMsgType.SWITCHER_SETIP);
      SwitcherDevicesBody body = new SwitcherDevicesBody();
      body.setDevices(switcherDevices);
      udpRequest.setBody(body);
      String data = JsonUtils.encode(udpRequest);
      udpBroadcaster(data, rec -> {
        SwitcherUdpBody<SwitcherDevice> dev =
            JsonUtils.decode(rec, new TypeReference<>() {
            });
        if (dev != null && dev.getMsgId().equals(msgId) && dev.getBody() != null) {
          devices.add(dev.getBody());
        }
      }, SO_TIMEOUT);
    }
    SwitcherDevicesBody responseBody = new SwitcherDevicesBody();
    responseBody.setDevices(devices);
    return Result.okStr(responseBody);
  }

  /**
   * 注册设备.
   */
  public String register(String msg) {
    MqRequest<RegisterRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    request.getBody().setServerPort(SERVER_PORT);
    List<SwitcherDevice> waitingToRegister = new ArrayList<>();
    for (SwitcherDevice device : request.getBody().getDevices()) {
      if (containsDevice(device.getSn(), device.getMac())) {
        SwitcherUdpBody<SwitcherDevice> dev =
            allSwitcherDeviceTable.get(device.getSn(), device.getMac());
        if (dev != null && dev.getBody() != null) {
          EnvDeviceVo deviceVo = envDeviceService.oneBySnAndMac(device.getSn(), device.getMac());
          if (deviceVo != null) {
            dev.getBody().setId(deviceVo.getId());
            waitingToRegister.add(dev.getBody());
          } else {
            String uuid = SkyLinkStringUtil.uuid();
            dev.getBody().setId(uuid);
            waitingToRegister.add(dev.getBody());
          }
        }
      } else {
        log.warn("Device not found: sn:{},mac:{}", device.getSn(), device.getMac());
      }
    }
    registerDevices(waitingToRegister, request.getBody().getServerIp());
    //更新设备注册状态
    for (SwitcherDevice device : request.getBody().getDevices()) {
      device.setRet(1);
      if (containsDevice(device.getSn(), device.getMac())) {
        SwitcherUdpBody<SwitcherDevice> dev =
            allSwitcherDeviceTable.get(device.getSn(), device.getMac());
        if (dev != null && dev.getBody() != null) {
          device.setRet(dev.getBody().getRet());
        }
      }
    }
    return Result.okStr(request.getBody().getDevices());
  }

  public void registerDevices(List<SwitcherDevice> devices, String host) {
    RegisterRequestBody registerRequestBody = new RegisterRequestBody();
    registerRequestBody.setServerIp(host);
    registerRequestBody.setServerPort(SERVER_PORT);
    registerRequestBody.setDevices(devices);
    SwitcherUdpBody<RegisterRequestBody> udpRequest = new SwitcherUdpBody<>();
    String msgId = SkyLinkStringUtil.uuid();
    udpRequest.setMsgId(msgId);
    udpRequest.setMsgType(SwitcherUdpMsgType.SWITCHER_REGISTER);
    udpRequest.setBody(registerRequestBody);
    String data = JsonUtils.encode(udpRequest);
    Table<String, String, RegistrationStatus> registerStatusDevice =
        HashBasedTable.create();
    udpBroadcaster(data, rec -> {
      SwitcherUdpBody<RegistrationStatus> dev =
          JsonUtils.decode(rec, new TypeReference<>() {
          });
      if (dev != null && dev.getMsgId().equals(msgId) && dev.getBody() != null) {
        registerStatusDevice.put(dev.getBody().getSn(), dev.getBody().getMac(), dev.getBody());
      }
    }, 4000);
    for (SwitcherDevice dev : devices) {
      dev.setRet(1);
      if (registerStatusDevice.contains(dev.getSn(), dev.getMac())) {
        RegistrationStatus status = registerStatusDevice.get(dev.getSn(), dev.getMac());
        if (status != null && Objects.equals(dev.getId(), status.getId())) {
          dev.setRet(status.getStatus());
        }
      } else {
        log.warn("Device register failed: sn:{},mac:{}", dev.getSn(), dev.getMac());
        dev.setRet(2);
      }
      if (dev.getRet() == 0) {
        saveSwitcherDevice(dev);
      }
    }
  }

  private boolean containsDevice(String sn, String mac) {
    if (Strings.isNullOrEmpty(sn) || Strings.isNullOrEmpty(mac)) {
      return false;
    }
    return allSwitcherDeviceTable.contains(sn, mac);
  }

  private void saveSwitcherDevice(SwitcherDevice switcherDevice) {
    if (switcherDevice != null) {
      EnvDevice envDevice = new EnvDevice();
      if (InspectSwitcherUtil.setDeviceModelFromSwitcherDeviceType(envDevice, switcherDevice)) {
        envDevice.setId(switcherDevice.getId());
        envDevice.setName(switcherDevice.getName());
        envDevice.setDeviceIp(switcherDevice.getIp());
        envDevice.setVersion(switcherDevice.getVersion());
        envDevice.setHardcode(switcherHardcode(switcherDevice.getSn(), switcherDevice.getMac()));
        List<Property> properties = new ArrayList<>();
        properties.add(new Property("mask", switcherDevice.getMask()));
        properties.add(new Property("sn", switcherDevice.getSn()));
        properties.add(new Property("gateway", switcherDevice.getGateway()));
        properties.add(new Property("mac", switcherDevice.getMac()));
        properties.add(new Property("device_type", switcherDevice.getDeviceType()));
        properties.add(new Property("device_model", switcherDevice.getDeviceModel()));
        properties.add(
                new Property("last_register_time", String.valueOf(System.currentTimeMillis())));
        properties.add(new Property("register_ack",
                switcherDevice.getRet() == 0 ? String.valueOf(true) : String.valueOf(false)));
        envDevice.setProperties(properties);
        List<Property> collectorProperties = new ArrayList<>();
        collectorProperties.add(new Property("group", "分组01"));
        if (Objects.equals(envDevice.getDeviceModel(), DeviceType.SWITCHER_TX.getDeviceTypeId())) {
          collectorProperties.add(new Property("room", "机房01"));
        } else if (Objects.equals(envDevice.getDeviceModel(), DeviceType.SWITCHER_RX.getDeviceTypeId())) {
          collectorProperties.add(new Property("room", "管制坐席01"));
        }
        envDevice.setCollectorProperties(collectorProperties);
        if (!envDeviceService.saveOrUpdate(envDevice)) {
          log.error("Save switcher device failed.");
        }
      }

    }
  }

  private String switcherHardcode(String sn, String mac) {
    return "SWITCHER" + "." + sn + "." + mac;
  }

  /**
   * 注销设备.
   */
  public String unregister(String msg) {
    MqRequest<SwitcherDevicesBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null || request.getBody() == null || request.getBody().getDevices() == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    for (SwitcherDevice device : request.getBody().getDevices()) {
      EnvDeviceVo envDeviceVo = envDeviceService.oneBySnAndMac(device.getSn(), device.getMac());
      if (envDeviceVo != null) {
        SwitcherRpcRequestBody<Void> rpcRequestBody = new SwitcherRpcRequestBody<>();
        rpcRequestBody.setSource(SwitcherRpcConstants.SERVER);
        String msgId = SkyLinkStringUtil.uuid();
        rpcRequestBody.setMsgId(msgId);
        device.setRet(envDeviceService.removeById(envDeviceVo.getId()) ? 0 : 1);
        iMqttSender.sendToMqtt(JsonUtils.encode(rpcRequestBody),
            SwitcherRpcConstants.extRpcRequest(envDeviceVo.getId(),
                SwitcherRpcConstants.RPC_UNREGISTER));
      }
    }
    SwitcherResponse<List<SwitcherDevice>> res = new SwitcherResponse<>();
    res.setResult(request.getBody().getDevices());
    res.setTimestamp(System.currentTimeMillis());
    return Result.okStr(res);
  }

  /**
   * 检查升级包.
   */
  public String checkUpgradePackage(String msg) {
    MqRequest<String> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    return Result.okStr(new SwitcherResponse<>());
  }

  /**
   * 设置位置.
   */
  public String setPosition(String msg) {
    MqRequest<PositionRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null || request.getBody() == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    EnvDevice envDevice = envDeviceService.getById(request.getBody().getId());
    if (envDevice == null) {
      return Result.failureStr("设备不存在", ResponseCode.EX_FAILURE_400);
    }
    Property groupProperty = Property.findFromArray("group", envDevice.getCollectorProperties());
    if (groupProperty != null) {
      groupProperty.setPropertyValue(request.getBody().getGroup());
    } else {
      envDevice.getCollectorProperties().add(new Property("group", request.getBody().getGroup()));
    }
    Property roomProperty = Property.findFromArray("room", envDevice.getCollectorProperties());
    if (roomProperty != null) {
      roomProperty.setPropertyValue(request.getBody().getRoom());
    } else {
      envDevice.getCollectorProperties().add(new Property("room", request.getBody().getRoom()));
    }
    if (envDeviceService.saveOrUpdate(envDevice)) {
      return Result.okStr();
    }
    return Result.failureStr("设置位置失败", ResponseCode.EX_FAILURE_400);
  }

  /**
   * 获取位置.
   */
  public String getPosition(String msg) {
    MqRequest<IdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    EnvDevice envDevice = envDeviceService.getById(request.getBody().getId());
    if (envDevice == null) {
      return Result.failureStr("设备不存在", ResponseCode.EX_FAILURE_400);
    }
    PositionRequestBody body = new PositionRequestBody();
    body.setId(envDevice.getId());
    if (envDevice.getCollectorProperties() != null) {
      body.setGroup(Property.findValueByKey(envDevice.getCollectorProperties(), "group", ""));
      body.setRoom(Property.findValueByKey(envDevice.getCollectorProperties(), "room", ""));
    }
    return Result.okStr(body);
  }

  @Override
  public String openVwPanels(String msg) {
    return Result.okStr(new SwitcherResponse<>());
  }
}
