package com.mediacomm.aircon.controller;

import com.mediacomm.aircon.domain.AirconDeviceStatus;
import com.mediacomm.aircon.domain.AirconLayoutData;
import com.mediacomm.aircon.domain.AirconPanelRect;
import com.mediacomm.aircon.domain.AirconRx;
import com.mediacomm.aircon.domain.AirconSeat;
import com.mediacomm.aircon.domain.AirconSeatPanels;
import com.mediacomm.aircon.domain.AirconServer;
import com.mediacomm.aircon.domain.AirconServerStatus;
import com.mediacomm.aircon.domain.AirconTx;
import com.mediacomm.aircon.domain.AirconUser;
import com.mediacomm.aircon.domain.AirconVideoPanels;
import com.mediacomm.aircon.domain.AirconVideoWall;
import com.mediacomm.aircon.domain.AirconVwScene;
import java.net.URI;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * AirconFeignClientApi.
 */
@FeignClient(name = "aircon-service", url = "http://localhost")
public interface AirconFeignClientApi {

  @PostMapping("/aircon/interface/reboot-server/{serverId}")
  void rebootServer(URI uri, @PathVariable("serverId") String serverId);

  @GetMapping("/aircon/interface/txlist")
  List<AirconTx> getTxList(URI uri);

  @GetMapping("/aircon/interface/rxlist")
  List<AirconRx> getRxList(URI uri);

  @GetMapping("/aircon/interface/video-walls")
  List<AirconVideoWall> getVideoWalls(URI uri);

  @GetMapping("/aircon/interface/seatlist")
  List<AirconSeat> getSeatList(URI uri);

  @GetMapping("/aircon/interface/server-list")
  List<AirconServer> getServerInfo(URI uri);

  @GetMapping("/aircon/interface/users")
  List<AirconUser> getUsers(URI uri);

  @GetMapping("/aircon/interface/video-wall/scenes")
  List<AirconVwScene> getVideoWallScenes(URI uri, @RequestParam("videoWallId") int videoWallId);

  @GetMapping("/aircon/interface/video-wall/panels")
  AirconVideoPanels getVideoWallPanels(URI uri, @RequestParam("videoWallId") int videoWallId);

  @PostMapping("/aircon/interface/video-wall/open-panel")
  AirconPanelRect openVideoWallPanel(URI uri, @RequestParam("videoWallId") int videoWallId,
                                     @RequestBody AirconPanelRect airconPanelRect);

  @PostMapping("/aircon/interface/video-wall/close-panel")
  void closeVideoWallPanel(URI uri, @RequestParam("videowallId") int videowallId,
                           @RequestBody AirconPanelRect airconPanelRect);

  @PostMapping("/aircon/interface/video-wall/close-all-panel")
  void closeVideoWallAllPanel(URI uri, @RequestParam("videoWallId") int videoWallId);

  @PostMapping("/aircon/interface/video-wall/open-panels")
  AirconVideoPanels openVideoWallPanels(URI uri, @RequestParam("videoWallId") int videoWallId,
                                      @RequestBody AirconVideoPanels airconVideoPanels);

  @PostMapping("/aircon/interface/video-wall/move-panel")
  AirconPanelRect moveVideoWallPanel(URI uri, @RequestParam("videoWallId") int videoWallId,
                                     @RequestBody AirconPanelRect airconPanelRect);

  @PostMapping("/aircon/interface/video-wall/swap-layer")
  void swapVideoWallLayer(URI uri, @RequestParam("videoWallId") int videoWallId,
                          @RequestBody Map<String, Integer> map);

  @PostMapping("/aircon/interface/video-wall/apply-scene")
  void applyVideoWallScene(URI uri, @RequestParam("videoWallId") int videoWallId,
                           @RequestBody String sceneId);

  @PostMapping("/aircon/interface/video-wall/apply-layout")
  void applyVideoWallLayout(URI uri, @RequestParam("videoWallId") int videoWallId,
                            @RequestBody AirconLayoutData airconLayoutData);

  @GetMapping("/aircon/interface/seat/panels")
  AirconSeatPanels getSeatPanels(URI uri, @RequestParam("decoderId") int decoderId,
                                 @RequestParam("channelId") int channelId);

  @GetMapping("/aircon/interface/status/servers")
  List<AirconServerStatus> getServersStatus(URI uri);

  @GetMapping("/aircon/interface/status/devices")
  List<AirconDeviceStatus> getDevicesStatus(URI uri);

  @PostMapping("/aircon/interface/seat/close-all-panel")
  void closeSeatAllPanel(URI uri, @RequestParam("decoderId") int decoderId,
                         @RequestParam("channelId") int channelId);

  @PostMapping("/aircon/interface/seat/panels")
  List<AirconPanelRect> openSeatPanels(URI uri, @RequestParam("decoderId") int decoderId,
                      @RequestParam("channelId") int channelId,
                      @RequestBody AirconSeatPanels airconVideoPanels);

  @PostMapping("/aircon/interface/seat/open-tx")
  void openTx(URI uri, @RequestParam("decoderId") int decoderId,
                      @RequestParam("channelId") int channelId,
                      @RequestBody Map<String, Integer> map);

  @PostMapping("/aircon/interface/seat/close-tx")
  void closeTx(URI uri, @RequestParam("decoderId") int decoderId,
                      @RequestBody Map<String, Integer> map);
}
