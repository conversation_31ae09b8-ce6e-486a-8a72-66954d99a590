package com.mediacomm.pojo;

import com.mediacomm.entity.vo.EnvDeviceVo;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * 切换器概要.
 */
@Data
public class SwitcherPreInfoDto {
  private Map<String, Collection<PreInfo>> preInfos = new HashMap<>();
  private int rxNum;
  private int txNum;
  private int rxLinkFailNum;
  private int txLinkFailNum;

  @Data
  public static class PreInfo {
    private EnvDeviceVo switcher;
    private boolean linkStatus; // true:离线，false:在线
    private String resolution; // 输入分辨率
  }
}
