package com.mediacomm.aircon.util;

import com.mediacomm.aircon.domain.AirconErrorCode;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.ResponseCode;
import org.apache.commons.lang3.StringUtils;

/**
 * .
 */
public class InspectAirconUtil {
  public static final String SNAPSHOT_SAVE_PATH = "/tmp/snapshot/aircon/";
  /**
   * 根据接口错误值返回指定错误码.
   *
   * @param response .
   * @return .
   */
  public static String getResultFromApiStatus(AirconErrorCode response) {
    return switch (response.getErrorCode()) {
      case 1 -> Result.failureStr(response.getFailureReason(), ResponseCode.UNKNOWN_ERROR_13006);
      case 1005, 400 -> Result.failureStr(response.getFailureReason(), ResponseCode.EX_FAILURE_400);
      case -3 -> Result.failureStr(response.getFailureReason(), ResponseCode.OUT_OF_LIMIT_13005);
      default -> Result.failureStr(response.getFailureReason(), ResponseCode.EX_FAILURE_500);
    };
  }

  public static String getSnapshotPath(String masterIp, KvmAsset tx) {
    int streamType = Integer.parseInt(Property
            .findValueByKey(tx.getCollectorProperties(), PropertyKeyConst.STREAM_TYPE, "1"));
    // http://**************/aircon/interface/tx-snapshot/26/1/1.jpg
    return String.format("http://%s:80/aircon/interface/tx-snapshot/%d/%d/file.jpg",
            masterIp, tx.getDeviceId(), streamType);
  }

  /**
   * 优先生成Tx的下载图url，当Tx Ip不存在时，则生成服务器的下载图url.
   *
   * @param masterIp 服务器Ip.
   * @param tx 云控编码器.
   * @return url.
   */
  public static String getSnapshotPathFromKvmAsset(String masterIp, KvmAsset tx) {
    if (StringUtils.isEmpty(tx.getDeviceIp())) {
      return getSnapshotPath(masterIp, tx);
    }
    int streamType = Integer.parseInt(Property
            .findValueByKey(tx.getCollectorProperties(), PropertyKeyConst.STREAM_TYPE, "1"));
    // http://************:8080/snapshot/0/1752486071574.jpg
    return String.format("http://%s:8080/snapshot/%d/file.jpg",
            tx.getDeviceIp(), streamType);
  }

  public static String getSnapshotSavePath(String hardCode) {
    return String.format(SNAPSHOT_SAVE_PATH + "%s.%s", hardCode, "jpg");
  }
}
