package com.mediacomm.caesar;

import com.google.common.collect.Lists;
import com.mediacomm.caesar.controller.CaesarFeignClientApi;
import com.mediacomm.caesar.controller.Vp7FeignClientApi;
import com.mediacomm.caesar.domain.CaesarConstants;
import com.mediacomm.caesar.domain.CaesarDeviceStatus;
import com.mediacomm.caesar.domain.CaesarServerStatus;
import com.mediacomm.caesar.domain.ConnectStatus;
import com.mediacomm.caesar.domain.PortStatus;
import com.mediacomm.caesar.domain.SlotStatus;
import com.mediacomm.caesar.domain.vp7.Vp7Status;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.ExtenderVideoLink;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.Signal;
import com.mediacomm.entity.dao.SignalValue;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.base.kvm.SignalCollector;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.system.variable.sysenum.SwitchMode;
import com.mediacomm.util.JsonUtils;
import feign.FeignException;
import io.micrometer.common.util.StringUtils;
import java.net.URI;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * CaesarSignalsCollector.
 */
@Slf4j
@Component
public class CaesarSignalsCollector extends SignalCollector {
  @Autowired
  private CaesarFeignClientApi cli;
  @Autowired
  private Vp7FeignClientApi vp7Cli;
  @Autowired
  private KvmMasterService kvmMasterService;
  @Autowired
  private KvmAssetService kvmAssetService;

  /**
   * 每5秒刷新一次凯撒主机状态.
   */
  @Scheduled(fixedDelay = 5000)
  @SchedulerLock(name = "caesarServer", lockAtLeastFor = "PT4S", lockAtMostFor = "PT4S")
  public void reloadCaesarServer() {
    Collection<KvmMaster> kvmMasters = kvmMasterService.allByDeviceModel(
        DeviceType.CAESAR.getDeviceTypeId());
    // 批量缓存信号量数值
    Map<String, Map<String, String>> deviceSignalValueInCache = new HashMap<>();
    for (KvmMaster kvmMaster : kvmMasters) {
      // 缓存信号量数值
      Map<String, String> masterSignalValue = new HashMap<>();
      // 缓存描述的信息
      Map<String, String> masterDescMap = new HashMap<>();
      URI uri = URI.create(
          String.format(CaesarConstants.URI_FORMAT, kvmMaster.getDeviceIp(), CaesarConstants.PORT));
      List<CaesarServerStatus> serverStatus = null;
      try {
        serverStatus = cli.getServerStatus(uri);
      } catch (FeignException e) {
        log.error("Failed to obtain the Caesar host status.", e);
      }
      String redisKey = RedisSignalKey.getDeviceStatusKey(DeviceType.CAESAR.getDeviceType(),
              kvmMaster.getMasterId());
      String redisDescKey = RedisSignalKey.getDeviceDecKey(DeviceType.CAESAR.getDeviceType(),
              kvmMaster.getMasterId());
      deviceSignalValueInCache.put(redisDescKey, masterDescMap);
      // link.status为false表示正常，true表示异常
      deviceSignalValueInCache.put(redisKey, masterSignalValue);
      if (serverStatus == null || serverStatus.isEmpty()) {
        addDeviceSignalValue(kvmMaster, RedisSignalKey.LINK_STATUS,
                new SignalValue(Boolean.TRUE), masterSignalValue);
        masterDescMap.put(RedisSignalKey.UPDATE_TIME, String.valueOf(System.currentTimeMillis()));
        continue;
      }
      addDeviceSignalValue(kvmMaster, RedisSignalKey.LINK_STATUS,
              new SignalValue(Boolean.FALSE), masterSignalValue);
      for (CaesarServerStatus status : serverStatus) {
        // power.status
        Signal power1StausSignal = getSignal(kvmMaster.getDeviceModel(), "powerStatus",
                RedisSignalKey.POWER_1_STATUS);
        Signal power2StausSignal = getSignal(kvmMaster.getDeviceModel(), "powerStatus",
                RedisSignalKey.POWER_2_STATUS);
        Signal power3StausSignal = getSignal(kvmMaster.getDeviceModel(), "powerStatus",
                RedisSignalKey.POWER_3_STATUS);
        Signal power4StausSignal = getSignal(kvmMaster.getDeviceModel(), "powerStatus",
                RedisSignalKey.POWER_4_STATUS);
        DeviceSignalValue power1SignalValue = setValue(power1StausSignal,
                new SignalValue(status.isPower1Status() ? 1 : 2), kvmMaster);
        DeviceSignalValue power2SignalValue = setValue(power2StausSignal,
                new SignalValue(status.isPower2Status() ? 1 : 2), kvmMaster);
        DeviceSignalValue power3SignalValue = setValue(power3StausSignal,
                new SignalValue(status.isPower3Status() ? 1 : 2), kvmMaster);
        DeviceSignalValue power4SignalValue = setValue(power4StausSignal,
                new SignalValue(status.isPower4Status() ? 1 : 2), kvmMaster);
        masterSignalValue.put(RedisSignalKey.POWER_1_STATUS, JsonUtils.encode(power1SignalValue));
        masterSignalValue.put(RedisSignalKey.POWER_2_STATUS, JsonUtils.encode(power2SignalValue));
        masterSignalValue.put(RedisSignalKey.POWER_3_STATUS, JsonUtils.encode(power3SignalValue));
        masterSignalValue.put(RedisSignalKey.POWER_4_STATUS, JsonUtils.encode(power4SignalValue));
        // cpu
        addDeviceSignalValue(kvmMaster, RedisSignalKey.CPU_RATE,
                new SignalValue(status.getCpuRate()), masterSignalValue);
        // men
        addDeviceSignalValue(kvmMaster, RedisSignalKey.MEM_RATE,
                new SignalValue(status.getMemRate()), masterSignalValue);
        // disk
        addDeviceSignalValue(kvmMaster, RedisSignalKey.DISK_RATE,
                new SignalValue(status.getDiskRate()), masterSignalValue);
        // temp
        addDeviceSignalValue(kvmMaster, RedisSignalKey.TEMPERATURE,
                new SignalValue(status.getTemperature()), masterSignalValue);
        // total

        masterDescMap.put(RedisSignalKey.TOTAL_TX_NUMBER,
                String.valueOf(status.getTotalTxNumber()));
        masterDescMap.put(RedisSignalKey.TOTAL_RX_NUMBER,
                String.valueOf(status.getTotalRxNumber()));
        masterDescMap.put(RedisSignalKey.ONLINE_TX_NUMBER,
                String.valueOf(status.getOnlineTxNumber()));
        masterDescMap.put(RedisSignalKey.ONLINE_RX_NUMBER,
                String.valueOf(status.getOnlineRxNumber()));
        // fan
        Signal fan1StausSignal = getSignal(kvmMaster.getDeviceModel(), "fanOnline",
                RedisSignalKey.FAN_1_ONLINE);
        Signal fan2StausSignal = getSignal(kvmMaster.getDeviceModel(), "fanOnline",
                RedisSignalKey.FAN_2_ONLINE);
        Signal fan3StausSignal = getSignal(kvmMaster.getDeviceModel(), "fanOnline",
                RedisSignalKey.FAN_3_ONLINE);
        DeviceSignalValue fan1SignalValue = setValue(fan1StausSignal,
                new SignalValue(!(status.getFan1OnlineStatus() == 1)), kvmMaster);
        DeviceSignalValue fan2SignalValue = setValue(fan2StausSignal,
                new SignalValue(!(status.getFan2OnlineStatus() == 1)), kvmMaster);
        DeviceSignalValue fan3SignalValue = setValue(fan3StausSignal,
                new SignalValue(!(status.getFan3OnlineStatus() == 1)), kvmMaster);
        masterSignalValue.put(RedisSignalKey.FAN_1_ONLINE, JsonUtils.encode(fan1SignalValue));
        masterSignalValue.put(RedisSignalKey.FAN_2_ONLINE, JsonUtils.encode(fan2SignalValue));
        masterSignalValue.put(RedisSignalKey.FAN_3_ONLINE, JsonUtils.encode(fan3SignalValue));
        Signal fan1SpeedSignal = getSignal(kvmMaster.getDeviceModel(), "fanSpeed",
                RedisSignalKey.FAN_1_SPEED);
        Signal fan2SpeedSignal = getSignal(kvmMaster.getDeviceModel(), "fanSpeed",
                RedisSignalKey.FAN_2_SPEED);
        Signal fan3SpeedSignal = getSignal(kvmMaster.getDeviceModel(), "fanSpeed",
                RedisSignalKey.FAN_3_SPEED);
        DeviceSignalValue fan1SpeedValue = setValue(fan1SpeedSignal,
                new SignalValue(status.getFan1Speed()), kvmMaster);
        DeviceSignalValue fan2SpeedValue = setValue(fan2SpeedSignal,
                new SignalValue(status.getFan2Speed()), kvmMaster);
        DeviceSignalValue fan3SpeedValue = setValue(fan3SpeedSignal,
                new SignalValue(status.getFan3Speed()), kvmMaster);
        masterSignalValue.put(RedisSignalKey.FAN_1_SPEED, JsonUtils.encode(fan1SpeedValue));
        masterSignalValue.put(RedisSignalKey.FAN_2_SPEED, JsonUtils.encode(fan2SpeedValue));
        masterSignalValue.put(RedisSignalKey.FAN_3_SPEED, JsonUtils.encode(fan3SpeedValue));
        // port
        List<List<PortStatus>> portStatusLists = Lists.partition(status.getPortStatusList(), 64);
        for (List<PortStatus> portStatusList : portStatusLists) {
          Map<String, String> portSignalValue = new HashMap<>();
          for (PortStatus portStatus : portStatusList) {
            String portStatusKey = RedisSignalKey.PORT_STATUS + "." + portStatus.getPortNumber();
            String portLineKey = RedisSignalKey.LINE_STATUS + "." + portStatus.getPortNumber();
            if (portStatus.getId() > 0) {
              Signal lineSignal = getSignal(kvmMaster.getDeviceModel(),
                      RedisSignalKey.LINE_STATUS, portLineKey);
              DeviceSignalValue lineSignalValue = setValue(lineSignal,
                      new SignalValue(!portStatus.isExtOnline()), kvmMaster);
              portSignalValue.put(portLineKey, JsonUtils.encode(lineSignalValue));
              // 光模块的状态
              Signal portStatusSignal = getSignal(kvmMaster.getDeviceModel(),
                      RedisSignalKey.PORT_STATUS, portStatusKey);
              DeviceSignalValue portStatusValue = setValue(portStatusSignal,
                      new SignalValue(!portStatus.isSfpOnline()), kvmMaster);
              portSignalValue.put(portStatusKey, JsonUtils.encode(portStatusValue));
            } else {
              redisUtil.hdel(redisKey, portStatusKey);
              redisUtil.hdel(redisKey, portLineKey);
            }
          }
          redisUtil.hset(redisKey, portSignalValue);
        }
        for (SlotStatus slotStatus : status.getSlotStatusList()) {
          String slotSignalKey = RedisSignalKey.SLOT_STATUS + "." + slotStatus.getSlotNumber();
          Signal slotSignal = getSignal(kvmMaster.getDeviceModel(),
                  RedisSignalKey.SLOT_STATUS, slotSignalKey);
          DeviceSignalValue slotSignalValue = setValue(slotSignal,
                  new SignalValue(!slotStatus.isSlotBoardOnline()), kvmMaster);
          masterSignalValue.put(slotSignalKey, JsonUtils.encode(slotSignalValue));
        }
      }
      masterDescMap.put(RedisSignalKey.UPDATE_TIME, String.valueOf(System.currentTimeMillis()));
    }
    redisUtil.batchHashSet(deviceSignalValueInCache);
    checkMasterSignalValue(kvmMasters, DeviceType.CAESAR);
  }

  /**
   * 每10秒刷新一次凯撒主机外设状态.
   */
  @Scheduled(fixedDelay = 10000)
  @SchedulerLock(name = "caesarExtend", lockAtLeastFor = "PT4S", lockAtMostFor = "PT4S")
  public void reloadExtendStatus() {
    Collection<KvmMaster> kvmMasters = kvmMasterService.allByDeviceModel(
        DeviceType.CAESAR.getDeviceTypeId());
    for (KvmMaster kvmMaster : kvmMasters) {
      URI uri = URI.create(
          String.format(CaesarConstants.URI_FORMAT, kvmMaster.getDeviceIp(), CaesarConstants.PORT));
      cli.getDeviceStatus(uri);
      List<CaesarDeviceStatus> deviceStatus;
      try {
        deviceStatus = cli.getDeviceStatus(uri);
      } catch (FeignException e) {
        log.error(e.getMessage(), e);
        continue;
      }
      // 批量缓存信号量数值
      Map<String, Map<String, String>> deviceSignalValueInCache = new HashMap<>();
      Collection<KvmAssetVo> assets = new ArrayList<>();
      for (CaesarDeviceStatus status : deviceStatus) {
        KvmAssetVo asset =
            kvmAssetService.oneByDeviceId(status.getDeviceId(), kvmMaster.getMasterId());
        if (asset != null) {
          // signal
          deviceSignalValueInCache.put(RedisSignalKey.getDeviceStatusKey(asset.getDeviceType(),
                  asset.getAssetId()), new HashMap<>());
          // desc
          deviceSignalValueInCache.put(RedisSignalKey.getDeviceDecKey(asset.getDeviceType(),
                  asset.getAssetId()), new HashMap<>());
          setExtendStatus(asset, status, deviceSignalValueInCache);
          if ("Tx".equals(status.getDeviceType())) {
            setTxStatus(asset, status, deviceSignalValueInCache);
          }
          assets.add(asset);
        }
      }
      // vp7状态
      Collection<KvmAssetVo> assetVos = kvmAssetService.allByDeviceModelId(
              DeviceType.CAESAR_VP7.getDeviceTypeId(), kvmMaster.getMasterId());
      if (!assetVos.isEmpty()) {
        for (KvmAssetVo vp7 : assetVos) {
          // signal
          String signalRedisKey = RedisSignalKey.getDeviceStatusKey(vp7.getDeviceType(), vp7.getAssetId());
          deviceSignalValueInCache.put(signalRedisKey, new HashMap<>());
          if (StringUtils.isEmpty(vp7.getDeviceIp())) {
            // link.status异常
            log.error("The deviceIp of the vp7 is empty.");
            addDeviceSignalValue(vp7, RedisSignalKey.LINK_STATUS,
                    new SignalValue(Boolean.TRUE), deviceSignalValueInCache.get(signalRedisKey));
            continue;
          }
          // desc
          String descRedisKey = RedisSignalKey.getDeviceDecKey(vp7.getDeviceType(),
                  vp7.getAssetId());
          deviceSignalValueInCache.put(descRedisKey, new HashMap<>());
          deviceSignalValueInCache.get(descRedisKey)
                  .put(RedisSignalKey.UPDATE_TIME, String.valueOf(System.currentTimeMillis()));
          Vp7Status vp7Status;
          try {
            vp7Status = vp7Cli.getStatus(URI.create(
                    String.format(CaesarConstants.URI_FORMAT, vp7.getDeviceIp(), CaesarConstants.PORT)));
          } catch (FeignException e) {
            log.error(e.getMessage(), e);
            // link.status异常
            addDeviceSignalValue(vp7, RedisSignalKey.LINK_STATUS,
                    new SignalValue(Boolean.TRUE), deviceSignalValueInCache.get(signalRedisKey));
            continue;
          }
          setVp7Status(vp7, vp7Status, deviceSignalValueInCache);
        }
      }
      redisUtil.batchHashSet(deviceSignalValueInCache);
      checkAssetSignalValue(assets);
    }
  }

  private void setTxStatus(KvmAssetVo asset, CaesarDeviceStatus status,
                           Map<String, Map<String, String>> deviceSignalValueInCache) {
    // edid.status
    String signalRedisKey = RedisSignalKey.getDeviceStatusKey(asset.getDeviceType(),
            asset.getAssetId());
    for (int i = 1; i <= status.getEdidValid().size(); i++) {
      addDeviceSignalValue(asset, RedisSignalKey.EDID_VALID,
              RedisSignalKey.buildNewSignalKey(RedisSignalKey.EDID_VALID, i),
              RedisSignalKey.buildNewSignalKey("edid通道", i),
              new SignalValue(!status.getEdidValid().get(i - 1)),
              deviceSignalValueInCache.get(signalRedisKey));
    }
  }

  private void setExtendStatus(KvmAssetVo asset, CaesarDeviceStatus status,
                               Map<String, Map<String, String>> deviceSignalValueInCache) {
    // link.status
    String signalRedisKey = RedisSignalKey.getDeviceStatusKey(asset.getDeviceType(),
            asset.getAssetId());
    addDeviceSignalValue(asset, RedisSignalKey.LINK_STATUS,
            new SignalValue(!status.isLinkStatus()), deviceSignalValueInCache.get(signalRedisKey));
    // videoLineStatus
    for (int i = 1; i <= status.getVideoLineStatus().size(); i++) {
      addDeviceSignalValue(asset, RedisSignalKey.VIDEO_LINE_STATUS,
              RedisSignalKey.buildNewSignalKey(RedisSignalKey.VIDEO_LINE_STATUS, i),
              RedisSignalKey.buildNewSignalKey("视频接口线状态", i),
              new SignalValue(status.getVideoLineStatus().get(i - 1) ? 1 : 2),
              deviceSignalValueInCache.get(signalRedisKey));
    }
    // desc
    String descRedisKey = RedisSignalKey.getDeviceDecKey(asset.getDeviceType(),
            asset.getAssetId());
    // link.port.num
    deviceSignalValueInCache.get(descRedisKey).put(RedisSignalKey.LINK_STATUS_1,
            String.valueOf(status.getLink1Port()));
    deviceSignalValueInCache.get(descRedisKey).put(RedisSignalKey.LINK_STATUS_2,
            String.valueOf(status.getLink2Port()));
    // connection status
    List<ExtenderVideoLink> otherSideAssetIds = new ArrayList<>();
    for (ConnectStatus connectStatus : status.getConnectStatusList()) {
      if (connectStatus.getConnectedId() > 0) {
        ExtenderVideoLink videoLink = new ExtenderVideoLink();
        if (SwitchMode.VIDEO_ONLY.getModelName()
                .equals(connectStatus.getConnectStatus())) {
          videoLink.setVideoOnly(true);
        } else if (SwitchMode.PRIVATE.getModelName()
                .equals(connectStatus.getConnectStatus())) {
          videoLink.setPrivateMode(true);
        }
        KvmAsset otherSideAsset = kvmAssetService
                .oneByDeviceId(connectStatus.getConnectedId(), asset.getMasterId());
        if (otherSideAsset != null) {
          videoLink.setAssetId(otherSideAsset.getAssetId());
          otherSideAssetIds.add(videoLink);
        }
      }
    }
    deviceSignalValueInCache.get(descRedisKey).put(RedisSignalKey.CONNECTION_STATUS,
            JsonUtils.encode(otherSideAssetIds));
  }

  private void setVp7Status(KvmAssetVo vp7, Vp7Status status, Map<String,
          Map<String, String>> deviceSignalValueInCache) {
    boolean updateFanNum =
            addNumProperty(vp7, PropertyKeyConst.FAN_NUMBER, status.getFanStatus().size());
    boolean updatePowerNum =
            addNumProperty(vp7, PropertyKeyConst.POWER_NUMBER, status.getPowerStatus().size());
    boolean updateLinkNum =
            addNumProperty(vp7, PropertyKeyConst.LINK_NUMBER, status.getLinkStatus().size());
    boolean updateVideoDpNum =
            addNumProperty(vp7, PropertyKeyConst.VIDEO_NUMBER, status.getVideoLineStatus().size());
    if (updateFanNum || updatePowerNum || updateLinkNum || updateVideoDpNum) {
      kvmAssetService.updateById(vp7);
    }
    String signalRedisKey = RedisSignalKey.getDeviceStatusKey(vp7.getDeviceType(),
            vp7.getAssetId());
    Map<String, String> signalValueMap = deviceSignalValueInCache.get(signalRedisKey);
    // link.status
    addDeviceSignalValue(vp7, RedisSignalKey.LINK_STATUS,
            new SignalValue(Boolean.FALSE), signalValueMap);
    // cpu
    addDeviceSignalValue(vp7, RedisSignalKey.CPU_RATE,
            new SignalValue(status.getCpuUsage()), signalValueMap);
    // men
    addDeviceSignalValue(vp7, RedisSignalKey.MEM_RATE,
            new SignalValue(status.getMemoryUsage()), signalValueMap);
    // disk
    addDeviceSignalValue(vp7, RedisSignalKey.DISK_RATE,
            new SignalValue(status.getDiskUsage()), signalValueMap);
    // temp
    addDeviceSignalValue(vp7, RedisSignalKey.TEMPERATURE,
            new SignalValue(status.getTemperature()), signalValueMap);
    // fanSpeed
    int fanCount = 1;
    for (Integer i : status.getFanStatus()) {
      String signalId = "fanSpeed" + "." + fanCount;
      Signal fanStausSignal = getSignal(vp7.getDeviceModel(), "fanSpeed",
              signalId);
      fanStausSignal.setSignalName("风扇转速" + "." + fanCount);
      DeviceSignalValue fanStatusSignalValue =
              setValue(fanStausSignal, new SignalValue(Double.valueOf(i)), vp7);
      signalValueMap.put(signalId, JsonUtils.encode(fanStatusSignalValue));
      fanCount++;
    }
    // power
    int powerCount = 1;
    for (Integer i : status.getPowerStatus()) {
      String signalId = "powerStatus" + "." + powerCount;
      Signal powerStausSignal = getSignal(vp7.getDeviceModel(), "powerStatus",
              signalId);
      powerStausSignal.setSignalName("电源状态" + "." + powerCount);
      DeviceSignalValue powerStatusSignalValue =
              setValue(powerStausSignal, new SignalValue(i), vp7);
      signalValueMap.put(signalId, JsonUtils.encode(powerStatusSignalValue));
      powerCount++;
    }
    // optical Status
    int opticalCount = 1;
    for (Integer i : status.getLinkStatus()) {
      String signalId = "opticalStatus" + "." + opticalCount;
      Signal opticalStausSignal = getSignal(vp7.getDeviceModel(), "opticalStatus",
              signalId);
      opticalStausSignal.setSignalName("光口状态" + "." + opticalCount);
      DeviceSignalValue opticalStatusSignalValue =
              setValue(opticalStausSignal, new SignalValue(i), vp7);
      signalValueMap.put(signalId, JsonUtils.encode(opticalStatusSignalValue));
      opticalCount++;
    }
    // videoLineStatus
    int videoLineCount = 1;
    for (Integer i : status.getVideoLineStatus()) {
      String signalId = "videoLineStatus" + "." + videoLineCount;
      Signal videoLineStausSignal = getSignal(vp7.getDeviceModel(), "videoLineStatus",
              signalId);
      videoLineStausSignal.setSignalName("视频口接线状态" + "." + videoLineCount);
      DeviceSignalValue videoLineStatusSignalValue = setValue(videoLineStausSignal,
              new SignalValue(i), vp7);
      signalValueMap.put(signalId, JsonUtils.encode(videoLineStatusSignalValue));
      videoLineCount++;
    }
  }
}
