package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.PeripheralUpgradePackage;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.PeripheralUpgradePackageMapper;
import com.mediacomm.system.variable.sysenum.DeviceType;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * 外设升级包服务.
 */
@Service
public class PeripheralUpgradePackageService extends
  SkyLinkServiceImpl<PeripheralUpgradePackageMapper, PeripheralUpgradePackage> {

  /**
   * 根据设备类型获取升级包列表.
   *
   * @param deviceType 设备类型
   * @return 升级包列表
   */
  public Collection<PeripheralUpgradePackage> listByDeviceType(DeviceType deviceType) {
    LambdaQueryWrapper<PeripheralUpgradePackage> queryWrapper =
        Wrappers.lambdaQuery(PeripheralUpgradePackage.class)
            .eq(PeripheralUpgradePackage::getDeviceType, deviceType)
            .orderByDesc(PeripheralUpgradePackage::getUploadTime);
    return list(queryWrapper);
  }

  /**
   * 根据MD5查询升级包.
   *
   * @param md5 MD5值
   * @return 升级包
   */
  public PeripheralUpgradePackage getByMd5(String md5) {
    LambdaQueryWrapper<PeripheralUpgradePackage> queryWrapper =
        Wrappers.lambdaQuery(PeripheralUpgradePackage.class)
            .eq(PeripheralUpgradePackage::getMd5, md5);
    return getOne(queryWrapper);
  }
}
