package com.mediacomm.caesar.preview;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.domain.RedundantModeEnum;
import com.mediacomm.caesar.domain.avgm.TxConnectRequest;
import com.mediacomm.caesar.domain.avgm.TxConnectedStatusResponse;
import com.mediacomm.caesar.preview.avgm.AvgmDeviceDataGetter;
import com.mediacomm.caesar.preview.avgm.AvgmPreviewStrategy;
import com.mediacomm.caesar.preview.device.FourScreenRxPreviewOperatorImpl;
import com.mediacomm.caesar.preview.device.PreviewDeviceOperator;
import com.mediacomm.caesar.preview.device.PreviewDeviceOperatorImpl;
import com.mediacomm.caesar.preview.device.R2P4FPreviewOperatorImpl;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.preview.strategy.PreviewStrategy;
import com.mediacomm.caesar.preview.strategy.PreviewStrategyFactory;
import com.mediacomm.caesar.preview.strategy.impl.DeviceDataGetterImpl;
import com.mediacomm.caesar.task.PollingPreviewRunner;
import com.mediacomm.caesar.task.PreviewThread;
import com.mediacomm.caesar.task.RealTimePreviewRunner;
import com.mediacomm.caesar.task.RefreshPreviewRunner;
import com.mediacomm.caesar.util.CaesarDbServiceUtil;
import com.mediacomm.caesar.util.InspectCaesarUtil;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.reqeust.body.Heartbeat;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.PreviewType;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.task.SkyLinkTaskPool;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Component
@Slf4j
public class PreviewManager {
  @Resource
  private CaesarDbServiceUtil caesarDbServiceUtil;
  @Resource
  private SkyLinkTaskPool taskPool;
  private boolean snapshotPreviewStart = false;
  // key:masterId
  private Map<String, PreviewStrategy> previewStrategyMap = Maps.newHashMap();
  private Map<String, List<PreviewThread>> previewThreadMap = Maps.newHashMap();
  private Map<String, AvgmPreviewStrategy> avgmPreviewStrategyMap = Maps.newHashMap();

  public void refreshSnapshotPreviewStatus(Set<String> caesarMasterIds) {
    // 启动预览
    long updateTime = getSnapshotStatusTime();
    boolean flag = getSnapshotStatusLock();
    if (updateTime == 0 && flag || System.currentTimeMillis() - updateTime > 16000) {
      snapshotPreviewStart = true;
    }
    if (snapshotPreviewStart) {
      if (!previewStrategyMap.isEmpty()) {
        updateSnapshotStatus(); // 更新预览状态时间，以表明在线
        for (String previewMasterId : previewStrategyMap.keySet()) {
          RedundantModeEnum mode = getRedundantMode(previewMasterId);
          if (!caesarMasterIds.contains(previewMasterId) || mode == RedundantModeEnum.BACKUP) {
            stopPreviewStrategy(previewMasterId);
          }
        }
      }
      for (String masterId : caesarMasterIds) {
        if (!previewStrategyMap.containsKey(masterId) && getRedundantMode(masterId) != RedundantModeEnum.BACKUP) {
          createPreviewStrategy(masterId);
          startPreviewThread(masterId);
        }
      }
    }

  }

  public void refreshStreamingResourceStatus(Set<String> caesarMasterIds) {
    synchronized (avgmPreviewStrategyMap) {
      if (!avgmPreviewStrategyMap.isEmpty()) {
        avgmPreviewStrategyMap.keySet().forEach(masterId -> {
          if (!caesarMasterIds.contains(masterId)) {
            avgmPreviewStrategyMap.remove(masterId).stop();
          }
        });
      }
    }
  }

  public void updatePreviewStatus(DeviceType deviceType, String masterId) {
    CaesarPreviewStrategyMode strategyMode = caesarDbServiceUtil.getStrategyMode(masterId);
    CaesarPreviewStrategyMode expectedMode = InspectCaesarUtil.getStrategyModeByDeviceType(deviceType);
    switch (deviceType) {
      case CAESAR_FOUR_SCREEN_RX -> {
        if (expectedMode.getPriority() >= strategyMode.getPriority() || existPreviewR2P4f(masterId).isEmpty()) {
          boolean existPreviewAddr = existPreviewFourScreenRx(masterId);
          PreviewStrategy previewStrategy = previewStrategyMap.get(masterId);
          if (previewStrategy != null) {
            synchronized (previewStrategyMap) {
              if (strategyMode == CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX) {
                if (existPreviewAddr) {
                  previewStrategy.resetChannels();
                } else {
                  changePreviewStrategy(masterId, CaesarPreviewStrategyMode.NONE);
                  stopPreviewStrategy(masterId); // 停止预览
                }
              } else if (existPreviewAddr) {
                changePreviewStrategy(masterId, CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX);
                stopPreviewStrategy(masterId);
              }
            }
          } else if (existPreviewAddr) {
            changePreviewStrategy(masterId, CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX);
          }
        }
      }
      case CAESAR_R1C8_VIDEO_WALL -> {
        // 整屏回显
        AvgmPreviewStrategy avgmPreviewStrategy = avgmPreviewStrategyMap.get(masterId);
        if (avgmPreviewStrategy != null) {
          avgmPreviewStrategy.resetChannel();
        }
        // 预览
        if (expectedMode.getPriority() >= strategyMode.getPriority()
                || (!existPreviewFourScreenRx(masterId) && existPreviewR2P4f(masterId).isEmpty())) {
          Collection<KvmVideoWall> allPreviewWalls =
                  caesarDbServiceUtil.getVideoWallService().allByMasterIdAndModelId(masterId,
                          DeviceType.CAESAR_R1C8_VIDEO_WALL.getDeviceTypeId());
          allPreviewWalls.removeIf(videoWall -> {
            PreviewType previewType = PreviewType.valueOf(Property.findValueByKey(videoWall.getCollectorProperties(),
                    PropertyKeyConst.PREVIEW_TYPE, String.valueOf(PreviewType.NONE)));
            return previewType != PreviewType.SNAPSHOT;
          });
          PreviewStrategy previewStrategy = previewStrategyMap.get(masterId);
          if (previewStrategy != null) {
            if (strategyMode == CaesarPreviewStrategyMode.PREVIEW_STRATEGY_VP6) {
              synchronized (previewStrategyMap) {
                if (CollectionUtil.isEmpty(allPreviewWalls)) {
                  changePreviewStrategy(masterId, CaesarPreviewStrategyMode.NONE);
                  stopPreviewStrategy(masterId); // 停止预览
                } else {
                  previewStrategy.resetChannels();
                }
              }
            } else if (CollectionUtil.isNotEmpty(allPreviewWalls)) {
              changePreviewStrategy(masterId, CaesarPreviewStrategyMode.PREVIEW_STRATEGY_VP6);
              stopPreviewStrategy(masterId); // 停止预览
            }
          } else if (CollectionUtil.isNotEmpty(allPreviewWalls)) {
            changePreviewStrategy(masterId, CaesarPreviewStrategyMode.PREVIEW_STRATEGY_VP6);
          }
        }

      }
      case CAESAR_R2P4F -> {
        Collection<KvmAssetVo> allPreviewRx = existPreviewR2P4f(masterId);
        PreviewStrategy previewStrategy = previewStrategyMap.get(masterId);
        synchronized (previewStrategyMap) {
          if (previewStrategy != null) {
            if (allPreviewRx.isEmpty()) {
              changePreviewStrategy(masterId, CaesarPreviewStrategyMode.NONE);
              stopPreviewStrategy(masterId); // 停止预览
            } else if (strategyMode == CaesarPreviewStrategyMode.PREVIEW_STRATEGY_R2P4F) {
              previewStrategy.resetChannels();
            } else {
              changePreviewStrategy(masterId, CaesarPreviewStrategyMode.PREVIEW_STRATEGY_R2P4F);
              stopPreviewStrategy(masterId);
            }
          } else if (!allPreviewRx.isEmpty()) {
            changePreviewStrategy(masterId, CaesarPreviewStrategyMode.PREVIEW_STRATEGY_R2P4F);
            stopPreviewStrategy(masterId);
          }
        }
      }
      default -> log.warn("Unknown preview device model!");
    }
  }

  private boolean existPreviewFourScreenRx(String masterId) {
    Collection<KvmAssetVo> allPreviewRx =
            caesarDbServiceUtil.getKvmAssetService()
                    .allByDeviceModelId(DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId(), masterId);
    boolean existPreviewAddr = false;
    for (KvmAssetVo rx : allPreviewRx) {
      String previewAddr =
              Property.findValueByKey(rx.getCollectorProperties(), PropertyKeyConst.PREVIEW_ADDRESS,
                      "");
      if (StringUtils.isNotEmpty(previewAddr)) {
        existPreviewAddr = true;
        break;
      }
    }
    return existPreviewAddr;
  }

  private Collection<KvmAssetVo> existPreviewR2P4f(String masterId) {
    return caesarDbServiceUtil.getKvmAssetService()
                    .allByDeviceModelId(DeviceType.CAESAR_R2P4F.getDeviceTypeId(), masterId);
  }

  private void changePreviewStrategy(String masterId, CaesarPreviewStrategyMode newMode) {
    log.info("Change preview strategy:{} {}", masterId, newMode);
    caesarDbServiceUtil.getRedisUtil().set(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId),
            newMode.name());
  }

  public void onGetSnapshot(String masterId, String txId) {
    PreviewStrategy strategy = previewStrategyMap.get(masterId);
    if (strategy != null) {
      strategy.onGetSnapshot(txId);
    }
  }

  public void addPreview(String masterId, ObjectIds objectIds) {
    PreviewStrategy strategy = previewStrategyMap.get(masterId);
    if (strategy != null) {
      strategy.addPreview(objectIds);
    }
  }

  public void onTxOnline(String masterId, String txId) {
    PreviewStrategy strategy = previewStrategyMap.get(masterId);
    if (strategy != null) {
      strategy.onTxOnline(txId);
    }
  }

  public void onTxOffline(String masterId, String txId) {
    PreviewStrategy strategy = previewStrategyMap.get(masterId);
    if (strategy != null) {
      strategy.onTxOffline(txId);
    }
  }

  public Collection<TxConnectedStatusResponse> onGetStreamingResource(KvmMaster kvmMaster,
                                                                      TxConnectRequest req) {
    synchronized (avgmPreviewStrategyMap) {
      if (!avgmPreviewStrategyMap.containsKey(kvmMaster.getMasterId())) {
        AvgmDeviceDataGetter deviceDataGetter = new AvgmDeviceDataGetter(caesarDbServiceUtil,
                taskPool, kvmMaster.getMasterId());
        AvgmPreviewStrategy previewStrategy = new AvgmPreviewStrategy(deviceDataGetter, kvmMaster);
        previewStrategy.resetChannel();
        avgmPreviewStrategyMap.put(kvmMaster.getMasterId(), previewStrategy);
      }
    }

    return avgmPreviewStrategyMap.get(kvmMaster.getMasterId()).connectReviewAsso(req);
  }

  public void onUpdateClient(String masterId, Heartbeat.ClientType clientType, String clientId) {
    switch (clientType) {
      case AVGM -> {
        AvgmPreviewStrategy strategy = avgmPreviewStrategyMap.get(masterId);
        if (strategy != null) {
          strategy.updateClient(clientId);
        }
      }
      default -> log.warn("Unknown client type {} from clientId {}.", clientType, clientId);
    }
  }
  /**
   * 更新预览锁状态.
   */
  private void updateSnapshotStatus() {
    caesarDbServiceUtil.getRedisUtil().set(RedisKey.KC_SC_SS, String.valueOf(System.currentTimeMillis()));
  }

  /**
   * 预览锁的值.
   *
   * @return .
   */
  private long getSnapshotStatusTime() {
    return caesarDbServiceUtil.getRedisUtil().getStr(RedisKey.KC_SC_SS).isPresent()
            ? Long.parseLong(caesarDbServiceUtil.getRedisUtil().getStr(RedisKey.KC_SC_SS).get()) : 0;
  }

  /**
   * 获取预览锁.
   *
   * @return .
   */
  private boolean getSnapshotStatusLock() {
    return caesarDbServiceUtil.getRedisUtil().lock(RedisKey.KC_SC_SL, String.valueOf(System.currentTimeMillis()));
  }

  /**
   * 读取主机的冗余模式.
   */
  private RedundantModeEnum getRedundantMode(String masterId) {
    return caesarDbServiceUtil.getRedisUtil().getStr(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS,
            masterId)).map(RedundantModeEnum::valueOf).orElse(RedundantModeEnum.ALONE);
  }

  private synchronized void createPreviewStrategy(String masterId) {
    CaesarPreviewStrategyMode strategyMode = caesarDbServiceUtil.getStrategyMode(masterId);
    DeviceDataGetter dataGetter = new DeviceDataGetterImpl(caesarDbServiceUtil, masterId);
    PreviewStrategy previewStrategy = null;
    switch (strategyMode) {
      case PREVIEW_STRATEGY_4CRX -> {
        previewStrategy = PreviewStrategyFactory.createPreviewStrategy(dataGetter, strategyMode);
        previewStrategyMap.put(masterId, previewStrategy);
        previewThreadMap.put(masterId, List.of(new PollingPreviewRunner(previewStrategy.getPreviewInfoOpt(), dataGetter,
                new FourScreenRxPreviewOperatorImpl(taskPool, dataGetter))));
      }
      case PREVIEW_STRATEGY_VP6 -> {
        PreviewDeviceOperator operator = new PreviewDeviceOperatorImpl(taskPool, dataGetter);
        previewStrategy = PreviewStrategyFactory.createPreviewStrategy(dataGetter, strategyMode);  // 创建预览策略
        previewStrategyMap.put(masterId, previewStrategy);
        previewThreadMap.put(masterId, List.of(
                new PollingPreviewRunner(previewStrategy.getPreviewInfoOpt(), dataGetter, operator),
                new RealTimePreviewRunner(previewStrategy.getPreviewInfoOpt(), dataGetter, operator),
                new RefreshPreviewRunner(previewStrategy, previewStrategy.getPreviewInfoOpt())));
      }
      case PREVIEW_STRATEGY_R2P4F -> {
        previewStrategy = PreviewStrategyFactory.createPreviewStrategy(dataGetter, strategyMode);
        previewStrategyMap.put(masterId, previewStrategy);
        previewThreadMap.put(masterId, List.of(new PollingPreviewRunner(previewStrategy.getPreviewInfoOpt(), dataGetter,
                new R2P4FPreviewOperatorImpl(taskPool, dataGetter, previewStrategy.getPreviewInfoOpt()))));
      }
      case NONE -> log.info("{} no preview device currently exists.", masterId);
      default -> log.error("The preview policy Undefined {}.", strategyMode);
    }
    if (previewStrategy != null) {
      previewStrategy.resetChannels();
    }
  }

  private synchronized void startPreviewThread(String masterId) {
    List<PreviewThread> threads = previewThreadMap.get(masterId);
    if (threads != null) {
      for (PreviewThread thread : threads) {
        taskPool.addTask(thread);
      }
    }
  }

  private synchronized void stopPreviewStrategy(String previewMasterId) {
    previewStrategyMap.remove(previewMasterId);
    List<PreviewThread> previewThreads = previewThreadMap.get(previewMasterId);
    if (previewThreads != null) {
      for (PreviewThread previewThread : previewThreads) {
        previewThread.stop();
      }
    }
  }
}
