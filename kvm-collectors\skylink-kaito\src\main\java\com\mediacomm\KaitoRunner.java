package com.mediacomm;

import com.mediacomm.controller.KaitoCmdController;
import com.mediacomm.entity.Result;
import com.mediacomm.system.variable.MessageType;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingKey;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.boot.CommandLineRunner;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Slf4j
@Component
public class KaitoRunner implements CommandLineRunner {

  @Resource
  private KaitoCmdController kaitoCmdController;

  @Override
  public void run(String... args) throws Exception {
    log.info("Kaito service running!");
  }

  @RabbitListener(queues = MessageType.KAITO02_KVM, concurrency = "5-10")
  public String receiver(@Payload String msg,
                         @Headers Map<String, Object> headers,
                         @Header(value = AmqpHeaders.RECEIVED_ROUTING_KEY) String routingKey) {
    return switch (routingKey) {
      case RoutingKey.KAITO_KVM_REFRESH_CONFIG -> kaitoCmdController.refreshConfig(msg);
      case RoutingKey.KAITO_KVM_GET_VW_PANELS -> kaitoCmdController.getVwPanels(msg);
      case RoutingKey.KAITO_KVM_OPEN_VW_PANEL -> kaitoCmdController.openVwPanel(msg);
      case RoutingKey.KAITO_KVM_OPEN_VW_PANELS -> kaitoCmdController.openVwPanels(msg);
      case RoutingKey.KAITO_KVM_CLOSE_VW_PANEL -> kaitoCmdController.closeVwPanel(msg);
      case RoutingKey.KAITO_KVM_CLOSE_ALL_VW_PANEL -> kaitoCmdController.closeAllVwPanels(msg);
      case RoutingKey.KAITO_KVM_MOVE_VW_PANEL -> kaitoCmdController.moveVwPanel(msg);
      case RoutingKey.KAITO_KVM_GET_TX_SNAPSHOT -> kaitoCmdController.getTxSnapshot(msg);
      case RoutingKey.KAITO_BANNER_GET -> kaitoCmdController.getOsdData(msg);
      case RoutingKey.KAITO_BANNER_ENABLE -> kaitoCmdController.setOsdData(msg);
      case RoutingKey.KAITO_SWAP_VW_PANEL_LAYER -> kaitoCmdController.swapVwPanelLayer(msg);
      default -> Result.failureStr("Kaito no such command", ResponseCode.EX_NOTFOUND_404);
    };
  }
}
