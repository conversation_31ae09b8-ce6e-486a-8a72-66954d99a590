package com.mediacomm.caesar.snapshot;

import static com.mediacomm.caesar.domain.CaesarPreviewStrategyMode.PREVIEW_STRATEGY_VP6;
import static com.mediacomm.system.variable.sysenum.PreviewType.SNAPSHOT;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.mediacomm.caesar.domain.CaesarConstants;
import com.mediacomm.caesar.domain.CaesarOnlineVideoWallDecoder;
import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.domain.CaesarVideoWall;
import com.mediacomm.caesar.preview.device.PreviewDeviceOperator;
import com.mediacomm.caesar.preview.device.PreviewDeviceOperatorImpl;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.preview.strategy.impl.DeviceDataGetterImpl;
import com.mediacomm.caesar.preview.strategy.impl.SimplePreviewStrategy;
import com.mediacomm.caesar.util.CaesarDbServiceUtil;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.system.exception.SkyLinkException;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.task.SkyLinkTaskPool;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.InputMismatchException;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PreviewTestHelper {

  static private Map<String, ReentrantLock> pathLock = new ConcurrentHashMap<>();
  static private String PREVIEW_CONTENT_SEPERATOR = ":";

  static String getPreviewUrl(Integer videoWallId, int seq) {
    return Paths
            .get(System.getProperty("java.io.tmpdir"), PreviewDeviceOperator.class.getSimpleName(),
                    videoWallId + "." + seq + ".txt")
            .toString();
  }

  static String getTxPath(String txId) {
    return Paths
            .get(System.getProperty("java.io.tmpdir"), PreviewDeviceOperator.class.getSimpleName(),
                    txId + ".txt").toString();
  }

  static void createPreviewDirectory() throws IOException {
    Path previewPath = Paths
            .get(System.getProperty("java.io.tmpdir"), PreviewDeviceOperator.class.getSimpleName());
    if (!Files.exists(previewPath)) {
      Files.createDirectories(previewPath);
    }
  }

  static void lockAtPath(String path) {
    ReentrantLock lock;
    synchronized (pathLock) {
      if (!pathLock.containsKey(path)) {
        pathLock.put(path, new ReentrantLock());
      }
      lock = pathLock.get(path);
    }
    lock.lock();
  }

  static void unlockAtPath(String path) {
    ReentrantLock lock = pathLock.get(path);
    if (lock != null) {
      lock.unlock();
    }
  }

  static void deletePath(Path path) throws IOException {
    if (Files.isDirectory(path)) {
      Files.list(path).forEach(item -> {
        try {
          deletePath(item);
        } catch (IOException e) {
          e.printStackTrace();
        }
      });
      Files.deleteIfExists(path);
    } else {
      Files.deleteIfExists(path);
    }
  }

  static void deletePreviewDirectory() throws IOException {
    Path previewPath = Paths
            .get(System.getProperty("java.io.tmpdir"), PreviewDeviceOperator.class.getSimpleName());
    deletePath(previewPath);
  }

  static void checkTxPreview(Collection<String> txes, boolean checkTime) throws IOException {
    checkTxPreview(txes, checkTime, PreviewTestHelper::getTxPath,
            SimplePreviewStrategy.PREVIEW_TIME_OUT_MILLI);
  }

  static void checkTxPreview(Collection<String> txes, boolean checkTime,
                             Function<String, String> getTxPathCallBack, int timeOutTime) throws IOException {
    log.info("Check tx preview " + txes);
    for (String tx : txes) {
      String path = getTxPathCallBack.apply(tx);
      lockAtPath(path);
      try {
        FileReader reader = new FileReader(path);
        BufferedReader bufferedReader = new BufferedReader(reader);
        String msg = bufferedReader.readLine();
        bufferedReader.close();
        reader.close();
        if (!msg.startsWith(tx)) {
          System.out
                  .println(String.format("check tx fail! %s preview content is error:%s", tx, msg));
          throw new RuntimeException();
        }
        if (checkTime) {
          File file = new File(path);
          long time = file.lastModified();
          if (new Date().getTime() - time > timeOutTime) {
            System.out.println(String.format("check tx fail! %s preview not update time : %dms", tx,
                    System.currentTimeMillis() - time));
            throw new RuntimeException();
          }
        }
      } finally {
        unlockAtPath(path);
      }
    }
  }

  static <T> Collection<T> getRandomElements(Collection<T> input, int count) {
    List<T> list = new ArrayList<>(input);
    List<T> output = new ArrayList<>();
    Random random = new Random();
    for (int i = 0; i < count; i++) {
      int index = random.nextInt(list.size());
      output.add(list.get(index));
      list.remove(index);
    }
    return output;
  }

  static <T, U> Map<T, U> makeMap(Collection<T> key, Collection<U> value) {
    if (key.size() != value.size()) {
      throw new InputMismatchException();
    }
    List<U> valueList = new ArrayList<>(value);
    return key.stream()
            .collect(Collectors.toMap((e) -> e, (e) -> {
              U item = valueList.get(0);
              valueList.remove(0);
              return item;
            }));
  }

  static class TaskRunnerMock extends SkyLinkTaskPool {
    private final ThreadPoolExecutor executor =
            new ThreadPoolExecutor(10, 10, 0L,
                    TimeUnit.MINUTES, new ArrayBlockingQueue<>(50),
                    new ThreadFactoryBuilder().setNameFormat("caesar-test-snapshot-thread-%s").setDaemon(true).build());

    @Override
    public <T> CompletableFuture<T> addAsyncTask(Supplier<T> task) {
      return CompletableFuture.supplyAsync(task, executor);
    }

    @Override
    public void addTask(Runnable task) {
      CompletableFuture.runAsync(task, executor);
    }
  }


  static class CaesarMocker {

    private DeviceDataGetter deviceDataGetter;
    //        videoWallId, seq,  占用通道数(2k占用1个，4k占用2个)
    private Map<Integer, Map<Integer, Integer>> videoWallOccupiedChannels = new HashMap<>();

    CaesarMocker(DeviceDataGetter deviceDataGetter) {
      this.deviceDataGetter = deviceDataGetter;
    }

    void openPanelAtDevice(Integer videoWallId, Map<String, Integer> txChannelMap,
                           Collection<Integer> unusedChannels, long sleepTime)
            throws SkyLinkException, InterruptedException {
      Map<Integer, Integer> occupiedChannels;
      if (videoWallOccupiedChannels.containsKey(videoWallId)) {
        occupiedChannels = new HashMap<>(videoWallOccupiedChannels.get(videoWallId));
      } else {
        occupiedChannels = new HashMap<>();
      }

      occupiedChannels.putAll(txChannelMap.entrySet().stream().collect(Collectors
              .toMap((e) -> e.getValue(), (e) -> deviceDataGetter.is4kTx(e.getKey()) ? 2 : 1)));
      for (Integer value : unusedChannels) {
        occupiedChannels.remove(value);
      }
      int channelSize = occupiedChannels.values().stream().reduce(0, (l, r) -> l + r);
      if (channelSize > CaesarConstants.PREVIEW_CHANNEL_LIMIT) {
        throw new SkyLinkException("open panel error");
      }
      videoWallOccupiedChannels.put(videoWallId, occupiedChannels);

      Thread.sleep(sleepTime);
      for (Map.Entry<String, Integer> entry : txChannelMap.entrySet()) {
        String path = getPreviewUrl(videoWallId, entry.getValue());
        lockAtPath(path);
        try {
          File file = new File(path);
          file.getParentFile().mkdirs();
          FileWriter fos = new FileWriter(path);
          BufferedWriter writer = new BufferedWriter(fos);
          // 写入TX名称与更新时间
          writer.write(String.format("%s%s%d", entry.getKey(), PREVIEW_CONTENT_SEPERATOR,
                  System.currentTimeMillis()));
          writer.close();
          fos.close();
        } catch (Exception e) {
          throw new SkyLinkException(e);
        } finally {
          unlockAtPath(path);
        }
      }

      for (Integer channel : unusedChannels) {
        String path = getPreviewUrl(videoWallId, channel);
        lockAtPath(path);
        try {
          File file = new File(path);
          file.getParentFile().mkdirs();
          FileWriter fos = new FileWriter(path);
          BufferedWriter writer = new BufferedWriter(fos);
          // 写入TX名称与更新时间
          writer.write(String.format("%04d%s%d", 0, PREVIEW_CONTENT_SEPERATOR,
                  System.currentTimeMillis()));
          writer.close();
          fos.close();
        } catch (Exception e) {
          throw new SkyLinkException(e);
        } finally {
          unlockAtPath(path);
        }
      }
    }

    boolean getPreviewFromDevice(String url, String savePath) {
      lockAtPath(url);
      lockAtPath(savePath);
      try {
        File file = new File(savePath);
        file.getParentFile().mkdirs();

        FileInputStream fis = new FileInputStream(url);
        FileOutputStream fos = new FileOutputStream(savePath);
        fos.getChannel().transferFrom(fis.getChannel(), 0, fis.getChannel().size());
        fos.close();
        fis.close();
      } catch (IOException e) {
        e.printStackTrace();
        return false;
      } finally {
        unlockAtPath(savePath);
        unlockAtPath(url);
      }
      return true;
    }

    Map<String, Boolean> getPreviewFromDevice(Map<String, String> urlSavePathMap) {
      return urlSavePathMap.entrySet().stream().collect(Collectors.toMap((entry) -> entry.getKey(),
              (entry) -> getPreviewFromDevice(entry.getKey(), entry.getValue())));
    }
  }

  static class PreviewDeviceOperatorMock extends PreviewDeviceOperatorImpl {

    CaesarMocker caesarMocker;
    double errorPercentage; // 出错概率
    Random random = new Random();

    public PreviewDeviceOperatorMock(TaskRunnerMock taskRunner,
                                     DeviceDataGetter dataGetter) {
      this(taskRunner, dataGetter, 0);
    }

    public PreviewDeviceOperatorMock(TaskRunnerMock taskRunner,
                                     DeviceDataGetter dataGetter, double errorPercentage) {
      super(taskRunner, dataGetter);
      caesarMocker = new CaesarMocker(dataGetter);
      this.errorPercentage = errorPercentage;
    }

    @Override
    protected Map<String, Boolean> getPreviewFromDevice(Map<String, String> urlSavePathMap) {
      if (isRandomError()) {
        return urlSavePathMap.keySet().stream().collect(Collectors.toMap((e) -> e, (e) -> false));
      } else {
        return caesarMocker.getPreviewFromDevice(urlSavePathMap);
      }
    }

    @Override
    protected void openPanelAtDevice(Integer videoWallId, Map<String, Integer> txChannelMap,
                                     Collection<Integer> unusedChannels)
            throws SkyLinkException, InterruptedException {
      if (isRandomError()) {
        throw new SkyLinkException("Random error!");
      } else {
        caesarMocker
                .openPanelAtDevice(videoWallId, txChannelMap, unusedChannels, OPEN_PANEL_DELAY_MILLI);
      }
    }

    private boolean isRandomError() {
      return random.nextInt(100) < errorPercentage * 100;
    }
  }

  static class KvmVideoWallServiceMock extends KvmVideoWallService {
    // key -> uniqueSearchKey
    private Map<String, KvmVideoWall> wallMap = new HashMap<>();

    KvmVideoWallServiceMock() {
    }

    @Override
    public boolean saveOrUpdate(KvmVideoWall wall) {
      wallMap.put(wall.getUniqueSearchKey(), wall);
      return true;
    }

    @Override
    public KvmVideoWall oneByUniqueSearchKey(String uniqueSearchKey) {
      return wallMap.get(uniqueSearchKey);
    }

    @Override
    public KvmVideoWall getById(Serializable id) {
      for (KvmVideoWall value : wallMap.values()) {
        if (value.getWallId().equals(id)) {
          return value;
        }
      }
      return null;
    }
  }

  static class CaesarRunnerForeignMock extends CaesarDbServiceUtil {

    public Map<String, KvmAsset> idKvmAssetMap = new HashMap<>();
    public Map<String, KvmAsset> hardcodeKvmAssetMap = new HashMap<>();
    public KvmMaster master;
    public Map<Integer, KvmVideoWall> videoWallMap = new HashMap<>();
    public Map<Integer, Collection<KvmPreviewAsso>> reviewAssoMap = new HashMap<>();
    private Map<Integer, CaesarVideoWall> onlineVideoWallMap = new HashMap<>();
    @Getter
    private KvmVideoWallService videoWallService = new KvmVideoWallServiceMock();
    @Getter
    private KvmAssetService kvmAssetService;
    @Setter
    @Getter
    private RedisUtil redisUtil;

    public CaesarRunnerForeignMock(int reviewVideoWallCnt, int txCnt, String masterId) {
      master = new KvmMaster();
      master.setMasterId(masterId);
      kvmAssetService = new KvmAssetServiceMock(idKvmAssetMap);
      for (int i = 0; i < txCnt; i++) {
        KvmAsset asset = new KvmAsset();
        asset.setAssetId(UUID.randomUUID().toString());
        asset.setHardcode("hardcode." + i);
        if (i < txCnt / 2) {
          asset.getProperties()
                  .add(new Property("resolution", "2K"));
        } else {
          asset.getProperties()
                  .add(new Property("resolution", "4K"));
        }
        asset.setDeviceId(i);
        asset.setMasterId(masterId);
        idKvmAssetMap.put(asset.getAssetId(), asset);
        hardcodeKvmAssetMap.put(asset.getHardcode(), asset);
      }

      for (int i = 0; i < reviewVideoWallCnt; i++) {
        KvmVideoWall videoWall = new KvmVideoWall();
        videoWall.setWallId(i);
        videoWall.setDeviceId(i);
        videoWall.getCollectorProperties().add(
                new Property(PropertyKeyConst.PREVIEW_MODEL, String.valueOf(i)));
        videoWall.getCollectorProperties().add(
                new Property(PropertyKeyConst.PREVIEW_TYPE, String.valueOf(SNAPSHOT)));
        videoWall.getCollectorProperties().add(
                new Property(PropertyKeyConst.PREVIEW_ADDRESS, "********"));
        videoWall.setMasterId(masterId);
        videoWall.setDeviceModel(DeviceType.CAESAR_R1C8_VIDEO_WALL.getDeviceTypeId());
        videoWall.setUniqueSearchKey(String.join(".", masterId,
                DeviceType.CAESAR_VP6_VIDEO_WALL.getDeviceType(),
                String.valueOf(i)));
        videoWallMap.put(videoWall.getWallId(), videoWall);
        videoWallService.saveOrUpdate(videoWall);

        CaesarVideoWall onlineVideoWall = new CaesarVideoWall();
        onlineVideoWall.setId(i);
        List<CaesarOnlineVideoWallDecoder> decoders = new ArrayList<>();
        for (int j = 0; j < CaesarConstants.PREVIEW_CHANNEL_LIMIT; j++) {
          decoders.add(new CaesarOnlineVideoWallDecoder());
        }
        onlineVideoWall.setOnlinedevices(decoders);
        onlineVideoWallMap.put(videoWall.getWallId(), onlineVideoWall);

        List<KvmPreviewAsso> list = new ArrayList<>();
        for (int j = 0; j < CaesarConstants.PREVIEW_CHANNEL_LIMIT; j++) {
          KvmPreviewAsso asso = new KvmPreviewAsso();
          asso.setWallId(videoWall.getWallId());
          asso.setSeq(j + 1);
          asso.setUrl(getPreviewUrl(videoWall.getWallId(), asso.getSeq()));
          list.add(asso);
        }
        reviewAssoMap.put(videoWall.getWallId(), list);
      }
    }

    @Override
    public Collection<CaesarVideoWall> getOnlineVideoWall(String masterId) {
      return onlineVideoWallMap.values();
    }

    @Override
    public KvmVideoWallService getVideoWallService() {
      return videoWallService;
    }

    @Override
    public CaesarPreviewStrategyMode getStrategyMode(String masterId) {
      return PREVIEW_STRATEGY_VP6;
    }

    @Override
    public KvmAssetService getKvmAssetService() {
      return kvmAssetService;
    }

  }

  static class KvmAssetServiceMock extends KvmAssetService {
    Map<String, KvmAsset> assets;

    public KvmAssetServiceMock(Map<String, KvmAsset> assets) {
      this.assets = assets;
    }

    @Override
    public KvmAsset getById(Serializable id) {
      return assets.get(id);
    }
  }

  static class DeviceDataGetterImplMock extends DeviceDataGetterImpl {


    public DeviceDataGetterImplMock(CaesarDbServiceUtil cmdServer, String masterId,
                                    Map<Integer, Collection<KvmPreviewAsso>> kvmPreviewAssos) {
      super(cmdServer, masterId);
      this.kvmVideoWallPreviewAssos = kvmPreviewAssos;
    }

    @Override
    public Map<Integer, Collection<KvmPreviewAsso>> getAllPreviewChannels(
            boolean ignoreIncompleteVideoWall) {
      return kvmVideoWallPreviewAssos;
    }
  }
}
