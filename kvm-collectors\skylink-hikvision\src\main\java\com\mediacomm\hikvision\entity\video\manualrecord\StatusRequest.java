package com.mediacomm.hikvision.entity.video.manualrecord;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 *
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatusRequest {
  private String cameraIndexCode;
  @JsonProperty(value = "taskID")
  private String taskId;
  private Integer type;
}
