package com.mediacomm.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoOutput {
  private AudioInterface audioInterface;
  private int isSupportAudio;
  private int isUsed;
  private int outputId;
  private String name;


  @Data
  public static class AudioInterface {
    private int linkStatus;
    private int withAudioInterface;
  }
}
