package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.TinyIntToBooleanTypeHandler;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.SwitchMode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

/**
 * .
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "kvm_switch_log", description = "kvm切换日志")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KvmSwitchLog extends SkyLinkDbEntity {
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  private String masterId;
  private String con; // rx端名称
  private String cpu; // tx端名称
  private Long switchTime; // 切换时间
  private String fromUser; // 对应主机内用户Id
  private SwitchMode switchMode; // 切换模式
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private boolean disconnect; // 连接或断开
}
