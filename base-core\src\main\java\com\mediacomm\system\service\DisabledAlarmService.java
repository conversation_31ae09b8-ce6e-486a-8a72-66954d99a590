package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mediacomm.entity.dao.DisabledAlarm;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.DisabledAlarmMapper;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class DisabledAlarmService extends SkyLinkServiceImpl<DisabledAlarmMapper, DisabledAlarm> {
  public PageResult<DisabledAlarm> allByPage(Integer currentPage, Integer pageSize) {

    Page<DisabledAlarm> page = new Page<>(currentPage, pageSize);
    page(page);
    return new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal(),
        page.getPages(), page.getRecords());
  }

  public DisabledAlarm oneByMasterIdAndDeviceIdAndSignalId(String pId, String aId, String sId) {
    LambdaQueryWrapper<DisabledAlarm> queryWrapper = Wrappers.lambdaQuery(DisabledAlarm.class)
        .eq(DisabledAlarm::getMasterId, pId)
        .and(w -> w.eq(DisabledAlarm::getDeviceId, aId))
        .and(w -> w.eq(DisabledAlarm::getSignalId, sId));
    return getOne(queryWrapper);
  }

  /**
   * 获取主键的最大值.
   *
   * @return .
   */
  public Integer getMaxId() {
    return baseMapper.getMaxId() == null ? Integer.valueOf(0) : baseMapper.getMaxId();
  }
}
