package com.mediacomm.aircon.task;

import com.mediacomm.aircon.util.InspectAirconUtil;
import com.mediacomm.entity.dao.KvmAsset;
import lombok.extern.slf4j.Slf4j;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * 缓存云控需要预览的Tx图片.
 */
@Slf4j
public class ImageFetcherTask {
  private final String serverIp;
  private final KvmAsset tx;

  public ImageFetcherTask(KvmAsset tx, String serverIp) {
    this.serverIp = serverIp;
    this.tx = tx;
  }

  /**
   * 注意：这个方法现在声明会抛出异常，异常处理的责任上移给了调用者。
   *
   * @throws IOException 如果任何 I/O 操作失败
   */
  public void saveImage() throws IOException {
    Path tempFile = null;
    try {
      URL url = new URL(InspectAirconUtil.getSnapshotPathFromKvmAsset(serverIp, tx));
      Path parentDir = Paths.get(InspectAirconUtil.SNAPSHOT_SAVE_PATH, "temp");
      Files.createDirectories(parentDir);
      tempFile = Files.createTempFile(parentDir, "aircon-", ".tmp");
      try (InputStream in = url.openStream()) {
        log.debug("Start copying aircon files to a temporary path: {}", tempFile);
        Files.copy(in, tempFile, StandardCopyOption.REPLACE_EXISTING);
        log.debug("The file was copied successfully.");
      }
      Path finalPath = Paths.get(InspectAirconUtil.getSnapshotSavePath(tx.getHardcode()));
      log.debug("Start moving aircon files to the final path: from {} to {}", tempFile.toAbsolutePath(), finalPath);
      Path finalParentDir = finalPath.getParent();
      // spotbug此处不检查会null异常
      if (finalParentDir != null) {
        Files.createDirectories(finalParentDir);
      }
      Files.move(tempFile, finalPath, StandardCopyOption.ATOMIC_MOVE);
      log.debug("The file move was successfully.");
    } catch (IOException e) {
      // 如果发生任何错误，清理临时文件
      if (tempFile != null) {
        try {
          Files.deleteIfExists(tempFile);
        } catch (IOException cleanupEx) {
          e.addSuppressed(cleanupEx); // 将清理异常附加到主异常上
        }
      }
      // 将原始异常重新包装或直接抛出，让上层知道任务失败
      throw new IOException("Failed to save image for device " + tx.getHardcode(), e);
    }
  }
}
