package com.mediacomm.config;

import com.mediacomm.service.KaitoCmd;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Component
public class KaitoCmdHandler implements CommandLineRunner {
  @Resource
  AutowireCapableBeanFactory beanFactory;
  @Resource
  DefaultListableBeanFactory defaultListableBeanFactory;

  private Map<String, KaitoCmd> kaitoExtendModelCmd = new HashMap<>();

  @Override
  public void run(String... args) throws Exception {
    ClassPathScanningCandidateComponentProvider provider =
            new ClassPathScanningCandidateComponentProvider(false);
    provider.addIncludeFilter(new AssignableTypeFilter(KaitoCmd.class));
    Set<BeanDefinition> components =
            provider.findCandidateComponents(KaitoCmd.class.getPackageName());
    for (BeanDefinition component : components) {
      Class<?> cls = Class.forName(component.getBeanClassName());
      KaitoCmd cmd = (KaitoCmd) cls.getDeclaredConstructor().newInstance();
      String className = component.getBeanClassName();
      if (StringUtils.isNotEmpty(className)) {
        defaultListableBeanFactory.registerSingleton(className, cmd);
        beanFactory.autowireBean(cmd);
        kaitoExtendModelCmd.put(cmd.getExtendModel(), cmd);
      }
    }
  }

  public KaitoCmd getKaitoCmd(String extendModel) {
    return kaitoExtendModelCmd.get(extendModel);
  }
}
