package com.mediacomm.domain.h;

import com.mediacomm.domain.KaitoIpcDetail;
import java.util.Collection;
import lombok.Data;

/**
 * .
 */
@Data
public class KaitoHIpcDetail {
  private int channelId;
  private String channelName;
  private String createTime;
  private int decodeCapacity;
  private int slotId;
  private int sourceId;
  private String sourceName;
  private int status;
  private Collection<KaitoIpcDetail.Stream> streamList;
}
