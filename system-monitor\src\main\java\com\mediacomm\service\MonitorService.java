package com.mediacomm.service;

import com.mediacomm.entity.Result;
import com.mediacomm.event.EventManager;
import com.mediacomm.system.variable.MessageType;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingKey;
import jakarta.annotation.Resource;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Component
public class MonitorService {
  @Resource
  private EventManager eventManager;

  @RabbitListener(queues = MessageType.MONITOR, concurrency = "5-10")
  public String receiver(@Payload String msg,
                         @Headers Map<String, Object> headers,
                         @Header(value = AmqpHeaders.RECEIVED_ROUTING_KEY) String routingKey) {
    if (StringUtils.isBlank(msg)) {
      return Result.failureStr("Incorrect message format.", ResponseCode.EX_FAILURE_400);
    }
    return switch (routingKey) {
      case RoutingKey.MONITOR_DEVICE_GET_VALUE -> eventManager.getValue(msg);
      case RoutingKey.MONITOR_DEVICE_ALARM_BUILD -> eventManager.buildAlarm(msg);
      default ->
          Result.failureStr("MonitorService no such command", ResponseCode.EX_NOTFOUND_404);
    };
  }
}
