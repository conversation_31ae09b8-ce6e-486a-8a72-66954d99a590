package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.TinyIntToBooleanTypeHandler;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "kvm_user", description = "kvm主机内用户信息")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KvmUser extends SkyLinkDbEntity {
  @TableId(value = "user_id", type = IdType.AUTO)
  private Integer userId;
  private Integer indexInDevice = -1; // kvm主机内用户Id
  private String masterId; // 对应主机Id
  private String userName; // 用户名称
  private String userLevel; // 用户等级
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private boolean deleted = false; // 是否已删除
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private boolean active = true; // 是否已激活
}
