package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.ListKvmSeatDecoderJsonTypeHandler;
import com.mediacomm.config.db.TinyIntToBooleanTypeHandler;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "kvm_seat", description = "kvm坐席")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KvmSeat extends SkyLinkDbEntity {
  @Schema(hidden = true)
  @TableId(value = "seat_id", type = IdType.AUTO)
  private Integer seatId;
  private String masterId; // 所属主机id
  private Integer deviceId; // 对应接口中坐席的实际Id
  private String name;
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private boolean optModelEnable; // 是否启动操作模式
  private Integer rowCount; // 行数
  private Integer colCount; // 列数
  private Integer positionId; // 所属房间内坐席分组Id
  private Integer seqInPosition = 1; // 分组中序号
  @TableField(typeHandler = ListKvmSeatDecoderJsonTypeHandler.class)
  private List<KvmSeatDecoder> decoders; // 屏幕信息
}
