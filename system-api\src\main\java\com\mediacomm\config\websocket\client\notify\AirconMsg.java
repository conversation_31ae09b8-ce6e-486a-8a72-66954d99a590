package com.mediacomm.config.websocket.client.notify;


import com.mediacomm.aircon.domain.AirconSeatChange;
import com.mediacomm.config.websocket.client.notify.domain.VideoWallChange;
import com.mediacomm.util.websocket.client.NoticeTemplate;

/**
 * .
 */
public class AirconMsg {
  /**
   * 云控大屏窗口改变通知.
   */
  public static class AirconVideoWallChangeNotice extends NoticeTemplate<VideoWallChange> {}

  /**
   * 云控坐席窗口改变通知.
   */
  public static class AirconSeatChangeNotice extends NoticeTemplate<AirconSeatChange> {}
}
