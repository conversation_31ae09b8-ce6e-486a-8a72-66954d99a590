package com.mediacomm.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.entity.dao.Alarm;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.handler.websocket.WebSocketHandler;
import com.mediacomm.system.variable.MessageType;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.util.BuildWsEventNoticeBodyUtils;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.WebSocketTopic;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Component
@Slf4j
public class RabbitMqListener {
  @Autowired
  private WebSocketHandler handler;
  @RabbitListener(queues = MessageType.SERVICE_WEB, concurrency = "5-10")
  public void receiver(@Payload String msg,
                       @Headers Map<String, Object> headers,
                       @Header(value = AmqpHeaders.RECEIVED_ROUTING_KEY) String routingKey) {
    switch (routingKey) {
      case RoutingKey.WEB_SERVER_CHANGE_ALARM_STATUS -> publicAlarmChangeMsg(msg);
      default -> log.warn("Unknown routingKey in web-server rabbitListener:" + routingKey);
    }
  }

  public void publicAlarmChangeMsg(String msg) {
    MqRequest<Alarm> request = JsonUtils.decode(msg, new TypeReference<>() {});
    if (request == null || request.getBody() == null) {
      log.error(String.format("Error alarm-change msg:%s", msg));
      return;
    }
    handler.sendMessage(ResUrlDef.WS_BACKSTAGE_NOTICE,
        BuildWsEventNoticeBodyUtils.buildWsEventNoticeBody(WebSocketTopic.ALARM_CHANGE,
            request.getBody()));
  }
}
