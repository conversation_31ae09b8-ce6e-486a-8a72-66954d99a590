package com.mediacomm.caesar.controller;

import com.mediacomm.caesar.domain.CaesarMqRequest;
import com.mediacomm.caesar.domain.CaesarPanelRect;
import com.mediacomm.caesar.domain.CaesarResponse;
import com.mediacomm.caesar.domain.CaesarVideoPanels;
import com.mediacomm.caesar.util.mapper.CaesarEntityMapper;
import com.mediacomm.caesar.util.mapper.jpa.KvmAssetContext;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.util.JsonUtils;
import feign.FeignException;
import java.net.URI;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class CaesarOpenVwPanelTest {

    @InjectMocks
    private CaesarCmdServer caesarCmdServer; // 直接测试命令服务器的 openVwPanel 逻辑

    // 依赖模拟
    @Mock private CaesarFeignClientApi caesarFeignClientApi;
    @Mock private KvmMasterService kvmMasterService;
    @Mock private KvmAssetService kvmAssetService;
    @Mock private KvmVideoWallService videoWallService;
    @Mock private CaesarEntityMapper caesarEntityMapper;

    // 公共测试数据对象
    private KvmMaster master;
    private KvmVideoWall videoWall;
    private KvmAsset asset;
    private PanelRect panelRect;
    private CaesarPanelRect mappedCaesarPanelRect;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 基础模拟数据
        master = TestDataFactory.createTestKvmMaster();
        videoWall = TestDataFactory.createTestKvmVideoWall();
        asset = TestDataFactory.createTestKvmAsset();
        panelRect = TestDataFactory.createTestPanelRect();
        mappedCaesarPanelRect = TestDataFactory.createTestCaesarPanelRect();

        // 默认 Mock 行为（各测试可覆盖）
        when(kvmMasterService.getById(TestDataFactory.TEST_MASTER_ID)).thenReturn(master);
        when(videoWallService.getById(TestDataFactory.TEST_VIDEO_WALL_ID)).thenReturn(videoWall);
        when(kvmAssetService.getById(panelRect.getVideoSrcId())).thenReturn(asset);

        // PanelRect -> CaesarPanelRect 映射 & 反向映射
        when(caesarEntityMapper.fromPanelRect(any(PanelRect.class), any(KvmAssetContext.class)))
            .thenReturn(mappedCaesarPanelRect);
        when(caesarEntityMapper.toPanelRect(any(CaesarPanelRect.class), anyString()))
            .thenReturn(panelRect);

        // getVideoWallPanels 用于生成 panelId（内部获取现有窗口 ID 列表）
        when(caesarFeignClientApi.getVideoWallPanels(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID)))
            .thenReturn(new CaesarVideoPanels());
    }

    @Test
    void testOpenVwPanelSuccess() {
        CaesarMqRequest<PanelRectRequestBody> request = TestDataFactory.createTestCaesarMqRequest();
        String messageBody = JsonUtils.encode(request);

        // 成功响应
        CaesarResponse success = TestDataFactory.createSuccessfulCaesarResponse();
        when(caesarFeignClientApi.openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(CaesarPanelRect.class), eq(TestDataFactory.TEST_USER_ADDRESS), eq(TestDataFactory.TEST_USER_NAME)))
            .thenReturn(success);

        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.CAESAR_KVM_OPEN_VW_PANEL);
        assertNotNull(result);
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response);
        assertEquals(200, response.getCode());
        verify(caesarFeignClientApi, times(1)).openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(CaesarPanelRect.class), eq(TestDataFactory.TEST_USER_ADDRESS), eq(TestDataFactory.TEST_USER_NAME));
    }

    @Test
    void testOpenVwPanelCaesarFailure() {
        CaesarMqRequest<PanelRectRequestBody> request = TestDataFactory.createTestCaesarMqRequest();
        String messageBody = JsonUtils.encode(request);

        CaesarResponse failed = TestDataFactory.createFailedCaesarResponse();
        when(caesarFeignClientApi.openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(CaesarPanelRect.class), eq(TestDataFactory.TEST_USER_ADDRESS), eq(TestDataFactory.TEST_USER_NAME)))
            .thenReturn(failed);

        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.CAESAR_KVM_OPEN_VW_PANEL);
        assertNotNull(result);
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response);
        // 失败返回 500（与原集成测试期望一致）
        assertEquals(500, response.getCode());
    }

    @Test
    void testOpenVwPanelInvalidMasterId() {
        CaesarMqRequest<PanelRectRequestBody> request = TestDataFactory.createTestCaesarMqRequest();
        request.setMasterId("invalid-master-id");
        String messageBody = JsonUtils.encode(request);

        when(kvmMasterService.getById("invalid-master-id")).thenReturn(null);

        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.CAESAR_KVM_OPEN_VW_PANEL);
        assertNotNull(result);
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response);
        assertEquals(404, response.getCode());
        assertTrue(response.getMessage().contains(CaesarCmdServer.NO_HOST));
        verify(caesarFeignClientApi, times(0)).openVideoWallPanel(any(), anyInt(), any(), anyString(), anyString());
    }

    @Test
    void testOpenVwPanelInvalidMessageFormat() {
        String invalidMessage = "{invalid json}";
        String result = sendMessageAndWaitForResponse(invalidMessage, RoutingKey.CAESAR_KVM_OPEN_VW_PANEL);
        assertNotNull(result);
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertTrue(response.getMessage().contains(CaesarCmdServer.PARAM_ERR));
        verify(caesarFeignClientApi, times(0)).openVideoWallPanel(any(), anyInt(), any(), anyString(), anyString());
    }

    @Test
    void testOpenVwPanelNetworkError() {
        CaesarMqRequest<PanelRectRequestBody> request = TestDataFactory.createTestCaesarMqRequest();
        String messageBody = JsonUtils.encode(request);

        // 抛出 FeignException（继承 RuntimeException）模拟网络错误
        when(caesarFeignClientApi.openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(CaesarPanelRect.class), eq(TestDataFactory.TEST_USER_ADDRESS), eq(TestDataFactory.TEST_USER_NAME)))
            .thenThrow(mock(FeignException.class));

        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.CAESAR_KVM_OPEN_VW_PANEL);
        assertNotNull(result);
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response);
        assertEquals(500, response.getCode());
        verify(caesarFeignClientApi, times(1)).openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(CaesarPanelRect.class), eq(TestDataFactory.TEST_USER_ADDRESS), eq(TestDataFactory.TEST_USER_NAME));
    }
    
    private String sendMessageAndWaitForResponse(String messageBody, String routingKey) {
        return caesarCmdServer.openVwPanel(messageBody);
    }
}
