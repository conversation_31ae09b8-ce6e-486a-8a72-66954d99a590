package com.mediacomm.entity.message.reqeust.body;

import com.mediacomm.system.variable.sysenum.SnapshotType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SnapshotStatus.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SnapshotStatus {
  private String id;
  private String masterId;
  private float frameRate = 10;
  private SnapshotType type;
  private String path;
  private boolean linkStatus = false;
  private long modifyTime;
}
