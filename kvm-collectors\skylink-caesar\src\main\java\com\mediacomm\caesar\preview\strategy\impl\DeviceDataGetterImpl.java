package com.mediacomm.caesar.preview.strategy.impl;

import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.util.CaesarDbServiceUtil;
import com.mediacomm.caesar.util.InspectCaesarUtil;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.PreviewType;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class DeviceDataGetterImpl implements DeviceDataGetter {
  private CaesarDbServiceUtil cmdServer;
  private final String snapshotPath = "/tmp/snapshot/caesar/";
  private final String picType = "jpg";
  /**
   * key->videoWallId, value->KvmPreviewAsso.
   */
  protected Map<Integer, Collection<KvmPreviewAsso>> kvmVideoWallPreviewAssos = new HashMap<>();
    /**
   * key->assetId, value->KvmPreviewAsso.
   */
  protected Map<String, Collection<KvmPreviewAsso>> kvmAssetsPreviewAssos  = new HashMap<>();

  private final String masterId;

  public DeviceDataGetterImpl(CaesarDbServiceUtil cmdServer, String masterId) {
    this.cmdServer = cmdServer;
    this.masterId = masterId;
  }

  @Override
  public boolean is4kTx(String txId) {
    KvmAsset tx = getExtendDevice(txId);
    return tx != null && (InspectCaesarUtil.is4k(tx)
        || InspectCaesarUtil.isDoubleVideo(tx));
  }

  @Override
  public KvmPreviewAsso getAvailablePreviewChannel(boolean is4k,
                                                   boolean ignoreIncompleteVideoWall) {
    List<KvmVideoWall> previewOnlineWalls = getOnlinePreviewWalls(ignoreIncompleteVideoWall);
    KvmPreviewAsso previewAsso = null;
    for (KvmVideoWall previewOnlineWall : previewOnlineWalls) {
      Collection<KvmPreviewAsso> assos = kvmVideoWallPreviewAssos.get(previewOnlineWall.getWallId());
      int count = 0;
      for (KvmPreviewAsso asso : assos) {
        if (asso.isUsed() && asso.isHighResolution()) {
          count += 2;
          continue;
        } else if (asso.isUsed()) {
          count++;
          continue;
        }
        previewAsso = asso;
      }
      int onlineChannels = ignoreIncompleteVideoWall ? 8 : previewOnlineWall.getDecoders().size();
      int needCnt = is4k ? 2 : 1;
      if (onlineChannels - count >= needCnt) {
        return previewAsso;
      }
    }
    return null;
  }

  @Override
  public Map<Integer, Collection<KvmPreviewAsso>> getAllPreviewChannels(
      boolean ignoreIncompleteVideoWall) {
    kvmVideoWallPreviewAssos = new HashMap<>();
    List<KvmVideoWall> onlinePreviewWalls = getOnlinePreviewWalls(ignoreIncompleteVideoWall);
    for (KvmVideoWall onlinePreviewWall : onlinePreviewWalls) {
      Collection<KvmPreviewAsso> previewAssos =
              InspectCaesarUtil.buildPreviewChannelsByType(onlinePreviewWall, PreviewType.SNAPSHOT);
      if (!previewAssos.isEmpty()) {
        kvmVideoWallPreviewAssos.put(onlinePreviewWall.getWallId(), previewAssos);
        log.info("Adequate {} reviewWall:{} online devices:{}", ignoreIncompleteVideoWall,
                onlinePreviewWall.getWallId(), onlinePreviewWall.getDecoders());
      }
    }
    return kvmVideoWallPreviewAssos;
  }

  @Override
  public Map<String, Collection<KvmPreviewAsso>> getAllPreviewChannels(DeviceType previewDeviceType) {
    Collection<KvmAssetVo> assets = getOnlinePreviewAssets(previewDeviceType);
    // deviceIp去重
    Collection<KvmAssetVo> uniqueAssets = assets.stream().collect(Collectors.toMap(
            KvmAssetVo::getDeviceIp,
            Function.identity(),
            (existing, replacement) -> existing)).values();
    kvmAssetsPreviewAssos = InspectCaesarUtil.buildPreviewChannelsByType(uniqueAssets);
    log.info("{} preview channels: {}", previewDeviceType, kvmAssetsPreviewAssos);
    log.debug("All preview assets: {}, Final preview assets: {}", assets, uniqueAssets);
    return kvmAssetsPreviewAssos;
  }

  @Override
  public Collection<KvmPreviewAsso> getPreviewChannels(String assetId) {
    return kvmAssetsPreviewAssos.get(assetId);
  }

  @Override
  public Collection<KvmPreviewAsso> getPreviewChannels(Integer videoWallId) {
    return new HashSet<>(kvmVideoWallPreviewAssos.get(videoWallId));
  }

  @Override
  public KvmVideoWall getKvmPreviewAssoWallById(Integer videoWallId) {
    return cmdServer.getVideoWallService().getById(videoWallId);
  }

  @Override
  public KvmAsset getExtendDevice(String extendId) {
    return cmdServer.getKvmAssetService().getById(extendId);
  }

  @Override
  public String getMasterIp() {
    return cmdServer.getMasterService().getById(masterId).getDeviceIp();
  }

  @Override
  public String makeTxSavePath(String txId) {
    String hardCode = getExtendDevice(txId).getHardcode();
    return String.format("%s%s.%s", snapshotPath, hardCode, picType);
  }

  @Override
  public void addPreviewChannel(Integer previewVideoWallId) {
    KvmVideoWall wall = getKvmPreviewAssoWallById(previewVideoWallId);
    if (wall != null) {
      Collection<KvmPreviewAsso> channels = InspectCaesarUtil.buildPreviewChannelsByType(wall, PreviewType.SNAPSHOT);
      if (!channels.isEmpty()) {
        kvmVideoWallPreviewAssos.put(previewVideoWallId, channels);
      }
    }
  }

  @Override
  public void updatePreviewChannel(String assetId, int availableNum) {
    kvmAssetsPreviewAssos.remove(assetId);
    KvmAssetVo asset = cmdServer.getKvmAssetService().oneById(assetId);
    if (asset != null) {
        List<KvmPreviewAsso> channels = new ArrayList<>(InspectCaesarUtil.buildPreviewChannels(asset));
        if (!channels.isEmpty()) {
          channels.subList(availableNum, channels.size()).clear();
          kvmAssetsPreviewAssos.put(assetId, channels);
          log.info("Update preview channels: {}", kvmAssetsPreviewAssos);
        }
    }
  }

  @Override
  public void updatePreviewChannel(Integer previewVideoWallId) {
    delPreviewChannel(previewVideoWallId);
    addPreviewChannel(previewVideoWallId);
  }

  @Override
  public void delPreviewChannel(Integer previewVideoWallId) {
    kvmVideoWallPreviewAssos.remove(previewVideoWallId);
  }

  /**
   * 根据要求返回需要的凯撒电视墙数据.
   *
   * @param ignoreIncompleteVideoWall 是否不要不是全部光纤接入的电视墙.
   * @return 符合要求的预览电视墙.
   */
  private List<KvmVideoWall> getOnlinePreviewWalls(boolean ignoreIncompleteVideoWall) {
    return cmdServer.getOnlinePreviewWalls(ignoreIncompleteVideoWall, masterId, PreviewType.SNAPSHOT);
  }

  private Collection<KvmAssetVo> getOnlinePreviewAssets(DeviceType deviceType) {
    return cmdServer.getKvmAssetService().allByDeviceModelId(deviceType.getDeviceTypeId(), masterId);
  }
}
