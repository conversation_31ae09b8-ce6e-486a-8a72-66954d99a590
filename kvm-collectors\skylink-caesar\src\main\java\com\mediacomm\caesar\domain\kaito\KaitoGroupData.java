package com.mediacomm.caesar.domain.kaito;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoGroupData {
  private Integer id;
  private String name;
  private String ip; // 嗨动主机IP
  private Integer port; // 嗨动主机端口
  @JsonProperty("pId")
  private String pId; // 嗨动接入方ID
  private String secreteKey; // 嗨动接入方密钥
  private String type; // 嗨动主机类型，E或者ALPHA
}
