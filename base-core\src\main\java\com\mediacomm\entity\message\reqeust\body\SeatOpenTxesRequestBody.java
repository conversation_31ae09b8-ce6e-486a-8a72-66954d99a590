package com.mediacomm.entity.message.reqeust.body;

import com.mediacomm.entity.message.LayoutData;
import com.mediacomm.entity.message.PanelRect;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeatOpenTxesRequestBody implements Serializable {
  private int seatId;
  private int decoderId;
  private int channelId;
  private int width; // 对于云控来说是整个屏幕的实际宽
  private int height; // 对于云控来说是整个屏幕的实际高
  private LayoutData layout;
  private List<PanelRect> panels = new ArrayList<>();
}
