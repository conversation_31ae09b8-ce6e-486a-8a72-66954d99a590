package com.mediacomm.entity.dao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.base.Objects;
import lombok.Data;

/**
 * .
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class KvmPreviewAsso {
  private Integer wallId;
  private Integer seq;
  private String url;
  private boolean highResolution = false;
  private boolean isUsed = false;
  private String rxId; // 使用4画面Rx预览时需要的目标Rx Id

  public void reset() {
    this.highResolution = false;
    this.isUsed = false;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    KvmPreviewAsso that = (KvmPreviewAsso) o;
    return seq.equals(that.seq) && Objects.equal(wallId, that.wallId) && Objects
        .equal(url, that.url);
  }

  @Override
  public int hashCode() {
    return Objects.hashCode(wallId, seq, url);
  }

}

