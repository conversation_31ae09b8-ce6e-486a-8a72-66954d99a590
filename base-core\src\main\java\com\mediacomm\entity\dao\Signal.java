package com.mediacomm.entity.dao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Signal implements Serializable {
  private String signalId;
  private String signalName;
  private String signalGroup;
  private SignalType signalType;
  private boolean inputEnable;
  private boolean outputEnable;
  private String signalAssoKey;
  private String measurementUnit;
  private String normalDesc;
  private Double aiUpperLimit;
  private Double aiLowerLimit;
  private List<AiThreshold> aiThresholds = new ArrayList<>();
  private AiControl aiControl;
  private List<DiThreshold> diThresholds = new ArrayList<>();
  private List<DiControl> diControls = new ArrayList<>();
  private List<StatusThreshold> statusThresholds = new ArrayList<>();
  private List<StatusControl> statusControls = new ArrayList<>();
}
