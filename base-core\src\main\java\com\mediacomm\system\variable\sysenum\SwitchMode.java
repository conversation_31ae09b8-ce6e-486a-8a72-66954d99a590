package com.mediacomm.system.variable.sysenum;

import lombok.Getter;

/**
 * kvm主机外设连接模式.
 */
@Getter
public enum SwitchMode {
  STANDARD(0, "operation", "STANDARD", "操作模式"),
  VIDEO_ONLY(1, "video", "VIDEO_ONLY", "视频模式"),
  PRIVATE(2, "private", "PRIVATE", "私有模式"),
  CONTROL_ONLY(3, "", "CONTROL_ONLY", "只控模式"),
  UNKNOWN(4, "disconnect", "UNKNOWN", "");
  private Integer modelId;
  private String modelType;
  private String modelName;
  private String modelTypeTitle;

  SwitchMode(Integer modelId, String modelType, String modelName, String modelTypeTitle) {
    this.modelId = modelId;
    this.modelType = modelType;
    this.modelName = modelName;
    this.modelTypeTitle = modelTypeTitle;
  }

  public static SwitchMode from(Integer modelId) {
    return switch (modelId) {
      case 0 -> STANDARD;
      case 1 -> VIDEO_ONLY;
      case 2 -> PRIVATE;
      case 3 -> CONTROL_ONLY;
      default -> UNKNOWN;
    };
  }

  public static SwitchMode from(String modeType) {
    return switch (modeType) {
      case "operation" -> STANDARD;
      case "video" -> VIDEO_ONLY;
      case "private" -> PRIVATE;
      default -> UNKNOWN;
    };
  }
}
