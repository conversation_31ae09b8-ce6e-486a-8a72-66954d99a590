package com.mediacomm.util;

import com.mediacomm.util.websocket.client.NoticeTemplate;
import java.util.Map;

/**
 * .
 */
public class BuildWsEventNoticeBodyUtils {
  /**
   * 构建WebSocket事件通知体.
   *
   * @param eventType 事件类型.
   * @param data 事件数据.
   * @param <T> 数据类型.
   * @return 通知体的JSON字符串.
   */
  public static <T> String buildWsEventNoticeBody(String eventType, Map<String, T> data) {
      NoticeTemplate<Map<String, T>> notice = new NoticeTemplate<>();
      notice.setType(eventType);
      notice.setMessage(data);
      return JsonUtils.encode(notice);
  }

  /**
   * 构建WebSocket事件通知体.
   *
   * @param eventType 事件类型.
   * @param data 事件数据.
   * @param <T> 数据类型.
   * @return 通知体的JSON字符串.
   */
  public static <T> String buildWsEventNoticeBody(String eventType, T data) {
    NoticeTemplate<T> notice = new NoticeTemplate<>();
    notice.setType(eventType);
    notice.setMessage(data);
    return JsonUtils.encode(notice);
  }
}
