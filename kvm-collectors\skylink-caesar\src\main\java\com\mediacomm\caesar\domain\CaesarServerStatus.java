package com.mediacomm.caesar.domain;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * CaesarServerStatus.
 */
@Data
public class CaesarServerStatus {
  private int id;
  private String name;
  private boolean linkStatus;
  private boolean power1Status;
  private boolean power2Status;
  private boolean power3Status;
  private boolean power4Status;
  private Double cpuRate;
  private Double memRate;
  private Double diskRate;
  private Double temperature;
  private int fan1OnlineStatus = -1;
  private int fan2OnlineStatus = -1;
  private int fan3OnlineStatus = -1;
  private Double fan1Speed;
  private Double fan2Speed;
  private Double fan3Speed;
  private int totalRxNumber;
  private int totalTxNumber;
  private int onlineRxNumber;
  private int onlineTxNumber;
  private List<SlotStatus> slotStatusList = new ArrayList<>();
  private List<PortStatus> portStatusList = new ArrayList<>();
}
