package com.mediacomm.controller.rpc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.message.reqeust.body.CommandRequestBody;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.BuildMqRequestBodyUtils;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RpcSenderUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * TsingliRpc.
 */
@Tag(name = "远程清立调用方法")
@RestController
@RequestMapping(ResUrlDef.TSINGLI_RPC)
public class TsingliRpc extends SkyLinkController<KvmMaster, KvmMasterService> {

  @Autowired
  RpcSenderUtils senderUtils;

  @OperationLogRecord(title = "控制清立设备", operateType = OperateType.OTHER,
      requestBody = "#{requestBody}")
  @Operation(summary = "控制清立设备")
  @PostMapping("/exec-cmd")
  public Result<?> txRxConnect(@RequestParam("masterId") String masterId, @RequestBody CommandRequestBody requestBody) {
    return doCmd(masterId, RoutingOperation.EXECUTE_COMMAND, requestBody);
  }

  private <T> Result<T> doCmd(String masterId, String topicDto, T requestBody) {
    KvmMaster master = service.getById(masterId);
    if (master == null) {
      return Result.failure("No exist master!", ResponseCode.EX_FAILURE_400);
    }
    return JsonUtils.decode(senderUtils.send(master.getMasterId(), topicDto,
        BuildMqRequestBodyUtils.buildKvmMasterMqBody(master, requestBody)), new TypeReference<>() {
    });
  }
}
