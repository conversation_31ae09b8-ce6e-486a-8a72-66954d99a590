package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mediacomm.config.db.ListPropertyJsonTypeHandler;
import com.mediacomm.entity.dao.EnvDevice;
import com.mediacomm.entity.dao.NetLinkMerge;
import com.mediacomm.entity.vo.EnvDeviceVo;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import java.util.Collection;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface EnvDeviceMapper extends BaseMapper<EnvDevice> {
  @Select("select ed.*, dm.model_name, dm.device_type from env_device as ed "
      + "inner join device_model as dm on (ed.device_model = dm.model_id) "
      + "where dm.device_type = #{deviceType}")
  @Results(id = "envDeviceMap", value = {
      @Result(property = "properties", column = "properties",
          typeHandler = ListPropertyJsonTypeHandler.class),
      @Result(property = "collectorProperties", column = "collector_properties",
          typeHandler = ListPropertyJsonTypeHandler.class)
  })
  Collection<EnvDeviceVo> findALlByDeviceType(String deviceType);

  @Select("select ed.*, dm.model_name, dm.device_type from env_device as ed "
      + "inner join device_model as dm on (ed.device_model = dm.model_id) "
      + "where ed.device_model in "
      + "(select dm.model_id from device_model as dm where dm.sub_system = #{subSystemType})")
  @ResultMap(value = "envDeviceMap")
  Collection<EnvDeviceVo> findALlBySubSystemType(SubSystemType subSystemType);

  @Insert("insert into net_link_merge (net_id, asso_device_name, net_port)"
      + " VALUES (#{netId}, #{assoDeviceName}, #{netPort})")
  void insertNetLinkMerge(NetLinkMerge netLink);

  @Select("select * from net_link_merge where net_id = #{netId}")
  Collection<NetLinkMerge> finalAllNetLinkAssoByNetId(String netDeviceId);

  @Delete("delete from net_link_merge where net_id = #{netDeviceId} and net_port = #{portNum} ")
  void delNetLinkMerge(String netDeviceId, Integer portNum);
}
