package com.mediacomm.caesar.preview.avgm;

import com.mediacomm.caesar.util.CaesarDbServiceUtil;
import com.mediacomm.caesar.util.InspectCaesarUtil;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.variable.sysenum.PreviewType;
import com.mediacomm.util.task.SkyLinkTaskPool;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;

/**
 * .
 */
public class AvgmDeviceDataGetter {
  private CaesarDbServiceUtil cmdServer;
  private SkyLinkTaskPool taskPool;
  /**
   * key->videoWallId, value->KvmPreviewAsso.
   */
  private Map<Integer, Collection<KvmPreviewAsso>> kvmPreviewAssos;
  @Getter
  private Map<Integer, KvmVideoWall> kvmPreviewWalls;
  private final String masterId;

  public AvgmDeviceDataGetter(CaesarDbServiceUtil cmdServer, SkyLinkTaskPool taskPool, String masterId) {
    this.cmdServer = cmdServer;
    this.masterId = masterId;
    this.taskPool = taskPool;
  }

  public Map<Integer, Collection<KvmPreviewAsso>> getAllPreviewChannels() {
    kvmPreviewAssos = new HashMap<>();
    kvmPreviewWalls = new HashMap<>();
    List<KvmVideoWall> onlinePreviewWalls = cmdServer.getOnlinePreviewWalls(false, masterId,
            PreviewType.RTSP);
    for (KvmVideoWall onlinePreviewWall : onlinePreviewWalls) {
      Collection<KvmPreviewAsso> previewAssos =
              InspectCaesarUtil.buildPreviewChannelsByType(onlinePreviewWall, PreviewType.RTSP);
      if (!previewAssos.isEmpty()) {
        kvmPreviewAssos.put(onlinePreviewWall.getWallId(), previewAssos);
        kvmPreviewWalls.put(onlinePreviewWall.getWallId(), onlinePreviewWall);
      }
    }
    return kvmPreviewAssos;
  }

  public KvmPreviewAsso getAvailablePreviewChannel(boolean is4k) {
    List<KvmVideoWall> previewOnlineWalls = cmdServer.getOnlinePreviewWalls(false, masterId,
            PreviewType.RTSP);
    KvmPreviewAsso previewAsso = null;
    for (KvmVideoWall previewOnlineWall : previewOnlineWalls) {
      Collection<KvmPreviewAsso> assos = kvmPreviewAssos.get(previewOnlineWall.getWallId());
      int count = 0;
      for (KvmPreviewAsso asso : assos) {
        if (asso.isUsed() && asso.isHighResolution()) {
          count += 2;
          continue;
        } else if (asso.isUsed()) {
          count++;
          continue;
        }
        previewAsso = asso;
      }
      int onlineChannels = previewOnlineWall.getDecoders().size();
      int needCnt = is4k ? 2 : 1;
      if (onlineChannels - count >= needCnt) {
        return previewAsso;
      }
    }
    return null;
  }

  public KvmAssetVo getKvmAsset(String assetId) {
    return cmdServer.getKvmAssetService().oneById(assetId);
  }

  public void addThread(Runnable runnable) {
    taskPool.addTask(runnable);
  }
}
