package com.mediacomm.controller;

import com.mediacomm.domain.KaitoLayerDetail;
import com.mediacomm.domain.request.KaitoCreatePresetReq;
import com.mediacomm.domain.request.KaitoDeviceIdReq;
import com.mediacomm.domain.request.KaitoInputDetailReq;
import com.mediacomm.domain.request.KaitoIpcSourceChanelListReq;
import com.mediacomm.domain.request.KaitoLayerClearReq;
import com.mediacomm.domain.request.KaitoLayerDetailReq;
import com.mediacomm.domain.request.KaitoLayerLayoutReq;
import com.mediacomm.domain.request.KaitoLayerReq;
import com.mediacomm.domain.request.KaitoOutputDetailReq;
import com.mediacomm.domain.request.KaitoPresetDetailReq;
import com.mediacomm.domain.request.KaitoRequestBody;
import com.mediacomm.domain.request.KaitoResponseBody;
import com.mediacomm.domain.request.KaitoScreenOsdReq;
import com.mediacomm.domain.request.KaitoWriteSourceReq;
import com.mediacomm.domain.request.KaitoWriteWindowReq;
import com.mediacomm.domain.request.KaitoZOrderReq;
import java.net.URI;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * .
 */
@FeignClient(name = "kaito-service", url = "http://localhost")
public interface KaitoEFeignClientApi {

  /**
   * 获取初始状态.
   */
  @PostMapping("/open/main/initStatus")
  KaitoResponseBody getInitStatus(URI uri, @RequestBody KaitoRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 读取视频预监视频流信息.
   */
  @PostMapping("/open/main/readVideoServerInfo")
  KaitoResponseBody getVideoServerInfo(URI uri, @RequestBody KaitoRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 获取输入列表.
   */
  @PostMapping("/open/input/readList")
  KaitoResponseBody getInputReadList(URI uri, @RequestBody KaitoRequestBody<KaitoDeviceIdReq> requestBody);

  @PostMapping("open/input/readDetail")
  KaitoResponseBody getInputDetail(URI uri, @RequestBody KaitoRequestBody<KaitoInputDetailReq> requestBody);

  /**
   * 读取Ipc列表详情.
   */
  @PostMapping("/open/ipc/IPCSourceChannelList")
  KaitoResponseBody getIpcSourceChannelList(URI uri, @RequestBody KaitoRequestBody<KaitoIpcSourceChanelListReq>
          requestBody);
  /**
   * 获取输出列表.
   */
  @PostMapping("/open/output/readList")
  KaitoResponseBody getOutputReadList(URI uri, @RequestBody KaitoRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 获取输出详情.
   */
  @PostMapping("/open/output/readDetail")
  KaitoResponseBody getOutputReadDetail(URI uri, @RequestBody KaitoRequestBody<KaitoOutputDetailReq> requestBody);

  /**
   * 获取大屏列表.
   */
  @PostMapping("/open/screen/readList")
  KaitoResponseBody getScreenReadList(URI uri, @RequestBody KaitoRequestBody<KaitoDeviceIdReq> requestBody);
  @PostMapping("/open/screen/readDetail")
  KaitoResponseBody getScreenReadDetail(URI uri, @RequestBody KaitoRequestBody<KaitoLayerReq> requestBody);
  /**
   * 获取设备详情.
   */
  @PostMapping("/open/device/readDetail")
  KaitoResponseBody getDeviceDetail(URI uri, @RequestBody KaitoRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 获取指定 screen上的图层列表.
   */
  @PostMapping("/open/layer/detailList")
  KaitoResponseBody getLayerList(URI uri, @RequestBody KaitoRequestBody<KaitoLayerReq> requestBody);

  /**
   * 获取指定图层的详细信息.
   */
  @PostMapping("/open/layer/readDetail")
  KaitoResponseBody getLayerDetail(URI uri, @RequestBody KaitoRequestBody<KaitoLayerDetailReq> requestBody);

  /**
   * 删除图层.
   */
  @PostMapping("/open/layer/delete")
  KaitoResponseBody deleteLayer(URI uri, @RequestBody KaitoRequestBody<KaitoLayerDetailReq> requestBody);
  /**
   * 清除图层.
   */
  @PostMapping("/open/layer/clear")
  KaitoResponseBody clearLayer(URI uri, @RequestBody KaitoRequestBody<KaitoLayerClearReq> requestBody);
  @PostMapping("/open/layer/writeSource")
  KaitoResponseBody writeSource(URI uri, @RequestBody KaitoRequestBody<KaitoWriteSourceReq> requestBody);
  @PostMapping("/open/layer/writeWindow")
  KaitoResponseBody writeWindow(URI uri, @RequestBody KaitoRequestBody<KaitoWriteWindowReq> requestBody);
  @PostMapping("/open/layer/writeZIndex")
  KaitoResponseBody writeZIndex(URI uri, @RequestBody KaitoRequestBody<KaitoZOrderReq> requestBody);
  @PostMapping("/open/layer/create")
  KaitoResponseBody createLayer(URI uri, @RequestBody KaitoRequestBody<KaitoLayerDetail> requestBody);
  @PostMapping("/open/layer/screenLayerLayout")
  KaitoResponseBody createLayers(URI uri, @RequestBody KaitoRequestBody<KaitoLayerLayoutReq> requestBody);
  @PostMapping("/open/preset/createPreset")
  KaitoResponseBody createPreset(URI uri, @RequestBody KaitoRequestBody<KaitoCreatePresetReq> requestBody);
  @PostMapping("/open/preset/readList")
  KaitoResponseBody readPresetList(URI uri, @RequestBody KaitoRequestBody<KaitoLayerReq> requestBody);
  @PostMapping("/open/preset/readDetail")
  KaitoResponseBody readPresetDetail(URI uri, @RequestBody KaitoRequestBody<KaitoPresetDetailReq> requestBody);
  @PostMapping("/open/preset/play")
  KaitoResponseBody playPreset(URI uri, @RequestBody KaitoRequestBody<KaitoPresetDetailReq> requestBody);
  @PostMapping("/open/preset/deletePreset")
  KaitoResponseBody deletePreset(URI uri, @RequestBody KaitoRequestBody<KaitoPresetDetailReq> requestBody);
  @PostMapping("/open/screen/writeOSD")
  KaitoResponseBody writeOSD(URI uri, @RequestBody KaitoRequestBody<KaitoScreenOsdReq> requestBody);
}
