package com.mediacomm.protocol;

import com.mediacomm.domain.Operate;
import com.mediacomm.domain.OperateParam;
import com.mediacomm.domain.Ptz;
import com.mediacomm.system.exception.NetException;
import com.mediacomm.util.TcpClient;
import java.io.IOException;

/**
 * PelcoP协议.
 */
public class PelcoP implements Protocol {

  /**
   * 获取云台TCP连接客户端.
   *
   * @param ptz .
   * @return TcpClient.
   * @throws NetException .
   */
  private TcpClient getClient(Ptz ptz) throws IOException {
    return new TcpClient(ptz.getIp(), ptz.getPort());
  }

  private static final byte up = 0X08;
  private static final byte down = 0x10;
  private static final byte left = 0x04;
  private static final byte right = 0x02;
  private static final byte zoomOut = 0x20;
  private static final byte zoomIn = 0x40;
  private static final byte loadPreset = 0x07;
  private static final byte savePreset = 0x03;

  private boolean sendCmd(TcpClient client, int address, String operate, OperateParam param)
      throws IOException {
    byte[] data = new byte[8];
    data[0] = (byte) 0xA0;
    data[1] = (byte) address;
    byte speed = 0x2f;
    switch (operate) {
      case "left-up":
        data[2] = (byte) 0x00;
        data[3] = left | up;
        data[4] = speed;
        data[5] = speed;
        break;
      case "left-down":
        data[2] = (byte) 0x00;
        data[3] = left | down;
        data[4] = speed;
        data[5] = speed;
        break;
      case "right-up":
        data[2] = (byte) 0x00;
        data[3] = right | up;
        data[4] = speed;
        data[5] = speed;
        break;
      case "right-down":
        data[2] = (byte) 0x00;
        data[3] = right | down;
        data[4] = speed;
        data[5] = speed;
        break;
      case "up":
        data[2] = (byte) 0x00;
        data[3] = up;
        data[4] = (byte) 0x00;
        data[5] = speed;
        break;
      case "down":
        data[2] = (byte) 0x00;
        data[3] = down;
        data[4] = (byte) 0x00;
        data[5] = speed;
        break;
      case "left":
        data[2] = (byte) 0x00;
        data[3] = left;
        data[4] = speed;
        data[5] = (byte) 0x00;
        break;
      case "right":
        data[2] = (byte) 0x00;
        data[3] = right;
        data[4] = speed;
        data[5] = (byte) 0x00;
        break;
      case "zoom-out":
        data[2] = (byte) 0x00;
        data[3] = zoomOut;
        data[4] = (byte) 0x00;
        data[5] = (byte) 0x00;
        break;
      case "zoom-in":
        data[2] = (byte) 0x00;
        data[3] = zoomIn;
        data[4] = (byte) 0x00;
        data[5] = (byte) 0x00;
        break;
      case "stop-zoom":
      case "stop-move":
        data[2] = (byte) 0x00;
        data[3] = (byte) 0x00;
        data[4] = (byte) 0x00;
        data[5] = (byte) 0x00;
        break;
      case "load-preset":
        data[2] = (byte) 0x00;
        data[3] = loadPreset;
        data[4] = (byte) 0x00;
        data[5] = (byte) param.getPreset();
        break;
      case "save-preset":
        data[2] = (byte) 0x00;
        data[3] = savePreset;
        data[4] = (byte) 0x00;
        data[5] = (byte) param.getPreset();
        break;
      case "home":
        data[2] = (byte) 0x00;
        data[3] = loadPreset;
        data[4] = (byte) 0x00;
        data[5] = (byte) 0x00;
        break;
      default:
        return false;
    }
    data[6] = (byte) 0XAF;
    data[7] = data[1];
    for (int i = 2; i < 7; i++) {
      data[7] = (byte) (data[7] ^ data[i]);
    }
    client.write(data);
    client.close();
    return true;
  }

  @Override
  public boolean doOperate(Ptz ptz, Operate opt) {
    try {
      TcpClient client = getClient(ptz);
      return sendCmd(client, ptz.getAddress(), opt.getOperate(), opt.getParam());
    } catch (Exception e) {
      return false;
    }
  }

}
