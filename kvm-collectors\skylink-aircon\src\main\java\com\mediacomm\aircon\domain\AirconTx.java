package com.mediacomm.aircon.domain;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * AirconTx.
 */
@Data
public class AirconTx implements AirconDeviceSameField {
  private int id = 0;
  private String name;
  private String sn;
  private String ip;
  private String deviceType;
  private String deviceModel;
  private String softVersion;
  private boolean linkStatus;
  private List<AirconProperty> properties = new ArrayList<>();
}
