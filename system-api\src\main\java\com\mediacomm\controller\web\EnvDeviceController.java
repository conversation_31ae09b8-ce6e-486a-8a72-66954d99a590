package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.EnvDevice;
import com.mediacomm.entity.dao.NetLinkMerge;
import com.mediacomm.entity.vo.EnvDeviceVo;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.EnvDeviceService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import com.mediacomm.util.SkyLinkStringUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "环境设备配置，如操作终端、交换机")
@RestController
@RequestMapping(ResUrlDef.ENV_DEVICE)
public class EnvDeviceController extends SkyLinkController<EnvDevice, EnvDeviceService> {

  @Operation(summary = "新增监控设备")
  @PostMapping
  public Result<String> addDevice(@RequestBody @Validated EnvDevice envDevice) {
    envDevice.setId(SkyLinkStringUtil.uuid());
    service.save(envDevice);
    return Result.ok();
  }

  @Operation(summary = "删除监控设备")
  @DeleteMapping("/{id}")
  public Result<String> deleteDevice(@PathVariable String id) {
    service.removeById(id);
    return Result.ok();
  }

  @Operation(summary = "更新监控设备")
  @PutMapping("/update")
  public Result<String> updateDevice(@RequestBody EnvDevice envDevice) {
    if (StringUtils.isBlank(envDevice.getId())) {
      return Result.failure("The device id is empty!", ResponseCode.EX_FAILURE_400);
    }
    service.updateById(envDevice);
    return Result.ok();
  }

  @Operation(summary = "根据系统分级查询监控设备")
  @GetMapping("/env-devices")
  public Result<Collection<EnvDeviceVo>> getEnvDevicesByType(SubSystemType subSystemType) {
    return Result.ok(service.allBySubSystemType(subSystemType));
  }

  @Operation(summary = "添加交换机端口对端设备")
  @PostMapping("/net-link-relation")
  public Result<String> addNetLinkRelation(@RequestBody NetLinkMerge netLink) {
    service.saveNetLinkPort(netLink);
    return Result.ok();
  }

  @Operation(summary = "获取交换机端口对端设备设置关系")
  @GetMapping("/net-link-relation")
  public Result<Collection<NetLinkMerge>> getNetLinkRelation(@RequestParam String netDeviceId) {
    return Result.ok(service.allNetLinkAssoByNetId(netDeviceId));
  }

  @Operation(summary = "删除交换机端口对端设备关系")
  @DeleteMapping("/net-link-relation")
  public Result<String> delNetLinkRelation(@RequestParam String netDeviceId,
                                           @RequestParam Integer portNum) {
    service.removeNetLinkAssoByNetIdAndPortNum(netDeviceId, portNum);
    return Result.ok();
  }
}
