package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.DeviceModel;
import com.mediacomm.entity.dao.PositionGroup;
import com.mediacomm.entity.dao.VisualizationDevice;
import com.mediacomm.entity.dao.VisualizationScene;
import com.mediacomm.entity.vo.VisualizationDeviceVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.VisualizationDeviceMapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class VisualizationDeviceService extends
    SkyLinkServiceImpl<VisualizationDeviceMapper, VisualizationDevice> {
  @Autowired
  DeviceModelService modelService;
  @Autowired
  PositionGroupService groupService;
  @Autowired
  VisualizationSceneService sceneService;
  /**
   * 根据硬编码查询可视化设备.
   *
   * @param hardcode 硬编码值.
   * @return 可视化设备实例.
   */
  public VisualizationDevice oneByHardcode(String hardcode) {
    LambdaQueryWrapper<VisualizationDevice> queryWrapper = Wrappers.lambdaQuery(VisualizationDevice.class)
        .eq(VisualizationDevice::getHardcode, hardcode);
    return getOne(queryWrapper);
  }

  public Collection<VisualizationDeviceVo> all() {
    Collection<VisualizationDeviceVo> vos = new ArrayList<>();
    list().forEach(device -> {
      VisualizationDeviceVo vo = Optional.ofNullable(device).map(VisualizationDeviceVo::new)
          .orElse(null);
      Optional.ofNullable(vo).ifPresent(this::addVisualizationDeviceInfo);
      vos.add(vo);
    });
    return vos;
  }

  public void addVisualizationDeviceInfo(VisualizationDeviceVo vo) {
    PositionGroup positionGroup = groupService.getById(vo.getRoomId());
    DeviceModel deviceModel = modelService.getById(vo.getDeviceModel());
    VisualizationScene scene = sceneService.getById(vo.getDefaultScene());
    Optional.ofNullable(positionGroup).ifPresent(g -> vo.setRoomName(g.getName()));
    Optional.ofNullable(scene).ifPresent(g -> vo.setSceneName(scene.getName()));
    Optional.ofNullable(deviceModel).ifPresent(m -> {
      vo.setModelName(m.getModelName());
      vo.setModelName(m.getDeviceType());
    });
  }
}
