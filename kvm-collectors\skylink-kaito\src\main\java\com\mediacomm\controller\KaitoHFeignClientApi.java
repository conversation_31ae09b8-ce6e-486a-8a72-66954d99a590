package com.mediacomm.controller;

import com.mediacomm.domain.KaitoDeviceDetail;
import com.mediacomm.domain.KaitoInitStatus;
import com.mediacomm.domain.KaitoInputDetail;
import com.mediacomm.domain.KaitoInputList;
import com.mediacomm.domain.KaitoLayerDetail;
import com.mediacomm.domain.KaitoOutputDetail;
import com.mediacomm.domain.KaitoOutputList;
import com.mediacomm.domain.KaitoScreenDetail;
import com.mediacomm.domain.KaitoScreenList;
import com.mediacomm.domain.KaitoVideoServerInfo;
import com.mediacomm.domain.h.KaitoHInputDetailList;
import com.mediacomm.domain.h.KaitoHIpcDetail;
import com.mediacomm.domain.h.KaitoHIpcList;
import com.mediacomm.domain.h.KaitoHLayerDetailList;
import com.mediacomm.domain.h.KaitoHRequestBody;
import com.mediacomm.domain.h.KaitoHResponseBody;
import com.mediacomm.domain.h.KaitoHScreenDetail;
import com.mediacomm.domain.h.KaitoHWriteOsdReq;
import com.mediacomm.domain.request.KaitoDeviceIdReq;
import com.mediacomm.domain.request.KaitoInputDetailReq;
import com.mediacomm.domain.request.KaitoIpcSourceChanelListReq;
import com.mediacomm.domain.request.KaitoLayerClearReq;
import com.mediacomm.domain.request.KaitoLayerDetailReq;
import com.mediacomm.domain.request.KaitoLayerLayoutReq;
import com.mediacomm.domain.request.KaitoLayerReq;
import com.mediacomm.domain.request.KaitoOutputDetailReq;
import com.mediacomm.domain.request.KaitoSourceIdReq;
import com.mediacomm.domain.request.KaitoWriteSourceReq;
import com.mediacomm.domain.request.KaitoWriteWindowReq;
import com.mediacomm.domain.request.KaitoZOrderReq;
import com.mediacomm.entity.message.reqeust.body.ObjectId;
import java.net.URI;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * H型号接口.
 */
@FeignClient(name = "kaito-h-service", url = "http://localhost")
public interface KaitoHFeignClientApi {
  /**
   * 获取初始状态.
   */
  @PostMapping("/open/api/main/initStatus")
  KaitoHResponseBody<KaitoInitStatus> getInitStatus(URI uri, @RequestBody KaitoHRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 读取视频预监视频流信息.
   */
  @PostMapping("/open/api/main/readVideoServerInfo")
  KaitoHResponseBody<KaitoVideoServerInfo> getVideoServerInfo(URI uri, @RequestBody KaitoHRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 获取输入列表.
   */
  @PostMapping("/open/api/input/readList")
  KaitoHResponseBody<KaitoInputList> getInputReadList(URI uri, @RequestBody KaitoHRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 获取输入详情.
   */
  @PostMapping("/open/api/input/readList")
  KaitoHResponseBody<KaitoHInputDetailList> getInputDetails(URI uri, @RequestBody KaitoHRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 获取输入详情.
   */
  @PostMapping("/open/api/input/readDetail")
  KaitoHResponseBody<KaitoInputDetail> getInputDetail(URI uri, @RequestBody KaitoHRequestBody<KaitoInputDetailReq> requestBody);


  /**
   * 读取Ipc列表.
   */
  @PostMapping("/open/api/ipc/IPCSourceList")
  KaitoHResponseBody<KaitoHIpcList> getIpcSourceList(URI uri, @RequestBody KaitoHRequestBody<KaitoIpcSourceChanelListReq> requestBody);

  /**
   * 读取Ipc详情.
   */
  @PostMapping("/open/api/ipc/IPCChannelList")
  KaitoHResponseBody<KaitoHIpcDetail> getIpcChannelList(URI uri, @RequestBody KaitoHRequestBody<KaitoSourceIdReq> requestBody);

  /**
   * 获取输出列表.
   */
  @PostMapping("/open/api/output/readList")
  KaitoHResponseBody<KaitoOutputList> getOutputReadList(URI uri, @RequestBody KaitoHRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 获取输出详情.
   */
  @PostMapping("/open/api/output/readDetail")
  KaitoHResponseBody<KaitoOutputDetail> getOutputDetail(URI uri, @RequestBody KaitoHRequestBody<KaitoOutputDetailReq> requestBody);

  /**
   * 获取大屏列表.
   */
  @PostMapping("/open/api/screen/readList")
  KaitoHResponseBody<KaitoScreenList> getScreenReadList(URI uri, @RequestBody KaitoHRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 获取大屏详情.
   */
  @PostMapping("/open/api/screen/readDetail")
  KaitoHResponseBody<KaitoHScreenDetail> getScreenReadDetail(URI uri, @RequestBody KaitoHRequestBody<KaitoLayerReq> requestBody);

  /**
   * 获取设备详情.
   */
  @PostMapping("/open/api/device/readDetail")
  KaitoHResponseBody<KaitoDeviceDetail> getDeviceDetail(URI uri, @RequestBody KaitoHRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 获取指定 screen上的图层列表.
   */
  @PostMapping("/open/api/layer/detailList")
  KaitoHResponseBody<KaitoHLayerDetailList> getLayerList(URI uri, @RequestBody KaitoHRequestBody<KaitoLayerReq> requestBody);

  /**
   * 删除图层.
   */
  @PostMapping("/open/api/layer/delete")
  KaitoHResponseBody<Void> deleteLayer(URI uri, @RequestBody KaitoHRequestBody<KaitoLayerDetailReq> requestBody);

  /**
   * 清除图层.
   */
  @PostMapping("/open/api/layer/clear")
  KaitoHResponseBody<Void> clearLayer(URI uri, @RequestBody KaitoHRequestBody<KaitoLayerClearReq> requestBody);

  /**
   * 换源.
   */
  @PostMapping("/open/api/layer/writeSource")
  KaitoHResponseBody<Void> writeSource(URI uri, @RequestBody KaitoHRequestBody<KaitoWriteSourceReq> requestBody);

  /**
   * 图层移动、缩放s.
   */
  @PostMapping("/open/api/layer/writeWindow")
  KaitoHResponseBody<Void> writeWindow(URI uri, @RequestBody KaitoHRequestBody<KaitoWriteWindowReq> requestBody);

  /**
   * 图层Z-index.
   */
  @PostMapping("/open/api/layer/writeZIndex")
  KaitoHResponseBody<Void> writeZIndex(URI uri, @RequestBody KaitoHRequestBody<KaitoZOrderReq> requestBody);

  /**
   * 创建图层.
   */
  @PostMapping("/open/api/layer/create")
  KaitoHResponseBody<ObjectId> createLayer(URI uri, @RequestBody KaitoHRequestBody<KaitoLayerDetail> requestBody);

  /**
   * 模板开窗.
   */
  @PostMapping("/open/api/layer/screenLayerLayout")
  KaitoHResponseBody<Void> createLayers(URI uri, @RequestBody KaitoHRequestBody<KaitoLayerLayoutReq> requestBody);

  /**
   * 设置OSD.
   */
  @PostMapping("/open/api/screen/writeOSD")
  KaitoHResponseBody<KaitoScreenDetail.Osd> writeOsd(URI uri, @RequestBody KaitoHRequestBody<KaitoHWriteOsdReq> requestBody);
}
