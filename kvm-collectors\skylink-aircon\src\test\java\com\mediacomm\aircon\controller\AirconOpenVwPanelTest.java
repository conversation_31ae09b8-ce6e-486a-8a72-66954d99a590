package com.mediacomm.aircon.controller;

import com.mediacomm.aircon.domain.AirconPanelRect;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.aircon.util.mapper.AirconEntityMapper;
import com.mediacomm.aircon.preview.AirconPreviewManager;
import feign.FeignException;
import java.net.URI;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 重构后的 Aircon OPEN_VW_PANEL 测试（不依赖 TestContainers）。
 * 参考 CaesarOpenVwPanelTest，实现纯单元测试：
 * 直接调用 AirconCmdServer.openVwPanel 验证行为与返回值。
 */
public class AirconOpenVwPanelTest { // 保留原类名以兼容现有引用

    @InjectMocks
    private AirconCmdServer airconCmdServer; // 被测类

    // 依赖 Mock
    @Mock private AirconFeignClientApi airconFeignClientApi;
    @Mock private KvmMasterService kvmMasterService;
    @Mock private KvmVideoWallService videoWallService;
    @Mock private KvmAssetService kvmAssetService;
    @Mock private AirconEntityMapper airconEntityMapper;
    @Mock private AirconPreviewManager airconPreviewManager; // 预留（当前用例未使用）

    // 公共测试数据
    private KvmMaster master;
    private KvmVideoWall videoWall;
    private KvmAsset asset;
    private PanelRect panelRect; // 业务层通用 PanelRect
    private AirconPanelRect mappedAirconPanelRect; // 映射后用于调用远端的 AirconPanelRect

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 初始化基础测试数据
        master = TestDataFactory.createTestKvmMaster();
        videoWall = TestDataFactory.createTestKvmVideoWall();
        asset = TestDataFactory.createTestKvmAsset();
        panelRect = TestDataFactory.createTestPanelRect();
        mappedAirconPanelRect = TestDataFactory.createSuccessfulAirconPanelRect();

        // 默认 Mock 行为（各测试可覆盖）
        when(kvmMasterService.getById(TestDataFactory.TEST_MASTER_ID)).thenReturn(master);
        when(videoWallService.getById(TestDataFactory.TEST_VIDEO_WALL_ID)).thenReturn(videoWall);
        when(kvmAssetService.getById(panelRect.getChildPanels().get(0).getVideoSrcId())).thenReturn(asset);

        // PanelRect -> AirconPanelRect
        when(airconEntityMapper.toAirconPanelRect(any(PanelRect.class))).thenReturn(mappedAirconPanelRect);
        // AirconPanelRect -> PanelRect
        when(airconEntityMapper.toPanelRect(any(AirconPanelRect.class), anyString())).thenReturn(panelRect);
    }

    @Test
    void testOpenVwPanelSuccess() {
        MqRequest<PanelRectRequestBody> request = TestDataFactory.createTestAirconMqRequest();
        String messageBody = JsonUtils.encode(request);

        AirconPanelRect responseRect = TestDataFactory.createSuccessfulAirconPanelRect();
        when(airconFeignClientApi.openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(AirconPanelRect.class))).thenReturn(responseRect);

        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.AIRCON_KVM_OPEN_VW_PANEL);
        assertNotNull(result, "响应不应为空");
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response);
        assertEquals(200, response.getCode());

        verify(airconFeignClientApi, times(1)).openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            argThat(rect -> rect.getXpos() == request.getBody().getPanelRect().getXpos() &&
                            rect.getYpos() == request.getBody().getPanelRect().getYpos() &&
                            rect.getWidth() == request.getBody().getPanelRect().getWidth() &&
                            rect.getHeight() == request.getBody().getPanelRect().getHeight()));
    }

    @Test
    void testOpenVwPanelAirconFailure() {
        MqRequest<PanelRectRequestBody> request = TestDataFactory.createTestAirconMqRequest();
        String messageBody = JsonUtils.encode(request);

        // 远端返回异常（模拟 Aircon 内部错误）
        when(airconFeignClientApi.openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(AirconPanelRect.class))).thenThrow(mock(FeignException.class));

        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.AIRCON_KVM_OPEN_VW_PANEL);
        assertNotNull(result);
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response);
        assertEquals(500, response.getCode(), "应返回500错误码");

        verify(airconFeignClientApi, times(1)).openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID), any(AirconPanelRect.class));
    }

    @Test
    void testOpenVwPanelInvalidMasterId() {
        MqRequest<PanelRectRequestBody> request = TestDataFactory.createTestAirconMqRequest();
        request.setMasterId("invalid-master-id");
        String messageBody = JsonUtils.encode(request);

        when(kvmMasterService.getById("invalid-master-id")).thenReturn(null);

        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.AIRCON_KVM_OPEN_VW_PANEL);
        assertNotNull(result);
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response);
        assertEquals(404, response.getCode());
        assertTrue(response.getMessage().contains(AirconCmdServer.NO_HOST));
        verify(airconFeignClientApi, times(0)).openVideoWallPanel(any(), anyInt(), any());
    }

    @Test
    void testOpenVwPanelInvalidMessageFormat() {
        String invalidMessage = "{invalid json}"; // 非法 JSON
        String result = sendMessageAndWaitForResponse(invalidMessage, RoutingKey.AIRCON_KVM_OPEN_VW_PANEL);
        assertNotNull(result);
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertTrue(response.getMessage().contains(AirconCmdServer.PARAM_ERR));
        verify(airconFeignClientApi, times(0)).openVideoWallPanel(any(), anyInt(), any());
    }

    @Test
    void testOpenVwPanelNetworkError() {
        MqRequest<PanelRectRequestBody> request = TestDataFactory.createTestAirconMqRequest();
        String messageBody = JsonUtils.encode(request);

        // 模拟网络不可用 / 服务不可达异常
        when(airconFeignClientApi.openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(AirconPanelRect.class))).thenThrow(mock(FeignException.class));

        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.AIRCON_KVM_OPEN_VW_PANEL);
        assertNotNull(result);
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response);
        assertEquals(500, response.getCode());
        verify(airconFeignClientApi, times(1)).openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID), any(AirconPanelRect.class));
    }
    
    private String sendMessageAndWaitForResponse(String messageBody, String routingKey) {
        return airconCmdServer.openVwPanel(messageBody);
    }
}
