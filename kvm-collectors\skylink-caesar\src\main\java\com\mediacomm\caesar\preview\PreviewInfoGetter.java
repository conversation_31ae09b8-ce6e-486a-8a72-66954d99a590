package com.mediacomm.caesar.preview;

import com.mediacomm.entity.dao.KvmPreviewAsso;
import java.util.Collection;
import lombok.ToString;

/**
 * .
 */
public interface PreviewInfoGetter {

  /**
   * .
   */
  enum RealTimePreviewStatus {
    REAL_TIME_READY, // 准备实时预览
    REAL_TIME_STOPPED, // 实时预览暂停取图
    REAL_TIME_RUN,   // 正在实时预览
    REAL_TIME_ESTABLISHING // 正在建立连接关系
  }

  /**
   * .
   */
  enum PollingPreviewStatus {
    POLLING,           // 正在轮询预览
    POLLING_STOPPED,   // 轮询预览暂停取图
  }

  /**
   * .
   */
  @ToString
  class RealTimePreviewInfo {

    /**
     * 实时预览状态.
     */
    public RealTimePreviewStatus status;
    /**
     * 实时预览通道.
     */
    public KvmPreviewAsso channel;

    public int runError = 0;
  }

  boolean isRealTimePreviewing(String txId);

  boolean isPollingPreviewing(String txId);

  boolean isPreviewing(String txId);

  Collection<KvmPreviewAsso> getPollingPreviewChannels();

  Collection<String> getRealTimePreviewTxes();

  Collection<String> getPollingPreviewTxes();

  /**
   * 获取实时预览信息.
   *
   * @param txId txid.
   * @return 预览信息
   */
  RealTimePreviewInfo getRealTimePreviewInfo(String txId);

  /**
   * 获取轮询预览信息.
   *
   * @param txId txid.
   * @return 预览信息
   */
  PollingPreviewStatus getPollingPreviewStatus(String txId);

  /**
   * 配置实时预览状态.
   * 当前为REAL_TIME_READY，可以配置为REAL_TIME_ESTABLISHING.
   * 当前为REAL_TIME_ESTABLISHING，可以配置为REAL_TIME_READY或者REAL_TIME_RUNNING.
   * 当前为REAL_TIME_RUNNING、REAL_TIME_STOPPED时，不能通过此接口配置状态.
   *
   * @param txId txId.
   * @param status .
   * @return 如果配置成功，返回true.
   */
  boolean setRealTimePreviewStatus(String txId, RealTimePreviewStatus status);

  /**
   * 标记实时预览出错.
   *
   * @param txId tx id.
   * @return 标记成功.
   */
  boolean setRealTimePreviewError(String txId);

  /**
   * 清除实时预览错误.
   *
   * @param txId tx id.
   * @return 清除成功.
   */
  boolean clearRealTimePreviewError(String txId);

  /**
   * 获取预览图上一次被使用的时间.
   *
   * @param txId txId.
   * @return 时间.
   */
  long getLastPreviewUsedTime(String txId);

  /**
   * 预览图是否重要.
   *
   * @param txId txid.
   * @return .
   */
  boolean isImportant(String txId);
}
