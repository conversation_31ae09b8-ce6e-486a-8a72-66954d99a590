package com.mediacomm.domain.h;

import com.mediacomm.domain.KaitoLayer;
import com.mediacomm.domain.KaitoLayerDetail;
import lombok.Data;

/**
 * .
 */
@Data
public class KaitoHLayer {
  private int deviceId;
  private KaitoLayer.AudioStatus audioStatus;
  private KaitoLayerDetail.General general;
  private int layerId;
  private int screenId;
  private Source source;
  private KaitoLayerDetail.Window window;

  @Data
  public static class Source {
    private int connectCapacity;
    private int cropId;
    private int decodeId;
    private int decodeMode;
    private int functionType;
    private int hardwareType;
    private int inputId;
    private int interfaceType;
    private int modelId;
    private String name;
    private int slotId;
    private int sourceId;
    private int sourceType; // 源类型：0：无源,1：输入类型, 3：IPC 类型
    private int streamId;
    private int templateId;
    private int videoMode;
  }
}
