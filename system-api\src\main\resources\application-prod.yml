spring:
  data:
    redis:
      host: ${REDIS_SERVICE_HOST}
      port: ${REDIS_HOST_PORT}
      database: 0                          #数据库索引
      #password: 123456
      client-type: lettuce
      lettuce:
        pool:
          max-active: 20     # 最大连接数
          max-idle: 10       # 最大空闲
          min-idle: 2        # 最小空闲
          max-wait: 5000     # 最大阻塞等待时间(负数表示没限制)
  datasource:
    driver-class-name: ${DATABASE_DRIVER_CLASS}
    username: ${DATABASE_USER}
    password: ${DATABASE_PASSWORD}
    url: jdbc:${DATABASE_DRIVER_NAME}://${DATABASE_HOST}:${DATABASE_PORT:3306}/${DATABASE_NAME}?${DATABASE_JDBC_URL_PARAMS}
    druid:
      initial-size: 10                            # 初始连接池大小
      min-idle: 10                                # 最小连接数
      max-active: 20                              # 最大连接数
      max-wait: 60000                             # 获取连接时的最大等待时间
      time-between-eviction-runs-millis: 60000    # 一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000      # 多久才进行一次检测需要关闭的空闲连接，单位是毫秒
      validation-query-timeout: 1
      validation-query: SELECT 1                  # 检测连接是否有效的 SQL语句，为空时以下三个配置均无效
      test-while-idle: true                       # 申请连接时如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效，默认false
      test-on-borrow: true                        # 申请连接时执行validationQuery检测连接是否有效，默认true，开启后会降低性能
      test-on-return: false                       # 归还连接时执行validationQuery检测连接是否有效，默认false，开启后会降低性能
      filter:
        slf4j:
          enabled: true
          result-set-log-enabled: true
          connection-log-enabled: false
          data-source-log-enabled: false
          statement-executable-sql-log-enable: true
          statement-log-enabled: true
  flyway:
    enabled: true # 启用Flyway，默认启用
    locations: classpath:db/migration
    baseline-on-migrate: true # 如果数据库中没有flyway_schema_history表，则将当前状态设为基线V1
    baseline-version: 1.0.0 # 基线版本号，与 baseline-on-migrate 配合使用，如果你的第一个脚本版本不是1，可以调整这个
    out-of-order: true # 允许乱序执行
  rabbitmq:
    host: ${RABBITMQ_SERVICE_HOST}
    port: ${RABBITMQ_HOST_PORT}
    username: ${RABBITMQ_DEFAULT_USER}
    password: ${RABBITMQ_DEFAULT_PASS}
    listener:
      simple:
        acknowledge-mode: auto # 自定确定
        prefetch: 1 # 消费者端每次拉去的消息数
        default-requeue-rejected: false # 消费被拒绝时,true为重回队列
        retry:
          enabled: false # 是否支持重试
          #max-attempts: 3 # 重试最大次数
          #max-interval: 1000ms # 重试最大间隔时间
  #cloud:
    #nacos:
      #server-addr: ${NACOS_SERVICE_HOST}
      #discovery:
        #server-addr: ${NACOS_SERVICE_HOST}
        #service: ${spring.application.name}
        #enabled: true
        #instance-enabled: true
  mqtt:
    url: tcp://${RABBITMQ_SERVICE_HOST}:1883
    client:
      id: stock-producer
    default:
      topic: stock-topic
    completionTimeout: 3000
    Qos: 2
    userName: admin
    password: 123456
logging:
  level:
    root: INFO # 或者 DEBUG 看更多信息
    org.springframework.jdbc.datasource.init: TRACE # TRACE 级别能看到更详细的脚本加载和执行步骤
    org.springframework.boot.autoconfigure.sql.init: TRACE # 相关的自动配置类
    org.springframework.core.io.support.PathMatchingResourcePatternResolver: DEBUG # 查看资源解析过程
    com.mysql.cj.jdbc: DEBUG # MySQL 驱动日志
