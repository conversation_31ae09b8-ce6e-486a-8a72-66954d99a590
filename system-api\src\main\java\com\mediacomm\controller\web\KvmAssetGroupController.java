package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAssetGroup;
import com.mediacomm.entity.vo.KvmAssetGroupVo;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.pojo.AssetsGroupDto;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmAssetGroupService;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.AssetGroupType;
import com.mediacomm.system.variable.sysenum.OperateType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "外设分组配置", description = "Tx或Rx分组配置的API")
@RestController
@RequestMapping(ResUrlDef.ASSET_GROUP)
public class KvmAssetGroupController extends
    SkyLinkController<KvmAssetGroup, KvmAssetGroupService> {

  @Autowired
  KvmAssetService assetService;

  @Operation(summary = "获取所有Tx分组")
  @Parameter(name = "roomId", description = "房间Id")
  @GetMapping("/kvm-video-srcs")
  public Result<Collection<KvmAssetGroupVo>> getVideoSrcs(@RequestParam(name = "roomId",
                                                           required = false) Integer roomId,
                                                          @RequestParam(required = false) Integer pid) {
    if (roomId != null && pid != null && pid > 0) {
      return Result.ok(service.allChildGroup(roomId, pid, AssetGroupType.TX_GROUP));
    }
    return Result.ok(service.allByGroupType(roomId, AssetGroupType.TX_GROUP));
  }

  @Operation(summary = "获取所有Rx分组")
  @Parameter(name = "roomId", description = "房间Id")
  @GetMapping("/kvm-video-views")
  public Result<Collection<KvmAssetGroupVo>> getVideoViews(@RequestParam(name = "roomId",
                                                            required = false) Integer roomId) {
    return Result.ok(service.allByGroupType(roomId, AssetGroupType.RX_GROUP));
  }

  @Operation(summary = "获取分组内外设信息")
  @GetMapping("/kvm-assets/{id}")
  public Result<Collection<KvmAssetVo>> getAssetsByGroupId(@PathVariable Integer id) {
    return Result.ok(assetService.allByGroupId(id));
  }

  /**
   * .
   */
  @OperationLogRecord(title = "修改指定分组", operateType = OperateType.UPDATE, requestBody = "#{src}")
  @Operation(summary = "根据Id修改指定分组信息")
  @PutMapping("/{id}")
  public Result<String> updateAssetGroup(@PathVariable Integer id, @RequestBody KvmAssetGroup src) {
    src.setAssetGroupId(id);
    service.updateById(src);
    return Result.ok();
  }

  @Operation(summary = "修改子分组的序号")
  @PostMapping("/child-group/seq")
  public Result<String> exchangeSeq(@RequestParam(name = "pid") Integer pid,
                                    @RequestBody List<Integer> childGroupIds) {
    if (service.countChildGroup(pid) != childGroupIds.size()) {
      return Result.failure("Requires full sub groups.",
              ResponseCode.EX_FAILURE_400);
    }
    List<KvmAssetGroup> groups = new ArrayList<>();
    for (int i = 0; i < childGroupIds.size(); i++) {
      KvmAssetGroup group = service.getById(childGroupIds.get(i));
      if (group == null || group.getParentGroupId() == 0
              || !Objects.equals(group.getParentGroupId(), pid)) {
        return Result.failure("Error param.",
                ResponseCode.EX_FAILURE_400);
      }
      group.setSeq(i + 1);
      groups.add(group);
    }
    service.updateBatchById(groups);
    return Result.ok();
  }

  @OperationLogRecord(title = "删除指定分组", operateType = OperateType.DELETE, requestBody = "#{id}")
  @Operation(summary = "根据Id删除指定分组信息")
  @DeleteMapping("/{id}")
  public Result<String> delAssetGroup(@PathVariable Integer id) {
    service.removeById(id);
    return Result.ok();
  }

  @OperationLogRecord(title = "新增分组", operateType = OperateType.INSERT, requestBody = "#{src}")
  @Operation(summary = "新增分组")
  @PostMapping
  public Result<?> addAssetGroup(@RequestBody KvmAssetGroup src) {
    service.save(src);
    return Result.ok();
  }

  @Operation(summary = "批量修改子分组的外设")
  @PostMapping("/kvm-extend-devices")
  public Result<String> addExtendsToGroups(@RequestBody Collection<AssetsGroupDto> extendsAndGroupIds) {
    for (AssetsGroupDto extendsAndGroupId : extendsAndGroupIds) {
      KvmAssetGroup group = service.getById(extendsAndGroupId.getGroupId());
      if (group == null) {
        return Result.failure("Not found group " + extendsAndGroupId.getGroupId() + ".",
                ResponseCode.EX_NOTFOUND_404);
      }
      if (group.getParentGroupId() == 0) {
        return Result.failure("Cannot add peripherals to a top-level group.",
                ResponseCode.EX_FAILURE_400);
      }
      service.delAllFromMerge(extendsAndGroupId.getGroupId());
      if (extendsAndGroupId.getAssetIds() != null && !extendsAndGroupId.getAssetIds().isEmpty()) {
        service.saveToMerge(extendsAndGroupId.getGroupId(), extendsAndGroupId.getAssetIds());
      }
    }
    return Result.ok();
  }

  @Operation(summary = "删除分组中的外设")
  @DeleteMapping("/kvm-extend-devices")
  public Result<String> delExtends(@RequestParam(name = "assetGroupId") Integer groupId,
                              @RequestBody Collection<String> extendIds) {
    service.delFromMerge(groupId, extendIds);
    return Result.ok();
  }

}
