package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.VisualizationLayout;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.VisualizationLayoutMapper;
import com.mediacomm.system.variable.sysenum.VisualizationLayoutType;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class VisualizationLayoutService extends
    SkyLinkServiceImpl<VisualizationLayoutMapper, VisualizationLayout> {

  public Collection<VisualizationLayout> allByLayoutType(VisualizationLayoutType type) {
    LambdaQueryWrapper<VisualizationLayout> wrapper =
        Wrappers.lambdaQuery(VisualizationLayout.class)
        .eq(VisualizationLayout::getLayoutType, type)
        .or(w -> w.eq(VisualizationLayout::getLayoutType, VisualizationLayoutType.GENERAL_LAYOUT));
    return this.list(wrapper);
  }
}
