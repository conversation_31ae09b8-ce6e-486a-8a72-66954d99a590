package com.mediacomm.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.system.variable.sysenum.ServiceName;
import com.mediacomm.util.JsonUtils;
import java.util.HashSet;
import java.util.Set;
import lombok.Data;

/**
 * 服务注册信息.
 *
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ServiceInstance {
  private ServiceName serviceId; // 服务名称，同时也是该服务的指定队列的key
  private String ip; // 服务Ip
  private Integer port; // 服务端口
  private Set<DeviceInstance> devices = new HashSet<>(); // 服务负责的设备

  /**
   * .
   */
  public static ServiceInstance createServiceInstance(ServiceName serviceId, String ip,
                                                      Integer port) {
    ServiceInstance serviceInstance = new ServiceInstance();
    serviceInstance.setServiceId(serviceId);
    serviceInstance.setIp(ip);
    serviceInstance.setPort(port);
    return serviceInstance;
  }

  /**
   * .
   */
  public String getDevices() {
    if (devices != null) {
      return JsonUtils.encodeCollection(devices);
    } else {
      return "";
    }
  }

  public Set<DeviceInstance> devicesObject() {
    return this.devices;
  }
}
