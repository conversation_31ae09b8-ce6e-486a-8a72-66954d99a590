package com.mediacomm.controller.web;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.PositionGroup;
import com.mediacomm.entity.dao.VisualizationLayout;
import com.mediacomm.entity.message.reqeust.body.ImageRequestBody;
import com.mediacomm.entity.message.reqeust.body.VideoWallChangeBody;
import com.mediacomm.entity.vo.KvmMasterVo;
import com.mediacomm.pojo.VideoWallGroupDto;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.service.PositionGroupService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.BuildMqRequestBodyUtils;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RpcSenderUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "大屏管理", description = "大屏管理的API")
@RestController
@RequestMapping(ResUrlDef.KVM_VIDEO_WALL)
public class KvmVideoWallController extends SkyLinkController<KvmVideoWall, KvmVideoWallService> {

  @Autowired
  PositionGroupService positionGroupService;
  @Autowired
  KvmMasterService masterService;
  @Autowired
  KvmAssetService assetService;
  @Autowired
  RpcSenderUtils senderUtils;

  @Operation(summary = "根据房间Id获取大屏信息")
  @GetMapping("/position/kvm-video-walls")
  public Result<Collection<KvmVideoWall>> getVideoWalls(@RequestParam(name = "positionId",
                                                          required = false) Integer positionId) {
    return Result.ok(service.allByPositionId(positionId));
  }

  @Operation(summary = "获取所有大屏信息")
  @GetMapping("/kvm-video-walls")
  public Result<Collection<KvmVideoWall>> getAllVideoWalls() {
    return Result.ok(service.list().stream().peek(wall -> {
      if (wall.getRoomId() != null) {
        PositionGroup g = positionGroupService.getById(wall.getRoomId());
        if (g != null) {
          wall.setRoomName(g.getName());
        }
      }
      if (wall.getMasterId() != null) {
        KvmMasterVo master = masterService.oneById(wall.getMasterId());
        if (master != null) {
          wall.setMasterName(master.getName());
        }
      }
    }).collect(Collectors.toSet()));
  }

  /**
   * .
   */
  @OperationLogRecord(title = "修改指定大屏", operateType = OperateType.UPDATE,
      requestBody = "#{videoWall}")
  @Operation(summary = "根据Id修改指定大屏信息")
  @PutMapping("/{id}")
  public Result<?> updateVideoWall(@PathVariable Integer id,
                                        @RequestBody KvmVideoWall videoWall) {
    KvmVideoWall oldWall = service.getById(id);
    videoWall.setWallId(id);
    service.updateById(videoWall);
    if (videoWall.getDeviceType() == DeviceType.CAESAR_R1C8_VIDEO_WALL
            || oldWall.getDeviceType() == DeviceType.CAESAR_R1C8_VIDEO_WALL) {
      return previewAssoChangeNotice(RoutingOperation.VW_UPDATE, oldWall, DeviceType.CAESAR_R1C8_VIDEO_WALL);
    }
    if (videoWall.getDeviceType() == DeviceType.CAESAR_VP7_VIDEO_WALL) {
      String bottomHash = Property.findValueByKey(
              videoWall.getCollectorProperties(), PropertyKeyConst.BOTTOM_IMAGE_HASH, null);
      if (StringUtils.isNotEmpty(bottomHash)) {
        return doCmd(videoWall, RoutingOperation.VW_BOTTOM_IMAGE_SET, ImageRequestBody.builder()
                .wallId(id)
                .hash(Long.parseLong(bottomHash)).build());
      }
    }
    return Result.ok();
  }

  @Operation(summary = "获取大屏当前勾选使用的布局")
  @GetMapping("/layouts")
  public Result<Collection<VisualizationLayout>> getVideoWallLayout(@RequestParam("videoWallId")
                                                                      Integer id) {
    return Result.ok(service.allLayoutsByWallId(id));
  }

  @Operation(summary = "选择大屏的可用布局")
  @Parameter(name = "videoWallId", description = "大屏Id")
  @PostMapping("/layouts")
  public Result<String> addVideoWallLayout(@RequestParam("videoWallId") Integer id,
                                           @RequestBody Collection<Integer> layoutIds) {
    Collection<Integer> oldLayoutIds = service.allLayoutsByWallId(id).stream()
        .map(VisualizationLayout::getLayoutId).collect(Collectors.toSet());
    if (CollectionUtil.isEmpty(layoutIds)) { // 删除布局
      service.delBatchLayoutById(id, oldLayoutIds);
    } else if (CollectionUtil.isEmpty(oldLayoutIds)) { // 新增布局
      service.saveBatchLayoutById(id, new HashSet<>(layoutIds));
      return Result.ok();
    } else {
      if (CollectionUtil.isEqualList(oldLayoutIds, layoutIds)) {
        return Result.ok();
      } else { // 修改布局
        service.delBatchLayoutById(id, oldLayoutIds);
        service.saveBatchLayoutById(id, new HashSet<>(layoutIds));
      }
    }
    return Result.ok();
  }

  @OperationLogRecord(title = "删除指定大屏", operateType = OperateType.DELETE)
  @Operation(summary = "根据Id删除指定大屏信息")
  @DeleteMapping("/{id}")
  public Result<?> delVideoWall(@PathVariable Integer id) {
    KvmVideoWall wall = service.getById(id);
    service.removeById(id);
    if (wall.getDeviceModel().equals(DeviceType.CAESAR_R1C8_VIDEO_WALL.getDeviceTypeId())) {
      return previewAssoChangeNotice(RoutingOperation.VW_UPDATE, wall, DeviceType.CAESAR_R1C8_VIDEO_WALL);
    }
    return Result.ok();
  }

  @Operation(summary = "获取可监控大屏")
  @GetMapping("/preliminary")
  public Result<Collection<VideoWallGroupDto>> getPreliminaryVideoWalls() {
    Collection<KvmVideoWall> isSupportMonitor = service.list().stream()
            .filter(wall -> Objects.equals(wall.getDeviceModel(), DeviceType.CAESAR_VP7_VIDEO_WALL.getDeviceTypeId()))
            .collect(Collectors.toSet());
    Collection<VideoWallGroupDto> videoWallGroupDtoList = new ArrayList<>();
    for (KvmVideoWall kvmVideoWall : isSupportMonitor) {
      VideoWallGroupDto wallGroupDto = new VideoWallGroupDto();
      wallGroupDto.setName(kvmVideoWall.getName());
      wallGroupDto.setWallId(kvmVideoWall.getWallId());
      wallGroupDto.setMasterId(kvmVideoWall.getMasterId());
      // 使用 Set 来辅助去重，deviceIp
      kvmVideoWall.getDecoders().forEach(decoder -> {
        KvmAsset asset = assetService.oneByDeviceId(decoder.getDeviceId(),
                kvmVideoWall.getMasterId());
        if (asset != null) {
          // 检查 wallGroupDto.getDecoders() 中是否已经存在相同 deviceIp 的 asset
          boolean exists = wallGroupDto.getDecoders().stream()
                  .anyMatch(existingAsset -> Objects.equals(existingAsset.getDeviceIp(), asset.getDeviceIp()));
          if (!exists) {
            wallGroupDto.getDecoders().add(asset);
          }
        }
      });
      videoWallGroupDtoList.add(wallGroupDto);
    }

    return Result.ok(videoWallGroupDtoList);
  }

  private Result<?> previewAssoChangeNotice(String topicDto, KvmVideoWall previewWall, DeviceType deviceType) {
    if (previewWall != null) {
      VideoWallChangeBody body = VideoWallChangeBody.builder()
              .wallId(previewWall.getWallId())
              .deviceType(deviceType).build();
      return doCmd(previewWall, topicDto, body);
    } else {
      return Result.failure("No exist video wall!", ResponseCode.EX_FAILURE_400);
    }
  }

  private <T> Result<?> doCmd(KvmVideoWall videoWall, String topicDto, T requestBody) {
    if (videoWall == null) {
      return Result.failure("No exist videoWall!", ResponseCode.EX_FAILURE_400);
    }
    return JsonUtils.decode(senderUtils.send(videoWall.getMasterId(), topicDto,
                    BuildMqRequestBodyUtils.buildVideoWallMqBody(videoWall, requestBody)),
            new TypeReference<>() {});
  }
}
