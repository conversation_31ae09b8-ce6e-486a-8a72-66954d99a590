package com.mediacomm.caesar.snapshot;

import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.system.variable.sysenum.DeviceType;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

class DeviceDataGetterDecorator implements DeviceDataGetter {

  private DeviceDataGetter inner;

  public DeviceDataGetterDecorator(DeviceDataGetter inner) {
    this.inner = inner;
  }

  public void setInner(DeviceDataGetter inner) {
    this.inner = inner;
  }

  @Override
  public boolean is4kTx(String txId) {
    return inner.is4kTx(txId);
  }

  @Override
  public KvmPreviewAsso getAvailablePreviewChannel(boolean is4k,
                                                   boolean ignoreIncompleteVideoWall) {
    return inner.getAvailablePreviewChannel(is4k, ignoreIncompleteVideoWall);
  }

  @Override
  public Map<Integer, Collection<KvmPreviewAsso>> getAllPreviewChannels(
      boolean ignoreIncompleteVideoWall) {
    return inner.getAllPreviewChannels(ignoreIncompleteVideoWall);
  }

  @Override
  public Collection<KvmPreviewAsso> getPreviewChannels(Integer videoWallId) {
    return inner.getPreviewChannels(videoWallId);
  }

  @Override
  public KvmVideoWall getKvmPreviewAssoWallById(Integer videoWallId) {
    return inner.getKvmPreviewAssoWallById(videoWallId);
  }

  @Override
  public KvmAsset getExtendDevice(String id) {
    return inner.getExtendDevice(id);
  }

  @Override
  public String getMasterIp() {
    return inner.getMasterIp();
  }

  @Override
  public String makeTxSavePath(String hardCode) {
    return null;
  }

  @Override
  public void addPreviewChannel(Integer previewVideoWallId) {

  }

  @Override
  public void updatePreviewChannel(Integer previewVideoWallId) {

  }

  @Override
  public void updatePreviewChannel(String assetId, int availableNum) {

  }

  @Override
  public void delPreviewChannel(Integer previewVideoWallId) {

  }

  @Override
  public Map<String, Collection<KvmPreviewAsso>> getAllPreviewChannels(DeviceType previewDeviceType) {
    return new HashMap<>();
  }

  @Override
  public Collection<KvmPreviewAsso> getPreviewChannels(String assetId) {
    return inner.getPreviewChannels(assetId);
  }
}
