package com.mediacomm.caesar.preview.strategy;

import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.system.variable.sysenum.DeviceType;

import java.util.Collection;
import java.util.Map;

/**
 * .
 */
public interface DeviceDataGetter {

  boolean is4kTx(String txId);

  /**
   * 获取可用的预览通道.
   *
   * @param is4k 是否用于4k的预览.
   * @param ignoreIncompleteVideoWall 忽略通道跟在线设备不完整的预览终端.
   * @return 可用的预览通道，如果没有，返回null.
   */
  KvmPreviewAsso getAvailablePreviewChannel(boolean is4k, boolean ignoreIncompleteVideoWall);

  /**
   * 重新生成所有预览终端的所有预览通道，并重置状态为未使用.
   *
   * @param ignoreIncompleteVideoWall 忽略通道跟在线设备不完整的预览终端.
   * @return key:视频墙ID, value:预览通道集合.
   */
  Map<Integer, Collection<KvmPreviewAsso>> getAllPreviewChannels(boolean ignoreIncompleteVideoWall);


  /**
   * 重新生成所有预览终端的所有预览通道，并重置状态为未使用.
   */
  Map<String, Collection<KvmPreviewAsso>> getAllPreviewChannels(DeviceType previewDeviceType);

  /**
   * 获取一个预览终端的所有预览通道.
   *
   * @param videoWallId .
   * @return .
   */
  Collection<KvmPreviewAsso> getPreviewChannels(Integer videoWallId);

  /**
   * 获取一个预览终端的所有预览通道.
   *
   * @param assetId .
   * @return .
   */
  Collection<KvmPreviewAsso> getPreviewChannels(String assetId);

  KvmVideoWall getKvmPreviewAssoWallById(Integer videoWallId);

  KvmAsset getExtendDevice(String extendId);

  String getMasterIp();

  /**
   * 生成Tx保存图片的路径.
   *
   * @param txId .
   * @return 返回路径名称.
   */
  String makeTxSavePath(String txId);

  void addPreviewChannel(Integer previewVideoWallId);

  void updatePreviewChannel(Integer previewVideoWallId);

  void updatePreviewChannel(String assetId, int availableNum);

  void delPreviewChannel(Integer previewVideoWallId);
}
