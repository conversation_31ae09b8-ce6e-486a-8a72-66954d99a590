package com.mediacomm.aircon.preview;

import com.mediacomm.aircon.task.ImageFetcherTask;
import com.mediacomm.entity.dao.KvmAsset;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * .
 */
@Component
@Slf4j
public class AirconPreviewManager {
  private final ExecutorService executorService = Executors.newVirtualThreadPerTaskExecutor();
  // 用于防止并发执行：存储进行中的任务
  private final ConcurrentHashMap<String, CompletableFuture<Void>> inFlightTasks = new ConcurrentHashMap<>();
  // 用于速率限制：存储每个设备上次任务的【完成】时间戳
  private final ConcurrentHashMap<String, Long> lastCompletionTimes = new ConcurrentHashMap<>();
  // 定义冷却时间常量
  private static final long MIN_INTERVAL_MS = 150;

  public void onDevicePreview(KvmAsset asset, String serverIp) {
    String assetId = asset.getAssetId();
    long currentTime = System.currentTimeMillis();
    Long lastCompletionTime = lastCompletionTimes.get(assetId);
    // 短时间内不要对Tx频繁的预览请求
    if (lastCompletionTime != null && (currentTime - lastCompletionTime < MIN_INTERVAL_MS)) {
      log.debug("Rate limit for device {}: request ignored. Last completion was {}ms ago.",
              asset.getName(), currentTime - lastCompletionTime);
      return;
    }
    // 使用 computeIfAbsent 实现原子性的“检查并创建”
    inFlightTasks.computeIfAbsent(asset.getAssetId(), key -> {
      log.debug("No active task for device {}. Initiating new image fetch task.", asset.getName());
      CompletableFuture<Void> taskFuture = new CompletableFuture<>();
      executorService.submit(() -> {
        try {
          ImageFetcherTask task = new ImageFetcherTask(asset, serverIp);
          task.saveImage();
          // 如果任务成功，完成 Future
          taskFuture.complete(null);
        } catch (Exception e) {
          // 如果任务失败，以异常方式完成 Future
          taskFuture.completeExceptionally(e);
        }
      });
      // 任务完成后，无论成功或失败，都将它从 Map 中移除，以便下次能重新触发
      taskFuture.whenComplete((result, throwable) -> {
        if (throwable != null) {
          log.error("Task for device {} failed.", asset.getName(), throwable.getCause());
        } else {
          log.debug("Task for device {} completed successfully.", asset.getName());
        }
        // 更新上次完成时间戳，用于限制请求频率
        lastCompletionTimes.put(key, System.currentTimeMillis());
        // 从进行中的任务列表移除
        inFlightTasks.remove(key, taskFuture);
        log.debug("Task for device {} completed and removed from in-flight map.", asset.getName());
      });
      return taskFuture;
    });
  }
}
