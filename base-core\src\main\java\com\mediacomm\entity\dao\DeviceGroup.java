package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "device_group", description = "设备分组")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeviceGroup extends SkyLinkDbEntity {
  @Schema(hidden = true)
  @TableId(value = "group_id", type = IdType.AUTO)
  private Integer groupId;
  private Integer groupSeq; // 分组序号
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private String groupName; // 分组名称
  private Integer iconSize = 10; // 图标大小
  private Integer colCount = 10; // 分组中单行列数
}
