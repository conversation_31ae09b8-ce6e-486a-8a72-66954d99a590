package com.mediacomm.caesar.controller;

import com.mediacomm.caesar.domain.vp7.Up7UpgradeCancelResponse;
import com.mediacomm.caesar.domain.vp7.Vp7IdParam;
import com.mediacomm.caesar.domain.vp7.Vp7Status;
import com.mediacomm.caesar.domain.vp7.Vp7UpgradeCancelParam;
import com.mediacomm.caesar.domain.vp7.Vp7UpgradeParam;
import com.mediacomm.caesar.domain.vp7.Vp7UpgradeResponse;
import com.mediacomm.caesar.domain.vp7.Vp7VersionResponse;
import com.mediacomm.entity.dao.VideoWallBanner;
import java.net.URI;
import java.util.Collection;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * vp7.
 */
@FeignClient(name = "vp7-service", url = "http://localhost")
public interface Vp7FeignClientApi {
  @GetMapping("/api/v1/status")
  Vp7Status getStatus(URI uri);

  @GetMapping("/api/v1/banner/subtitle/config")
  VideoWallBanner.Subtitle getBannerSubtitleConfig(URI uri, @RequestBody Vp7IdParam param);

  @GetMapping("/api/v1/banner/logo/image")
  byte[] getLogo(URI uri);

  @GetMapping("/api/v1/banner/logo/config")
  VideoWallBanner.Logo getLogoConfig(URI uri, @RequestBody Vp7IdParam param);

  @GetMapping("/api/v1/banner/clock/config")
  VideoWallBanner.Clock getClockConfig(URI uri, @RequestBody Vp7IdParam param);

  @GetMapping("/api/v1/banner/weather/config")
  VideoWallBanner.Weather getWeatherConfig(URI uri, @RequestBody Vp7IdParam param);

  @GetMapping("/api/v1/banner/dutyschedule/config")
  VideoWallBanner.DutySchedule getDutyScheduleConfig(URI uri, @RequestBody Vp7IdParam param);

  @GetMapping("/api/v1/bgimage")
  byte[] getBgImage(URI uri);

  @GetMapping("/api/v1/banner/config")
  Collection<VideoWallBanner.BannerContent> getBannerConfig(URI uri);

  @PostMapping("/api/v1/banner/subtitle/config")
  void setBannerSubtitleConfig(URI uri, @RequestBody VideoWallBanner.Subtitle subtitle);

  @PostMapping(value = "/api/v1/banner/logo/image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  void uploadLogo(URI uri, @RequestPart MultipartFile logo, @RequestPart String hash);

  @PostMapping("/api/v1/banner/logo/config")
  void setLogoConfig(URI uri, @RequestBody VideoWallBanner.Logo logoConfig);

  @PostMapping("/api/v1/banner/clock/config")
  void setClockConfig(URI uri, @RequestBody VideoWallBanner.Clock clockConfig);

  @PostMapping("/api/v1/banner/weather/config")
  void setWeatherConfig(URI uri, @RequestBody VideoWallBanner.Weather weatherConfig);

  @PostMapping("/api/v1/banner/dutyschedule/config")
  void setDutyScheduleConfig(URI uri, @RequestBody VideoWallBanner.DutySchedule dutyScheduleConfig);

  @PostMapping(value = "/api/v1/bgimage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  void uploadBgImage(URI uri, @RequestPart MultipartFile image, @RequestPart String hash);

  @PostMapping("/api/v1/banner/config")
  void setBannerConfig(URI uri, @RequestBody VideoWallBanner.BannerContent banner);

  /**
   * 接收通知并下载升级包
   */
  @PostMapping("/api/v1/ota/start")
  Vp7UpgradeResponse startOta(URI uri, @RequestBody Vp7UpgradeParam requestBody);

  /**
   * 取消升级.
   */
  @PostMapping("/api/v1/ota/cancel")
  Up7UpgradeCancelResponse cancelOta(URI uri, @RequestBody Vp7UpgradeCancelParam requestBody);

  /**
   * 查询升级进度.
   */
  @GetMapping("/api/v1/ota/status")
  Vp7UpgradeResponse getOtaStatus(URI uri);

  /**
   * 获取版本信息.
   */
  @GetMapping("/api/v1/version")
  Vp7VersionResponse getVersion(URI uri);
}
