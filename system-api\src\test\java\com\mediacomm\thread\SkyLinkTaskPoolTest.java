package com.mediacomm.thread;

import com.mediacomm.util.task.SkyLinkTaskPool;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * .
 *
 * @author: Wu<PERSON><PERSON><PERSON>ie
 */
@SpringBootTest
public class SkyLinkTaskPoolTest {
  @Autowired
  SkyLinkTaskPool skyLinkTaskPool;

  @Test
  public void add() {
    Runnable runnable = new Runnable() {
      @Override
      public void run() {
        System.out.println("My Thread-1!");
      }
    };
    skyLinkTaskPool.addTask(runnable);
  }
}
