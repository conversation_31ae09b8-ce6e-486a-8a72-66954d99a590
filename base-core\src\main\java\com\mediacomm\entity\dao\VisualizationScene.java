package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.VisualizationSceneType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "visualization_scene", description = "可视化场景")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VisualizationScene extends SkyLinkDbEntity {
  @Schema(hidden = true)
  @TableId(value = "scene_id", type = IdType.AUTO)
  private Integer sceneId; // 可视化场景Id
  @NotNull
  private String name; // 场景名称
  private Integer deviceModel; // 场景对应的设备类型
  private Integer roomId; // 所属房间Id
  @TableField(exist = false)
  private String roomName; // 房间名称
  @NotNull
  @TableField(typeHandler = JacksonTypeHandler.class)
  private Object data; // 场景内的页面数据
  private VisualizationSceneType sceneType; // 场景功能
  private Long configTime; // 更新时间
}
