import hashlib
import base64

def base64_md5_encrypt(input_string):
    """
    Encrypts the input string using Base64 encoding followed by MD5 hashing.

    Args:
        input_string (str): The string to be encrypted.

    Returns:
        str: The Base64 encoded and MD5 hashed string.
    """
    # 创建MD5哈希对象
    encode_str = input_str.encode('utf-8')
    md5_str = hashlib.md5(encode_str).hexdigest()
    str_bytes = md5_str.encode('utf-8')
    str_base64 = base64.b64encode(str_bytes)
    return str_base64.decode('utf-8')


# Example usage
if __name__ == "__main__":
    # skylink  NTdl  MGRhODNi
    input_str = "1689586062335" + "ZmY4"
    encrypted_str = base64_md5_encrypt(input_str)
    # 1693976746123

    print(f"sign: {encrypted_str}")
