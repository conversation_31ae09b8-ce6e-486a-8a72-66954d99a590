package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.ExtOptionTypHandler;
import com.mediacomm.config.db.ListExtPropertyJsonTypeHandler;
import com.mediacomm.config.db.ListPropertyJsonTypeHandler;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.vo.ExtPropertyVo;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "device_model", description = "设备型号")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeviceModel extends SkyLinkDbEntity {
  @TableId(value = "model_id", type = IdType.AUTO)
  private Integer modelId; // 型号Id
  private String deviceType; // 类型
  private String modelName; // 名称
  private String manufacturer; // 厂商
  private Integer maxUseAge; // 最大使用年限
  private SubSystemType subSystem; // 所属的子系统
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> properties = new ArrayList<>(); // 内部扩展属性
  @TableField(typeHandler = ListExtPropertyJsonTypeHandler.class)
  private List<ExtPropertyVo> extProperties = new ArrayList<>(); // 表单可配置扩展属性
  @TableField(typeHandler = ExtOptionTypHandler.class)
  private List<ExtOption> extOption = new ArrayList<>(); // 型号扩展配置
}
