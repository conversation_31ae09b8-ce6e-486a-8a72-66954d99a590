package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mediacomm.entity.dao.KvmUser;
import com.mediacomm.entity.vo.KvmUserVo;
import java.util.Collection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * Kvm用户.
 */
@Mapper
public interface KvmUserMapper extends BaseMapper<KvmUser> {
  /**
   * 关联查询所有KVM用户信息.
   *
   * @return .
   */
  @Select(" select ku.*, km.name as master_name from kvm_user as ku, kvm_master as km "
      + "where ku.master_id = km.master_id")
  Collection<KvmUserVo> findAll();

  /**
   * 通过主机Id筛选KVM用户信息.
   *
   * @param masterId 主机Id.
   * @return .
   */
  @Select("select ku.*, km.name as master_name from kvm_user ku join kvm_master km "
      + "on ku.master_id = km.master_id where km.master_id = #{masterId}")
  Collection<KvmUserVo> findAllByMasterId(String masterId);
}
