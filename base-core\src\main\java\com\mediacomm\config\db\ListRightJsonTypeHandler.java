package com.mediacomm.config.db;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.entity.dao.Right;
import com.mediacomm.util.JsonUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * .
 */
@Slf4j
@MappedTypes({List.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class ListRightJsonTypeHandler extends AbstractJsonTypeHandler<List<Right>> {
  @Override
  protected List<Right> parse(String json) {
    return JsonUtils.decode(json, new TypeReference<>() {
    });
  }

  @Override
  protected String toJson(List<Right> obj) {
    return JsonUtils.encode(obj);
  }
}
