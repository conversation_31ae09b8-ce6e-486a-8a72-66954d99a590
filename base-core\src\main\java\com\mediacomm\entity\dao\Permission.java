package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mediacomm.config.db.ListRightJsonTypeHandler;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "permission", description = "权限")
public class Permission extends SkyLinkDbEntity {
  @TableId(value = "menu_id", type = IdType.AUTO)
  private Integer menuId;
  private String name;
  private String title;
  @TableField(typeHandler = ListRightJsonTypeHandler.class)
  private List<Right> rights;
  private Integer pid; // 父级Id
}
