package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.SyncWallGroup;
import java.util.Collection;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

/**
 * .
 */
@Mapper
public interface SyncWallGroupMapper extends BaseMapper<SyncWallGroup> {
  @Select("select wall.*, master.`name` as master_name, pg.`name` as room_name "
      + "from kvm_video_wall as wall "
      + "inner join kvm_master as master on (wall.master_id = master.master_id) "
      + "inner join position_group as pg on (pg.position_id = wall.room_id) "
      + "where wall.wall_id in ( select wid from sync_wall_group_merge where sid = #{syncGroupId})")
  @Results(id = "videoWallMap", value = {
      @Result(property = "decoders", column = "decoders", typeHandler = JacksonTypeHandler.class),
      @Result(property = "properties", column = "properties",
          typeHandler = JacksonTypeHandler.class)
  })
  Collection<KvmVideoWall> findWallsById(Integer syncGroupId);

  @Insert({"<script>",
      "insert into sync_wall_group_merge (sid, wid) VALUES ",
      "<foreach collection='wallIds' item='wid' separator=','>",
      "(#{sid}, #{wid})",
      "</foreach>",
      "</script>"})
  void insertToMerge(Integer sid, Collection<Integer> wallIds);

  @Delete("delete from sync_wall_group_merge where sid = #{sid} and wid = #{wid} ")
  void delFromMerge(Integer sid, Integer wid);

}
