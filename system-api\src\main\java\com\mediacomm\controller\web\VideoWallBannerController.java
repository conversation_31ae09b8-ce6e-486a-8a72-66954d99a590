package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.VideoWallBanner;
import com.mediacomm.entity.message.reqeust.body.VwBannerRequestBody;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.service.VideoWallBannerService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.BannerType;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.BuildMqRequestBodyUtils;
import com.mediacomm.util.RpcSenderUtils;
import com.mediacomm.util.WebSocketHandlerSendNoticeUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Slf4j
@Tag(name = "大屏横幅配置")
@RestController
@RequestMapping(ResUrlDef.VIS_WALL_BANNER)
public class VideoWallBannerController extends SkyLinkController<VideoWallBanner,
        VideoWallBannerService> {
  @Autowired
  private KvmVideoWallService videoWallService;
  @Autowired
  private RpcSenderUtils senderUtils;
  @Autowired
  private WebSocketHandlerSendNoticeUtils noticeUtils;

  @OperationLogRecord(title = "创建横幅", operateType = OperateType.INSERT,
          requestBody = "#{banner}")
  @Operation(summary = "创建横幅")
  @PostMapping
  public Result<VideoWallBanner> addVideoWallBanner(@RequestBody VideoWallBanner.BannerContent bannerContent,
                                           @RequestParam Integer videoWallId) {
    KvmVideoWall wall = videoWallService.getById(videoWallId);
    if (wall == null) {
      return Result.failure("No exist videoWall!", ResponseCode.EX_FAILURE_400);
    }
    if (wall.getBannerType() != BannerType.NON_SUPPORT) {
      Collection<VideoWallBanner> banners = service.listByVideoWallId(videoWallId);
      if (banners.size() >= 20) {
        return Result.failure("The number of banners cannot exceed 5!", ResponseCode.OUT_OF_LIMIT_13005);
      }
      VideoWallBanner banner = new VideoWallBanner();
      banner.setWallId(videoWallId);
      banner.setContent(bannerContent);
      service.save(banner);
      return Result.ok(banner);
    } else {
      return Result.failure("This videoWall does not support banner!", ResponseCode.EX_FAILURE_400);
    }
  }

  @OperationLogRecord(title = "修改横幅", operateType = OperateType.INSERT,
          requestBody = "#{banner}")
  @Operation(summary = "修改横幅")
  @PutMapping("/{bannerId}")
  public Result<String> updateVideoWallBanner(@RequestBody VideoWallBanner.BannerContent bannerContent,
                                              @PathVariable Integer bannerId) {
    VideoWallBanner banner = service.getById(bannerId);
    if (banner == null) {
      return Result.failure("No exist banner!", ResponseCode.EX_FAILURE_400);
    }
    // 条幅在运行状态下，发生修改时才有下发配置的需要，否则只需要更新数据库中条幅的数据
    banner.setContent(bannerContent);
    service.updateById(banner);
    if (banner.isStatus()) {
      KvmVideoWall videoWall = videoWallService.getById(banner.getWallId());
      if (videoWall != null) {
        VwBannerRequestBody requestBody = new VwBannerRequestBody();
        requestBody.setWallId(videoWall.getWallId());
        requestBody.setBannerId(bannerId);
        requestBody.setEnable(banner.isStatus());
        senderUtils.send(videoWall.getMasterId(), RoutingOperation.BANNER_SET,
                BuildMqRequestBodyUtils.buildVideoWallMqBody(videoWall, requestBody));
        noticeUtils.sendBannerChangeNotice(videoWall.getWallId());
      } else {
        return Result.failure("The videoWall is not exist!",
                ResponseCode.EX_FAILURE_400);
      }
    }
    return Result.ok();
  }

  @Operation(summary = "获取大屏横幅")
  @GetMapping("/banners")
  public Result<Collection<VideoWallBanner>> getVideoWallBanner(@RequestParam Integer videoWallId) {
    return Result.ok(service.listByVideoWallId(videoWallId));
  }

  @Operation(summary = "删除横幅")
  @DeleteMapping("/{bannerId}")
  public Result<String> delVideoWallBanner(@PathVariable Integer bannerId) {
    service.removeById(bannerId);
    return Result.ok();
  }
}
