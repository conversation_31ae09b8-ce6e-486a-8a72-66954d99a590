package com.mediacomm.entity.message.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 外设升级进度响应.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PeripheralUpgradeProgressResponse {
  private String deviceId; // 设备ID
  private Integer taskId; // 任务ID
  private Integer progress; // 进度(0-100)
  private Integer status; // 状态
  private String errorMessage; // 错误信息
}
