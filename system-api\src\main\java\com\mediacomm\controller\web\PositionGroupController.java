package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.PositionGroup;
import com.mediacomm.entity.dao.PositionLevel;
import com.mediacomm.entity.vo.PositionGroupVo;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.PositionGroupService;
import com.mediacomm.system.service.PositionLevelService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.sysenum.OperateType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "房间位置配置", description = "房间位置配置的API")
@RestController
@RequestMapping(ResUrlDef.POSITION_GROUP)
public class PositionGroupController extends
    SkyLinkController<PositionGroup, PositionGroupService> {

  @Autowired
  PositionLevelService levelService;

  @Operation(summary = "获取所有房间")
  @Parameter(name = "parentId", description = "根据父级房间Id获取父级房间内的房间")
  @GetMapping("/position-groups")
  public Result<Collection<PositionGroupVo>> getPositionGroups(
      @RequestParam(value = "parentId", required = false) Integer parentId) {
    return Result.ok(service.all(parentId));
  }

  @Operation(summary = "获取所有房间等级信息")
  @GetMapping("/levels")
  public Result<Collection<PositionLevel>> getPostionLevels() {
    return Result.ok(levelService.list());
  }

  /**
   * 修改房间.
   */
  @OperationLogRecord(title = "修改房间", operateType = OperateType.UPDATE,
      requestBody = "#{group}")
  @Operation(summary = "根据Id修改指定房间信息")
  @PutMapping("/{id}")
  public Result<?> updatePositionGroup(@PathVariable Integer id, @RequestBody PositionGroup group) {
    group.setPositionId(id);
    service.updateById(group);
    return Result.ok();
  }

  @OperationLogRecord(title = "删除房间", operateType = OperateType.DELETE)
  @Operation(summary = "根据Id删除指定房间信息")
  @DeleteMapping("/{id}")
  public Result<?> delPositionGroup(@PathVariable Integer id) {
    service.removeById(id);
    return Result.ok();
  }

  @OperationLogRecord(title = "新增房间", operateType = OperateType.INSERT,
      requestBody = "#{group}")
  @Operation(summary = "新增房间信息")
  @PostMapping
  public Result<?> addPositionGroup(@RequestBody PositionGroup group) {
    service.save(group);
    return Result.ok();
  }
}
