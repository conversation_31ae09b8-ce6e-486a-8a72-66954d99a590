package com.mediacomm.aircon.util.mapper;

import com.mediacomm.aircon.domain.AirconDeviceSameField;
import com.mediacomm.aircon.domain.AirconKvmDecoder;
import com.mediacomm.aircon.domain.AirconPanelRect;
import com.mediacomm.aircon.domain.AirconProperty;
import com.mediacomm.aircon.domain.AirconRx;
import com.mediacomm.aircon.domain.AirconSeat;
import com.mediacomm.aircon.domain.AirconTx;
import com.mediacomm.aircon.domain.AirconVideoWall;
import com.mediacomm.aircon.domain.AirconVwDecoder;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.KvmSeatDecoder;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.KvmVideoWallDecoder;
import com.mediacomm.entity.dao.Version;
import com.mediacomm.entity.message.LayerData;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.SkyLinkStringUtil;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.BeforeMapping;
import org.mapstruct.Context;
import org.mapstruct.MappingTarget;
import org.springframework.stereotype.Component;


/**
 * .
 */
@Component
@Slf4j
public class AirconEntityMapperResolver {

  @Resource
  private KvmAssetService assetService;

  @BeforeMapping
  public void toKvmAssetBefore(@NotNull AirconTx airconTx, @MappingTarget KvmAsset kvmAsset,
                               @Context String masterId) {
    initializeCommonAssetData(kvmAsset, airconTx, masterId);
    kvmAsset.setDeviceModel(DeviceType.AIRCON_ENCODER.getDeviceTypeId());
  }

  @BeforeMapping
  public void toKvmAssetBefore(@NotNull AirconRx airconRx, @MappingTarget KvmAsset kvmAsset,
                               @Context String masterId) {
    initializeCommonAssetData(kvmAsset, airconRx, masterId);
    switch (airconRx.getDeviceType()) {
      case "aircon-kvm" ->
              kvmAsset.setDeviceModel(DeviceType.AIRCON_SEAT_RX.getDeviceTypeId());
      case "aircon-video-wall" ->
              kvmAsset.setDeviceModel(DeviceType.AIRCON_VIDEO_WALL_RX.getDeviceTypeId());
      default -> log.warn(String.format("Undefined aircon deviceType! %s %s",
              airconRx.getName(), airconRx.getDeviceType()));
    }
  }

  @BeforeMapping
  public void toKvmVideoWallBefore(@NotNull AirconVideoWall airconVideoWall,
                                   @MappingTarget KvmVideoWall kvmVideoWall,
                                   @Context String masterId) {
    kvmVideoWall.setMasterId(masterId);
    kvmVideoWall.setDeviceModel(DeviceType.AIRCON_VIDEO_WALL.getDeviceTypeId());
    kvmVideoWall.setUniqueSearchKey(String.join(".", masterId,
            String.valueOf(airconVideoWall.getId())));
    kvmVideoWall.setDecoders(new ArrayList<>(airconVideoWall.getVwDecoders().size()));
    kvmVideoWall.setSingleW(airconVideoWall.getSingleW() * airconVideoWall.getColCount());
    kvmVideoWall.setSingleH(airconVideoWall.getSingleH() * airconVideoWall.getRowCount());
    for (AirconVwDecoder vwDecoder : airconVideoWall.getVwDecoders()) {
      KvmVideoWallDecoder videoWallDecoder = new KvmVideoWallDecoder();
      videoWallDecoder.setDeviceId(vwDecoder.getDeviceId());
      List<AirconProperty> property = vwDecoder.getProperties();
      for (AirconProperty airconProperty : property) {
        if (airconProperty.getKey().equals("height")) {
          videoWallDecoder.setHeight(Integer.parseInt(airconProperty.getValue()));
        } else if (airconProperty.getKey().equals("width")) {
          videoWallDecoder.setWidth(Integer.parseInt(airconProperty.getValue()));
        } else if (airconProperty.getKey().equals("xpos")) {
          videoWallDecoder.setXpos(Integer.parseInt(airconProperty.getValue()));
        } else if (airconProperty.getKey().equals("ypos")) {
          videoWallDecoder.setYpos(Integer.parseInt(airconProperty.getValue()));
        }
      }
      kvmVideoWall.getDecoders().add(videoWallDecoder);
    }
  }

  @BeforeMapping
  public void toKvmSeatBefore(@NotNull AirconSeat airconSeat,
                              @MappingTarget KvmSeat kvmSeat,
                              @Context String masterId) {
    kvmSeat.setMasterId(masterId);
  }

  @BeforeMapping
  public void toKvmSeatDecoderBefore(@NotNull AirconKvmDecoder airconKvmDecoder,
                                      @MappingTarget KvmSeatDecoder kvmSeatDecoder,
                                      @Context String masterId) {
    KvmAssetVo rx = assetService.oneByDeviceId(airconKvmDecoder.getDeviceId(), masterId);
    if (rx != null) {
      kvmSeatDecoder.setDeviceType(rx.getDeviceType());
      kvmSeatDecoder.setDeviceName(rx.getName());
      kvmSeatDecoder.setAssetId(rx.getAssetId());
    }
  }

  @BeforeMapping
  public void toPanelRectBefore(@NotNull AirconPanelRect airconPanelRect,
                                @MappingTarget PanelRect panelRect,
                                @Context String masterId) {
    KvmAssetVo tx = assetService.oneByDeviceId(airconPanelRect.getVideoSrcId(), masterId);
    if (tx != null) {
      panelRect.addFullScreenLayer(tx, airconPanelRect.getCtrlMode());
    }
  }

  @BeforeMapping
  public void toAirconPanelRectBefore(@NotNull PanelRect panelRect,
                                      @MappingTarget AirconPanelRect airconPanelRect) {
    KvmAssetVo tx = assetService.oneById(panelRect.getVideoSrcId());
    if (tx != null) {
      airconPanelRect.setVideoSrcId(tx.getDeviceId());
    }
  }

  @BeforeMapping
  public void toLayerDataBefore(@NotNull AirconPanelRect airconPanelRect,
                                @MappingTarget LayerData layerData,
                                @Context String masterId) {
    KvmAssetVo tx = assetService.oneByDeviceId(airconPanelRect.getVideoSrcId(), masterId);
    if (tx != null) {
      layerData.setVideoSrcId(tx.getAssetId());
      layerData.setVideoSrcName(tx.getName());
      layerData.setDeviceType(tx.getDeviceType());
    }
  }

  private static void initializeCommonAssetData(KvmAsset kvmAsset, AirconDeviceSameField device, String masterId) {
    kvmAsset.setAssetId(SkyLinkStringUtil.uuid());
    kvmAsset.setMasterId(masterId);
    kvmAsset.resetVersion();
    kvmAsset.getVersion().add(new Version("app", device.getSoftVersion(), ""));
    for (AirconProperty property : device.getProperties()) {
      kvmAsset.getProperties().add(new Property(property.getKey(), property.getValue()));
    }
  }
}
