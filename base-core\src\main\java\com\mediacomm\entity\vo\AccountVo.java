package com.mediacomm.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.entity.dao.Account;
import com.mediacomm.entity.dao.Role;
import java.util.Collection;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class AccountVo extends Account {
  private String personnelName;
  private boolean personnelEnable;
  private String departmentName;
  private Integer departmentId;
  private String jobNumber;
  private Collection<Role> roles;

  /**
   * Account to AccountVo.
   *
   * @param account .
   */
  public AccountVo(Account account) {
    setAccountId(account.getAccountId());
    setAccountName(account.getAccountName());
    setAccountDesc(account.getAccountDesc());
    setAccountCreator(account.getAccountCreator());
    setAccountEnable(account.isAccountEnable());
    setBusinessAuth(account.getBusinessAuth());
    setAccountHomePage(account.getAccountHomePage());
    setAccountPassword(account.getAccountPassword());
    setPersonnel(account.getPersonnel());
    setAccountCreateTime(account.getAccountCreateTime());
  }
}
