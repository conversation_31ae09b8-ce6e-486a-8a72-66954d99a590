package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.VisualizationLayout;
import java.util.Collection;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

/**
 * Kvm坐席.
 */
@Mapper
public interface KvmSeatMapper extends BaseMapper<KvmSeat> {
  @Select("select * from visualization_layout where layout_id "
      + "in (select lid from seat_layout_merge WHERE sid = #{seatId});")
  @Results(id = "layoutMap", value = {
      @Result(property = "layoutData", column = "layout_data",
          typeHandler = JacksonTypeHandler.class),
  })
  Collection<VisualizationLayout> findLayoutBySeatId(Integer seatId);

  @Insert({"<script>",
      "insert into seat_layout_merge (sid, lid) VALUES ",
      "<foreach collection='layoutIds' item='lid' separator=','>",
      "(#{seatId}, #{lid})",
      "</foreach>",
      "</script>"})
  boolean insertToMerge(Integer seatId, Collection<Integer> layoutIds);

  @Delete({"<script>",
      "delete from seat_layout_merge where sid = #{seatId} and lid in ",
      "<foreach collection='layoutIds' item='layoutId' open='(' separator=',' close=')'>",
      "#{layoutId}",
      "</foreach>",
      "</script>"})
  boolean delFromMerge(Integer seatId, Collection<Integer> layoutIds);
}
