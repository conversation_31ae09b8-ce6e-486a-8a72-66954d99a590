package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.VideoWallScene;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.VideoWallSceneMapper;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class VideoWallSceneService extends
    SkyLinkServiceImpl<VideoWallSceneMapper, VideoWallScene> {

  /**
   * 通过大屏Id获取相关的预案.
   *
   * @param wallId 大屏Id.
   * @return Collection.
   */
  public Collection<VideoWallScene> allByWallId(Integer wallId) {
    LambdaQueryWrapper<VideoWallScene> wrapper = Wrappers.lambdaQuery(VideoWallScene.class)
        .eq(VideoWallScene::getWallId, wallId);
    return list(wrapper);
  }

  public void setPollingScenesEnableValue(Integer sceneId, boolean enable) {
    baseMapper.updatePollingScenesEnableValue(sceneId, enable);
  }
}
