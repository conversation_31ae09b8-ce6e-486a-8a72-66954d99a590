package com.mediacomm.config.rabbitmqtt;

import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.messaging.MessageHandler;

/**
 * mqtt消息生产者配置.
 */
@Configuration
public class RabbitMQTTConfig {
  @Value("${spring.mqtt.url}")
  private String hostUrl;

  @Value("${spring.mqtt.client.id}")
  private String clientId;

  @Value("${spring.mqtt.default.topic}")
  private String defaultTopic;

  @Value("${spring.mqtt.userName}")
  private String userName;

  @Value("${spring.mqtt.password}")
  private String password;

  @Bean
  public MqttPahoClientFactory mqttClientFactory() {
    MqttConnectOptions mqttConfig = new MqttConnectOptions();
    mqttConfig.setServerURIs(new String[] {hostUrl});
    mqttConfig.setUserName(userName);
    mqttConfig.setPassword(password.toCharArray());
    mqttConfig.setKeepAliveInterval(2);
    mqttConfig.setAutomaticReconnect(true);
    DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
    factory.setConnectionOptions(mqttConfig);
    return factory;
  }

  @Bean
  @ServiceActivator(inputChannel = "mqttOutboundChannel")
  public MessageHandler mqttOutbound() {
    MqttPahoMessageHandler mqttClientFactory =
        new MqttPahoMessageHandler(clientId, mqttClientFactory());
    mqttClientFactory.setAsync(true);
    mqttClientFactory.setDefaultTopic(defaultTopic);
    return mqttClientFactory;
  }

}
