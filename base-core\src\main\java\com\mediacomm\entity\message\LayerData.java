package com.mediacomm.entity.message;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LayerData implements Serializable {
  private int seq;
  private int ctrlMode = 2;
  private String videoSrcId;
  private String videoSrcName;
  private String deviceType;
  private boolean enableAudio;
}
