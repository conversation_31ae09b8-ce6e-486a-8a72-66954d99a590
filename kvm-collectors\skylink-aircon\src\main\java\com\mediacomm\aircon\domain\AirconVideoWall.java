package com.mediacomm.aircon.domain;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * AirconVideoWall.
 */
@Data
public class AirconVideoWall {
  private int id = 0;
  private String name;
  private int rowCount = 2;
  private int colCount = 4;
  private int singleW = 1920; // 单个屏幕的宽
  private int singleH = 1080; // 单个屏幕的高
  private List<AirconVwDecoder> vwDecoders = new ArrayList<>();
}
