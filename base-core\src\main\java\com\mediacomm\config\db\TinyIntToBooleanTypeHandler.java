package com.mediacomm.config.db;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * 兼容kingbase对mysql中的tinyInt类型转化为boolean类型.
 */
@MappedTypes({Boolean.class})
@MappedJdbcTypes({JdbcType.TINYINT})
public class TinyIntToBooleanTypeHandler extends BaseTypeHandler<Boolean> {
  @Override
  public void setNonNullParameter(PreparedStatement preparedStatement, int i, Boolean aBoolean,
                                  JdbcType jdbcType) throws SQLException {
    preparedStatement.setInt(i, Boolean.TRUE.equals(aBoolean) ? 1 : 0 );
  }

  @Override
  public Boolean getNullableResult(ResultSet resultSet, String columnName) throws SQLException {
    int anInt = resultSet.getInt(columnName);
    return Integer.valueOf("1").equals(anInt);
  }

  @Override
  public Boolean getNullableResult(ResultSet resultSet, int columnIndex) throws SQLException {
    int anInt = resultSet.getInt(columnIndex);
    return Integer.valueOf("1").equals(anInt);
  }

  @Override
  public Boolean getNullableResult(CallableStatement callableStatement, int columnIndex)
      throws SQLException {
    int anInt = callableStatement.getInt(columnIndex);
    return Integer.valueOf("1").equals(anInt);
  }
}
