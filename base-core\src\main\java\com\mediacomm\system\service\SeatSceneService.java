package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.SeatScene;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.SeatSceneMapper;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class SeatSceneService extends SkyLinkServiceImpl<SeatSceneMapper, SeatScene> {

  public Collection<SeatScene> allByKey(String key) {
    LambdaQueryWrapper<SeatScene> wrapper = Wrappers.lambdaQuery(SeatScene.class)
            .eq(SeatScene::getUniqueSearchKey, key);
    return list(wrapper);
  }

  public void delByKey(String key) {
    LambdaQueryWrapper<SeatScene> wrapper = Wrappers.lambdaQuery(SeatScene.class)
            .eq(SeatScene::getUniqueSearch<PERSON>ey, key);
    remove(wrapper);
  }
}
