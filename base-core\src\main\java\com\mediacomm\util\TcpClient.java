package com.mediacomm.util;

import com.mediacomm.system.exception.NetException;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * .
 */
public class TcpClient implements Closeable {

  private final Socket socket;
  private final Lock lock = new ReentrantLock(true);

  /**
   * .
   */
  public TcpClient(String host, int port) throws IOException {
    SocketAddress address = new InetSocketAddress(host, port);
    socket = new Socket();
    socket.connect(address, 2000);
    socket.setSoTimeout(2000);
    socket.setKeepAlive(true);
  }

  /**
   * .
   */
  public void write(byte[] data, int offset, int length) throws IOException {
    this.lock.lock();
    try {
      OutputStream out = socket.getOutputStream();
      out.write(data, offset, length);
      out.flush();
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   */
  public void write(byte[] buff) throws IOException {
    this.lock.lock();
    try {
      OutputStream out = socket.getOutputStream();
      out.write(buff);
      out.flush();
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   */
  public OutputStream getOutputStream() throws NetException {
    try {
      return socket.getOutputStream();
    } catch (IOException e) {
      throw new NetException(e);
    }
  }

  /**
   * .
   */
  public int read(byte[] buff, int offset, int length) throws NetException {
    this.lock.lock();
    try {
      InputStream in = socket.getInputStream();
      return in.read(buff, offset, length);
    } catch (IOException e) {
      throw new NetException(e);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   */
  public int read(byte[] data) throws IOException {
    this.lock.lock();
    try {
      InputStream in = socket.getInputStream();
      return in.read(data);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   */
  public InputStream getInputStream() throws NetException {
    try {
      return socket.getInputStream();
    } catch (IOException e) {
      throw new NetException(e);
    }
  }

  /**
   * .
   */
  public boolean isClosed() {
    return socket.isClosed();
  }

  /**
   * 关闭tcp.
   */
  public void close() throws IOException {
    socket.close();
  }
}
