package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.DeviceModel;
import com.mediacomm.entity.dao.EnvDevice;
import com.mediacomm.entity.dao.NetLinkMerge;
import com.mediacomm.entity.vo.EnvDeviceVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.EnvDeviceMapper;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
@Slf4j
public class EnvDeviceService extends SkyLinkServiceImpl<EnvDeviceMapper, EnvDevice> {

  @Autowired
  DeviceModelService dm;

  public Collection<EnvDeviceVo> allByDeviceType(String deviceType) {
    return baseMapper.findALlByDeviceType(deviceType);
  }

  public Collection<EnvDeviceVo> allBySubSystemType(SubSystemType subSystemType) {
    return baseMapper.findALlBySubSystemType(subSystemType);
  }

  public void saveNetLinkPort(NetLinkMerge netLinkMerge) {
    baseMapper.insertNetLinkMerge(netLinkMerge);
  }

  public Collection<NetLinkMerge> allNetLinkAssoByNetId(String netDeviceId) {
    return baseMapper.finalAllNetLinkAssoByNetId(netDeviceId);
  }

  public void removeNetLinkAssoByNetIdAndPortNum(String netDeviceId, Integer portNum) {
    baseMapper.delNetLinkMerge(netDeviceId, portNum);
  }

  public EnvDeviceVo oneById(String id) {
    EnvDeviceVo vo = Optional.ofNullable(getById(id)).map(EnvDeviceVo::new).orElse(null);
    Optional.ofNullable(vo).ifPresent(this::addEnvDeviceInfo);
    return vo;
  }

  public EnvDeviceVo oneBySnAndMac(String sn, String mac) {
    String hardcode = switcherHardcode(sn, mac);
    LambdaQueryWrapper<EnvDevice> wrapper =
        Wrappers.lambdaQuery(EnvDevice.class).eq(EnvDevice::getHardcode, hardcode);
    EnvDeviceVo vo = Optional.ofNullable(getOne(wrapper)).map(EnvDeviceVo::new).orElse(null);
    Optional.ofNullable(vo).ifPresent(this::addEnvDeviceInfo);
    return vo;
  }

  /**
   * 查询超过一分钟未注册成功的Switcher设备.
   */
  public Collection<EnvDeviceVo> allUnregisterSwitcher() {
    Collection<EnvDeviceVo> unregister = new ArrayList<>();
    Collection<EnvDeviceVo> allByDeviceType =
        baseMapper.findALlByDeviceType(DeviceType.SWITCHER_TX.getDeviceType());
    long currentTimeMillis = System.currentTimeMillis();
    for (EnvDeviceVo vo : allByDeviceType) {
      String registerTime = Property.findValueByKey(vo.getProperties(), "last_register_time", "0");
      long time = 0;
      try {
        time = Long.parseLong(registerTime);
      } catch (NumberFormatException ex) {
        log.warn("last_register_time is not number", ex);
      }
      if (currentTimeMillis - time < 1000 * 60) {
        continue;
      }
      String registerAck = Property.findValueByKey(vo.getProperties(), "register_ack", "false");
      if (!Boolean.parseBoolean(registerAck)) {
        unregister.add(vo);
      }
    }
    return unregister;
  }

  private void addEnvDeviceInfo(EnvDeviceVo vo) {
    DeviceModel model = dm.getById(vo.getDeviceModel());
    Optional.ofNullable(model).ifPresent(m -> {
      vo.setModelName(model.getModelName());
      vo.setDeviceType(model.getDeviceType());
    });
  }

  private String switcherHardcode(String sn, String mac) {
    return "SWITCHER" + "." + sn + "." + mac;
  }
}
