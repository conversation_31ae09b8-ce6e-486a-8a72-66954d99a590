package com.mediacomm.event.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.domain.kaito.CardCategoryEnum;
import com.mediacomm.domain.kaito.KaitoValue;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.entity.vo.KvmMasterVo;
import com.mediacomm.entity.vo.KvmSlotVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmSlotService;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Map;
import java.util.Optional;

/**
 * .
 */
public class KaitoListener extends EvenListener {
  @Resource
  protected KvmMasterService kvmMasterService;
  @Resource
  protected KvmAssetService assetService;
  @Resource
  protected KvmSlotService slotService;
  @Resource
  RedisUtil redisUtil;

  @Override
  public String getValue(String request) {
    MqRequest req = JsonUtils.decode(request, new TypeReference<>() {
    });
    KvmMasterVo master = kvmMasterService.oneById(req.getMasterId());
    if (master == null) {
      return Result.failureStr("Kaito device no exist.", ResponseCode.EX_FAILURE_400);
    }
    Collection<KvmAssetVo> assets = assetService.allByMasterId(req.getMasterId()).stream()
            .sorted(Comparator.comparing(KvmAssetVo::getDeviceId)).toList();
    Collection<KvmSlotVo> slots = slotService.allByMaster(req.getMasterId()).stream()
            .sorted(Comparator.comparing(KvmSlotVo::getDeviceId)).toList();
    String masterRedisKey = RedisSignalKey.getDeviceStatusKey(
            master.getDeviceType().getDeviceType(), master.getMasterId());
    Optional<Map<String, String>> deviceSignalValue = redisUtil.hmget(masterRedisKey);
    if (deviceSignalValue.isPresent()) {
      KaitoValue value = new KaitoValue();
      value.setMasterId(master.getMasterId());
      value.setMasterName(master.getName());
      value.setModelName(master.getDeviceType().getDeviceType());
      Collection<DeviceSignalValue> masterDeviceSignalValueList = new ArrayList<>();
      // master signal
      addDeviceSignalValue(deviceSignalValue.get(), masterDeviceSignalValueList);
      value.setSignalValues(masterDeviceSignalValueList);
      Collection<KaitoValue.SlotSignalValue> slotSignalValues = new ArrayList<>();
      // slot signal
      slots.forEach(slot -> {
        String slotRedisKey = RedisSignalKey.getDeviceStatusKey(slot.getDeviceType(), slot.getSlotId());
        String slotDescRedisKey = RedisSignalKey.getDeviceDecKey(slot.getDeviceType(), slot.getSlotId());
        Optional<Map<String, String>> slotSignalValue = redisUtil.hmget(slotRedisKey);
        Optional<Map<String, String>> slotDesc = redisUtil.hmget(slotDescRedisKey);
        KaitoValue.SlotSignalValue sValue = KaitoValue.SlotSignalValue.builder()
                .slotId(slot.getSlotId())
                .slotName(slot.getName())
                .modelName(slot.getModelName())
                .deviceType(DeviceType.valueOf(slot.getDeviceType()))
                .extendList(new ArrayList<>())
                .build();
        Collection<DeviceSignalValue> slotSignalValueList = new ArrayList<>();
        slotDesc.ifPresent(map -> map.forEach((k, v) -> {
          if (k.equals(RedisSignalKey.CARD_TYPE)) {
            sValue.setCardType(CardCategoryEnum.valueOf(v));
          }
        }));
        slotSignalValue.ifPresent(map -> addDeviceSignalValue(map, slotSignalValueList));
        sValue.setSignalValues(slotSignalValueList);
        slotSignalValues.add(sValue);
      });
      // asset signal
      assets.forEach(asset -> {
        String assetRedisKey = RedisSignalKey.getDeviceStatusKey(asset.getDeviceType(), asset.getAssetId());
        String assetDescKey = RedisSignalKey.getDeviceDecKey(asset.getDeviceType(), asset.getAssetId());
        Optional<Map<String, String>> assetSignalValue = redisUtil.hmget(assetRedisKey);
        Optional<Map<String, String>> assetDesc = redisUtil.hmget(assetDescKey);
        KaitoValue.ExtendSignalValue extendSignalValue = KaitoValue.ExtendSignalValue.builder()
                .assetId(asset.getAssetId())
                .deviceType(DeviceType.valueOf(asset.getDeviceType()))
                .assetName(asset.getName())
                .modelName(asset.getModelName())
                .signalValues(new ArrayList<>())
                .build();
        assetSignalValue.ifPresent(map -> addDeviceSignalValue(map, extendSignalValue.getSignalValues()));
        assetDesc.ifPresent(map -> map.forEach((k, v) -> {
          if (k.equals(RedisSignalKey.CARD_POSITION)) {
            slotSignalValues.forEach(s -> {
              if (s.getSlotId().equals(v)) {
                s.getExtendList().add(extendSignalValue);
              }
            });
          }
        }));
      });
      value.setSlots(slotSignalValues);
      return Result.okStr(value);
    }
    return Result.failureStr("Data not ready.", ResponseCode.EX_NOTFOUND_404);
  }

  @Override
  public DeviceType getDeviceType() {
    return DeviceType.KAITO02;
  }
}
