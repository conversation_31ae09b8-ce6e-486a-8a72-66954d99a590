package com.mediacomm.caesar.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * PortStatus.
 */
@Data
public class PortStatus {
  private int portNumber;
  @JsonProperty("isSFPOnline")
  private boolean isSfpOnline;
  @JsonProperty("isExtOnline")
  private boolean isExtOnline;
  private String extType;
  private int id;
  private List<CaesarProperty> properties = new ArrayList<>();
}
