package com.mediacomm.entity.vo;

import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class KvmAssetVo extends KvmAsset {
  private String modelName;
  private String masterName;
  private String deviceType;
  private SubSystemType subSystemType;

  /**
   * .
   */
  public KvmAssetVo(KvmAsset kvmAsset) {
    setAssetId(kvmAsset.getAssetId());
    setAlias(kvmAsset.getAlias());
    setName(kvmAsset.getName());
    setVersion(kvmAsset.getVersion());
    setProperties(kvmAsset.getProperties());
    setCollectorProperties(kvmAsset.getCollectorProperties());
    setDeviceId(kvmAsset.getDeviceId());
    setDeviceIp(kvmAsset.getDeviceIp());
    setDeviceModel(kvmAsset.getDeviceModel());
    setHardcode(kvmAsset.getHardcode());
    setMasterId(kvmAsset.getMasterId());
  }
}
