package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.EncoderAsso;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.vo.EncoderAssoVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.AssetMapper;
import com.mediacomm.system.mapper.EncoderAssoMapper;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class EncoderAssoService extends SkyLinkServiceImpl<EncoderAssoMapper, EncoderAsso> {
  @Autowired
  AssetMapper assetMapper;
  /**
   * 根据主键ID获取关联编码器列表.
   *
   * @param masterId 主键ID.
   * @return 关联编码器列表.
   */
  public EncoderAssoVo oneByMasterId(String masterId) {
    LambdaQueryWrapper<EncoderAsso> wrapper = Wrappers.lambdaQuery(EncoderAsso.class)
        .eq(EncoderAsso::getMasterId, masterId);
    EncoderAsso e = getOne(wrapper);
    EncoderAssoVo vo = Optional.ofNullable(e).map(EncoderAssoVo::new).orElse(null);
    Optional.ofNullable(vo).ifPresent(this::addEncoderAssoInfo);
    return vo;
  }

  private void addEncoderAssoInfo(EncoderAssoVo vo) {
    KvmAsset rx = assetMapper.selectById(vo.getRxId());
    Optional.ofNullable(rx).ifPresent(r -> vo.setRxName(r.getName()));
  }
}
