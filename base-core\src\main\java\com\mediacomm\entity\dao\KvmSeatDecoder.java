package com.mediacomm.entity.dao;

import java.io.Serializable;
import lombok.Data;

/**
 * .
 */
@Data
public class KvmSeatDecoder implements Serializable {
  private Integer deviceId; // 主机内的rx ID
  private Integer channelId;
  private Integer seatId;
  private String deviceName;
  private String assetId; // 云视内的rx ID
  private String deviceType; // rx类型
  private int rowPos;
  private int colPos;
  private int height;
  private int width;
}
