package com.mediacomm.caesar.preview.strategy;


import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.preview.strategy.impl.FourScreenRxPreviewStrategy;
import com.mediacomm.caesar.preview.strategy.impl.R2P4FPreviewStrategy;
import com.mediacomm.caesar.preview.strategy.impl.SimplePreviewStrategy;

/**
 * 预览策略工厂.
 */
public class PreviewStrategyFactory {
  /**
   * 创建R2C8/4CRX/R2P4F预览策略.
   */
  public static PreviewStrategy createPreviewStrategy(DeviceDataGetter dataGetter, CaesarPreviewStrategyMode strategyMode) {
    return switch (strategyMode) {
      case PREVIEW_STRATEGY_4CRX -> new FourScreenRxPreviewStrategy(dataGetter);
      case PREVIEW_STRATEGY_R2P4F -> new R2P4FPreviewStrategy(dataGetter);
      case PREVIEW_STRATEGY_VP6 -> new SimplePreviewStrategy(dataGetter);
      default -> null;
    };
  }
}
