package com.mediacomm.caesar.task;

import com.mediacomm.caesar.preview.PreviewInfoGetter;
import com.mediacomm.caesar.preview.device.PreviewDeviceOperator;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 实时预览线程.
 */
@Slf4j
public class RealTimePreviewRunner extends PreviewThread {

  /**
   * .
   */
  public RealTimePreviewRunner(PreviewInfoGetter previewInfo,
                               DeviceDataGetter dataGetter, PreviewDeviceOperator operator) {
    this.previewInfo = previewInfo;
    this.dataGetter = dataGetter;
    this.operator = operator;
  }

  @Override
  public String getThreadName() {
    return "RealTimePreviewRunner Thread";
  }

  @Override
  protected void execute() {
    synchronized (previewInfo) {
      Collection<String> txIds = previewInfo.getRealTimePreviewTxes();
      if (!txIds.isEmpty()) {
        Map<String, KvmPreviewAsso> txIdWithReviewAsso = new HashMap<>();
        Map<String, String> txSavePathMap = new HashMap<>();
        for (String txId : txIds) {
          PreviewInfoGetter.RealTimePreviewInfo status = previewInfo.getRealTimePreviewInfo(txId);
          if (status.status == PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_RUN) {
            txSavePathMap.put(txId, dataGetter.makeTxSavePath(txId));
          } else if (status.status == PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_READY) {
            txIdWithReviewAsso.put(txId, status.channel);
            previewInfo.setRealTimePreviewStatus(
                txId, PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_ESTABLISHING);
          }
        }
        if (!txSavePathMap.isEmpty()) {
          operator.getPreview(txSavePathMap).whenComplete((stringBooleanMap, throwable) -> {
            synchronized (previewInfo) {
              for (String txId : txSavePathMap.keySet()) {
                if (!stringBooleanMap.get(txId)) {
                  log.error("RealTimePreviewRunner get preview " + txId
                      + " failed!", throwable);
                  previewInfo.setRealTimePreviewError(txId);
                } else {
                  previewInfo.clearRealTimePreviewError(txId);
                }
              }
            }
          });
        }
        if (!txIdWithReviewAsso.isEmpty()) {
          operator.openPanel(txIdWithReviewAsso).whenComplete((stringBooleanMap, throwable) -> {
            synchronized (previewInfo) {
              for (Map.Entry<String, KvmPreviewAsso> entry : txIdWithReviewAsso.entrySet()) {
                String txId = entry.getKey();
                if (stringBooleanMap.get(txId)) {
                  previewInfo.setRealTimePreviewStatus(
                      txId, PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_RUN);
                  log.info("RealTimePreviewRunner Current connections:" + txId
                      + " -> " + txIdWithReviewAsso.get(txId).getUrl());
                } else {
                  previewInfo.setRealTimePreviewStatus(
                      txId, PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_READY);
                  log.error("RealTimePreviewRunner open " + txId + " Panel failed!", throwable);
                }
              }
            }
          });
        }
      }
    }
    try {
      Thread.sleep(100);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }
  }

  @Override
  public boolean isStop() {
    return stop;
  }

  @Override
  public void stop() {
    this.stop = true;
    while(isRunning()) {
      try {
        Thread.sleep(100);
      } catch (Exception e) {
        log.error(e.getMessage(), e);
      }
    }
  }
}
