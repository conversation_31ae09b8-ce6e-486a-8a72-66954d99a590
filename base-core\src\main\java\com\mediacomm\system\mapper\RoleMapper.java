package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mediacomm.entity.dao.Role;
import java.util.Collection;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 角色.
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {
  @Insert("insert into role (name) VALUES ( #{name} )")
  boolean insertToRole(String name);

  @Select("select * from role where role_id in ("
      + "select rid from account_role_merge where aid = #{accountId})")
  Collection<Role> findRolesByAccountId(Integer accountId);

  @Insert({"<script>",
      "insert into account_role_merge (aid, rid) VALUES ",
      "<foreach collection='roleIds' item='rid' separator=','>",
      "(#{accountId}, #{rid})",
      "</foreach>",
      "</script>"})
  boolean insertToMerge(Integer accountId, Collection<Integer> roleIds);

  @Delete({"<script>",
      "delete from account_role_merge where aid = #{accountId} and rid in ",
      "<foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>",
      "#{roleId}",
      "</foreach>",
      "</script>"})
  boolean delFromMerge(Integer accountId, Collection<Integer> roleIds);

}
