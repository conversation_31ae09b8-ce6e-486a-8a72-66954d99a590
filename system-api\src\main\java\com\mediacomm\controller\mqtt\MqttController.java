package com.mediacomm.controller.mqtt;

import com.mediacomm.config.rabbitmqtt.IMqttSender;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * MqttController.
 */
@RestController
@RequestMapping("/mqtt")
public class MqttController {

  @Resource
  private IMqttSender iMqttSender;

  @PostMapping("sendMqtt")
  public String sendMsg(@RequestParam(value = "msg") String msg, @RequestParam(value = "topic") String topic){
    try {
      iMqttSender.sendToMqtt(msg, topic);
      iMqttSender.sendToMqtt(topic, 2, msg);
      return  "send successfully";
    }catch (Exception e) {
      // TODO: handle exception
      System.out.print(e);
      return "send failed";
    }
  }
}
