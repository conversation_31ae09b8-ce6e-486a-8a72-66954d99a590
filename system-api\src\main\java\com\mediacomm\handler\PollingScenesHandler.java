package com.mediacomm.handler;

import cn.hutool.system.SystemUtil;
import com.mediacomm.caesar.domain.KvmOperationFrom;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.VideoWallScene;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.OpenVwPanelsRequestBody;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.service.VideoWallSceneService;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.util.BuildMqRequestBodyUtils;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.RpcSenderUtils;
import com.mediacomm.util.task.SkyLinkTaskPool;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 轮询大屏预案.
 */
@Component
@Slf4j
public class PollingScenesHandler {
  @Resource
  private KvmVideoWallService videoWallService;
  @Resource
  private VideoWallSceneService sceneService;
  @Resource
  private RedisUtil redisUtil;
  @Resource
  private RpcSenderUtils senderUtils;
  @Resource
  private SkyLinkTaskPool taskPool;

  private boolean start = false;
  /**
   * key:wallId.
   */
  private final Map<Integer, Task> pollingTasks = new ConcurrentHashMap<>();

  @Scheduled(fixedRate = 5000)
  public void execute() {
    long updateTime = getPollingScenesTime();
    boolean flag = getPollingScenesStatusLock();
    if (updateTime == 0 && flag || System.currentTimeMillis() - updateTime > 16000) {
      start = true;
    }
    if (start) {
      updatePollingScenesStatus();
      Collection<KvmVideoWall> currentVideoWallList = videoWallService.list().stream()
            .filter(KvmVideoWall::isPollingScenesEnable).collect(Collectors.toSet());
      if (!pollingTasks.isEmpty()) {
        Collection<Integer> currentVideoWallIdList = currentVideoWallList.stream().map(KvmVideoWall::getWallId)
              .collect(Collectors.toSet());
        // 检查电视墙是否存在或者启动轮询
        for (Integer pollingTaskKey : pollingTasks.keySet()) {
          if (!currentVideoWallIdList.contains(pollingTaskKey)) {
            pollingTasks.remove(pollingTaskKey).stop();
          }
        }
        // 检查电视墙轮询任务是否存在异常退出
        for (Map.Entry<Integer, Task> integerTaskEntry : pollingTasks.entrySet()) {
          if (integerTaskEntry.getValue().getRunningStatus()) {
            log.warn("VideoWall {} run the polling task failed, do check!", integerTaskEntry.getKey());
            videoWallService.setPollingScenesValue(integerTaskEntry.getKey(), false);
            pollingTasks.remove(integerTaskEntry.getKey());
          }
        }
      }
      // 启动轮询任务
      for (KvmVideoWall videoWall : currentVideoWallList) {
        if (!pollingTasks.containsKey(videoWall.getWallId()) && videoWall.getPollingIntervalTime() > 0) {
          Task pollingScenesTask = new Task(senderUtils, videoWall);
          pollingScenesTask.resetPollingScenes(sceneService.allByWallId(videoWall.getWallId())
                  .stream().filter(VideoWallScene::isPollingScenesEnable).collect(Collectors.toSet()));
          pollingTasks.put(videoWall.getWallId(), pollingScenesTask);
          taskPool.addTask(pollingScenesTask);
        }
      }
    }

  }

  public void addPollingScenesToTask(Integer wallId) {
    Collection<VideoWallScene> scenes = sceneService.allByWallId(wallId).stream()
            .filter(VideoWallScene::isPollingScenesEnable).collect(Collectors.toSet());
    if (!scenes.isEmpty() && !pollingTasks.containsKey(wallId)) {
      addPollingScenesToTask(wallId, scenes);
    }
  }

  public void addPollingScenesToTask(Integer wallId, Collection<VideoWallScene> scenes) {
    if (pollingTasks.containsKey(wallId)) {
      log.info("Add polling scenes to task from videoWall {}", wallId);
      pollingTasks.get(wallId).resetPollingScenes(scenes);
    }
  }

  public void changePollingScenesIntervalTime(Integer wallId, int intervalSeconds) {
    if (pollingTasks.containsKey(wallId)) {
      log.info("Change polling scenes interval time {}s from videoWall {}", intervalSeconds, wallId);
      pollingTasks.get(wallId).resetPollingInterval(intervalSeconds);
    }
  }

  public void stopPollingScenesTask(Integer wallId) {
    if (pollingTasks.containsKey(wallId)) {
      log.info("Stop polling scenes task from videoWall {}", wallId);
      pollingTasks.remove(wallId).stop();
    }
  }

  private void updatePollingScenesStatus() {
    redisUtil.set(RedisKey.POLLING_SCENES_STATUS, String.valueOf(System.currentTimeMillis()));
  }

  private long getPollingScenesTime() {
    return redisUtil.getStr(RedisKey.POLLING_SCENES_STATUS).isPresent()
        ? Long.parseLong(redisUtil.getStr(RedisKey.POLLING_SCENES_STATUS).get()) : 0;
  }

  private boolean getPollingScenesStatusLock() {
    return redisUtil.lock(RedisKey.POLLING_SCENES_LOCK, String.valueOf(System.currentTimeMillis()));
  }

  static class Task implements Runnable {
    private RpcSenderUtils senderUtils;
    private volatile boolean stop = false;
    private KvmVideoWall videoWall;
    private int pollingIntervalSeconds;
    private Collection<VideoWallScene> pollingScenes = new ArrayList<>();

    protected Task(RpcSenderUtils senderUtils, KvmVideoWall videoWall) {
      this.senderUtils = senderUtils;
      this.videoWall = videoWall;
      this.pollingIntervalSeconds = videoWall.getPollingIntervalTime();
    }

    protected void stop() {
      stop = true;
    }

    protected boolean getRunningStatus() {
      return stop;
    }

    protected synchronized void resetPollingScenes(Collection<VideoWallScene> scenes) {
      pollingScenes = scenes;
    }

    protected synchronized void resetPollingInterval(int pollingIntervalSeconds) {
      this.pollingIntervalSeconds = pollingIntervalSeconds;
    }

    @Override
    public synchronized void run() {
      try {
        while (!stop) {
          for (VideoWallScene pollingScene : pollingScenes) {
            if (stop) {
              break;
            }
            OpenVwPanelsRequestBody body = new OpenVwPanelsRequestBody();
            body.setId(pollingScene.getWallId());
            body.setPanelData(pollingScene.getPanelData());
            body.setLayoutData(pollingScene.getLayoutData());
            MqRequest<OpenVwPanelsRequestBody> mqRequest = BuildMqRequestBodyUtils.buildVideoWallMqBody(videoWall,
                    body, new KvmOperationFrom(SystemUtil.getHostInfo().getAddress(), "polling"));
            senderUtils.send(videoWall.getMasterId(), RoutingOperation.VW_PANELS_OPEN, mqRequest);
            log.debug("pollingIntervalSeconds = {}", pollingIntervalSeconds);
            wait(pollingIntervalSeconds * 1000L);
          }
        }
      } catch (Exception e) {
        log.error("VideoWall {} failed to execute the polling scenes task", videoWall.getName(), e);
        stop();
      }

    }
  }

}
