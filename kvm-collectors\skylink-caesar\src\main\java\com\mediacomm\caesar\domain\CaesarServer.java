package com.mediacomm.caesar.domain;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * CaesarServer.
 */
@Data
public class CaesarServer {
  private int id;
  private String name;
  private String sn;
  private int totalFanNumber;
  private int totalPowerNumber;
  private int totalPortNumber;
  private int totalSlotNumber;
  private int portPreSlot;
  private String ip1;
  private String ip2;
  private String deviceType;
  private String deviceModel;
  private String softVersion;
  private String systemVersion;
  private String fpgaVersion;
  private List<Slot> slotInfos = new ArrayList<>();
}
