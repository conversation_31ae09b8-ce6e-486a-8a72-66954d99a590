package com.mediacomm.aircon.controller;

import com.mediacomm.aircon.domain.AirconPanelRect;
import com.mediacomm.aircon.domain.AirconVideoPanels;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.LayerData;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.LayoutType;

import java.util.Collections;

/**
 * Aircon测试数据工厂类，用于创建集成测试所需的各种测试数据
 */
public class TestDataFactory {

    public static final String TEST_MASTER_ID = "test-aircon-master-001";
    public static final String TEST_ASSET_ID = "test-aircon-asset-001";
    public static final int TEST_VIDEO_WALL_ID = 1;
    public static final int TEST_TX_DEVICE_ID = 3001;
    public static final String TEST_DEVICE_IP = "127.0.0.1";
    public static final String TEST_USER_ADDRESS = "*************";
    public static final String TEST_USER_NAME = "testuser";

    /**
     * 创建测试用的KvmMaster
     */
    public static KvmMaster createTestKvmMaster() {
        KvmMaster master = new KvmMaster();
        master.setMasterId(TEST_MASTER_ID);
        master.setDeviceIp(TEST_DEVICE_IP);
        master.setName("Test Aircon Master");
        master.setAlias("Test Aircon Master");
        master.setDeviceModel(DeviceType.AIRCON.getDeviceTypeId());
        master.setVersion("1.0.0");
        master.setCollectorProperties(Collections.emptyList());
        master.setProperties(Collections.emptyList());
        return master;
    }

    /**
     * 创建测试用的KvmVideoWall
     */
    public static KvmVideoWall createTestKvmVideoWall() {
        KvmVideoWall videoWall = new KvmVideoWall();
        videoWall.setWallId(TEST_VIDEO_WALL_ID);
        videoWall.setDeviceId(TEST_VIDEO_WALL_ID);
        videoWall.setMasterId(TEST_MASTER_ID);
        videoWall.setName("Test Aircon Video Wall");
        videoWall.setDeviceModel(DeviceType.AIRCON_VIDEO_WALL.getDeviceTypeId());
        videoWall.setRowCount(2);
        videoWall.setColCount(2);
        videoWall.setSingleW(1920);
        videoWall.setSingleH(1080);
        videoWall.setUniqueSearchKey("VW-" + TEST_VIDEO_WALL_ID);
        videoWall.setCollectorProperties(Collections.emptyList());
        videoWall.setProperties(Collections.emptyList());
        videoWall.setDecoders(Collections.emptyList());
        return videoWall;
    }

    /**
     * 创建测试用的KvmAsset (TX设备)
     */
    public static KvmAsset createTestKvmAsset() {
        KvmAsset asset = new KvmAsset();
        asset.setAssetId(TEST_ASSET_ID);
        asset.setDeviceId(TEST_TX_DEVICE_ID);
        asset.setMasterId(TEST_MASTER_ID);
        asset.setName("Test Aircon TX Device");
        asset.setAlias("Test Aircon TX");
        asset.setDeviceModel(DeviceType.AIRCON_ENCODER.getDeviceTypeId());
        asset.setVersion(Collections.emptyList());
        // 添加分辨率类型属性
        Property resolutionProperty = new Property(PropertyKeyConst.CAESAR_TX_RESOLUTION_TYPE_KEY, "1");
        asset.setProperties(Collections.singletonList(resolutionProperty));
        
        return asset;
    }

    /**
     * 创建测试用的PanelRect
     */
    public static PanelRect createTestPanelRect() {
        PanelRect panelRect = new PanelRect();
        panelRect.setPanelId(1);
        panelRect.setSeq(0);
        panelRect.setXpos(0);
        panelRect.setYpos(0);
        panelRect.setWidth(1920);
        panelRect.setHeight(1080);
        panelRect.setLayoutType(LayoutType.FULL_SCREEN);
        
        // 创建图层数据
        LayerData layer = new LayerData();
        layer.setSeq(0);
        layer.setVideoSrcId(TEST_ASSET_ID);
        layer.setVideoSrcName("Test Aircon TX Device");
        layer.setCtrlMode(1); // 完全控制
        layer.setEnableAudio(false);
        
        panelRect.setChildPanels(Collections.singletonList(layer));
        
        return panelRect;
    }

    /**
     * 创建测试用的PanelRectRequestBody
     */
    public static PanelRectRequestBody createTestPanelRectRequestBody() {
        PanelRectRequestBody body = new PanelRectRequestBody();
        body.setId(TEST_VIDEO_WALL_ID);
        body.setPanelRect(createTestPanelRect());
        return body;
    }

    /**
     * 创建测试用的MqRequest
     */
    public static MqRequest<PanelRectRequestBody> createTestAirconMqRequest() {
        MqRequest<PanelRectRequestBody> request = new MqRequest<>();
        request.setMasterId(TEST_MASTER_ID);
        request.setBody(createTestPanelRectRequestBody());
        return request;
    }

    /**
     * 创建成功的AirconPanelRect响应
     */
    public static AirconPanelRect createSuccessfulAirconPanelRect() {
        AirconPanelRect panelRect = new AirconPanelRect();
        panelRect.setPanelId(1);
        panelRect.setSeq(0);
        panelRect.setXpos(0);
        panelRect.setYpos(0);
        panelRect.setWidth(1920);
        panelRect.setHeight(1080);
        panelRect.setCtrlMode(1);
        panelRect.setVideoSrcId(TEST_TX_DEVICE_ID);
        return panelRect;
    }

    /**
     * 创建测试用的AirconVideoPanels
     */
    public static AirconVideoPanels createTestAirconVideoPanels() {
        AirconVideoPanels videoPanels = new AirconVideoPanels();
        videoPanels.setPanels(Collections.singletonList(createSuccessfulAirconPanelRect()));
        return videoPanels;
    }
}
