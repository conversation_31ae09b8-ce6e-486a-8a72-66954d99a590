package com.mediacomm.controller.web;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmOperationLog;
import com.mediacomm.entity.vo.KvmOperationLogVo;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmOperationLogService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.sysenum.OperationType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Collection;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * KvmOperationLogController.
 */
@Tag(name = "KVM操作日志")
@RestController
@RequestMapping(ResUrlDef.KVM_OPERATION_LOG)
@Slf4j
public class KvmOperationLogController extends
    SkyLinkController<KvmOperationLog, KvmOperationLogService> {

  @Autowired
  KvmMasterService masterService;

  @Operation(summary = "KVM操作日志查询")
  @GetMapping("/search")
  public Result<KvmOperationLogResult> getKvmOperationLog(
      @RequestParam int currentPage,
      @RequestParam int pageSize,
      @RequestParam long startTime,
      @RequestParam long endTime,
      @RequestParam(name = "masterId", required = false) String masterId) {
    int offset = (currentPage - 1) * pageSize;
    KvmOperationLogResult kvmOperationLogResult = new KvmOperationLogResult();
    kvmOperationLogResult.setCurrentPage(currentPage);
    kvmOperationLogResult.setPageSize(pageSize);
    int totalSize = service.countBetweenStartAndEndTime(startTime, endTime, masterId);
    kvmOperationLogResult.setTotalPages(totalSize / pageSize);
    kvmOperationLogResult.setTotalSize(totalSize);
    kvmOperationLogResult.setContent(
        service.getBetweenStartAndEndTime(offset, pageSize, startTime, endTime, masterId));
    return Result.ok(kvmOperationLogResult);
  }

  @GetMapping("/export")
  public void exportExcel(HttpServletResponse response,
                          @RequestParam long startTime,
                          @RequestParam long endTime) {
    try (ExcelWriter writer = ExcelUtil.getWriter(true)) {
      response.setContentType("application/vnd.ms-excel;charset=utf-8");
      response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
      response.setHeader("Content-Disposition", "attachment;filename=operation-logs.xlsx");
      ServletOutputStream out = response.getOutputStream();
      Collection<KvmOperationLogVo> logVos =
          service.getBetweenStartAndEndTime(startTime, endTime, null);
      writer.write(logVos.stream().map(KvmOperationLogRow::new).toList(), true);
      writer.flush(out);
      IoUtil.close(out);
    } catch (Exception ex) {
      log.error(ex.getMessage(), ex);
    }
  }

  @Data
  public static class KvmOperationLogRow {
    private Integer id;
    @Alias(value = "KVM主机ID")
    private String masterId;
    @Alias(value = "KVM主机")
    private String masterName;
    @Alias(value = "操作时间")
    private String operationTime;
    @Alias(value = "操作类型")
    private String operationType;
    @Alias(value = "操作事件")
    private String operationEvent;
    @Alias(value = "来源地址")
    private String operationSrcAddress;
    @Alias(value = "来源用户")
    private String operationSrcUser;
    @Alias(value = "目标来源")
    private String operationSrcDevice;
    @Alias(value = "受控设备")
    private String operationTargetDevice;

    public KvmOperationLogRow(KvmOperationLogVo vo) {
      this.id = vo.getId();
      this.masterId = vo.getMasterId();
      this.masterName = vo.getMasterName();
      DateTime date = DateUtil.date(vo.getOperationTime());
      this.operationTime = date.toString("yyyy-MM-dd HH:mm:ss");
      this.operationType = OperationType.getOperationString(vo.getOperationType());
      this.operationEvent = operationEvent(vo.getOperationEvent());
      this.operationSrcAddress = vo.getOperationSrcAddress();
      this.operationSrcUser= vo.getOperationSrcUser();
      this.operationSrcDevice = vo.getOperationSrcDevice();
      this.operationTargetDevice = vo.getOperationTargetDevice();
    }
  }

  private static String operationEvent(String event) {
    return switch (event) {
      case "open_panel" -> "开窗";
      case "close_panel" -> "关窗";
      case "move_panel" -> "移动窗口";
      case "swap_panel" -> "切换窗口层级";
      case "open_panels" -> "批量开窗";
      case "clear_panels" -> "清屏";
      case "operation" -> "操作模式连接";
      case "video" -> "视频模式连接";
      case "private" -> "私有模式连接";
      case "disconnect" -> "断开连接";
      default -> "";
    };
  }

  @Data
  public static class KvmOperationLogResult {
    private int currentPage;
    private int pageSize;
    private int totalSize;
    private int totalPages;
    private Collection<KvmOperationLogVo> content;
  }
}
