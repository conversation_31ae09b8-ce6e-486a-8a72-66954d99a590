package com.mediacomm.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 屏幕.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoScreen {
  private Audio audio;
  private String createTime;
  private String name;
  private int screenId;

  @Data
  public static class Audio {
    private int audioOutputMode;
    private int inputId;
    private int interfaceId;
    private int isMute;
    private int slotId;
    private int volume;
  }
}
