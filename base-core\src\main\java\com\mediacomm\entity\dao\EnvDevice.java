package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mediacomm.config.db.ListPropertyJsonTypeHandler;
import com.mediacomm.entity.Property;
import com.mediacomm.system.annotation.IpValidation;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 监控设备.
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
public class EnvDevice extends SkyLinkDbEntity {
  @TableId(value = "id", type = IdType.ASSIGN_UUID)
  private String id;
  private String name;
  @IpValidation
  private String deviceIp;
  private Integer deviceModel;
  private String hardcode;
  private String version;
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> collectorProperties = new ArrayList<>();
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> properties = new ArrayList<>();
}
