package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.KvmAssetGroup;
import com.mediacomm.entity.vo.KvmAssetGroupVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.KvmAssetGroupMapper;
import com.mediacomm.system.variable.sysenum.AssetGroupType;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class KvmAssetGroupService extends SkyLinkServiceImpl<KvmAssetGroupMapper, KvmAssetGroup> {

  /**
   * 通过分组类型获取所有Tx或Rx分组.
   *
   * @param roomId 房间Id.
   * @param type 分组类型.
   * @return Collection.
   */
  public Collection<KvmAssetGroupVo> allByGroupType(Integer roomId, AssetGroupType type) {
    if (roomId != null) {
      return baseMapper.findByRoomIdAndType(roomId, type.toString());
    } else {
      return baseMapper.findByType(type.toString());
    }
  }

  public Collection<KvmAssetGroupVo> allChildGroup(Integer roomId, Integer pid, AssetGroupType type) {
    return baseMapper.findChildGroup(roomId, pid, type.toString());
  }

  /**
   * 批量将外设存入分组.
   *
   * @param groupId 分组Id.
   * @param extendIds 外设Id.
   * @return .
   */
  public boolean saveToMerge(Integer groupId, Collection<String> extendIds) {
    return baseMapper.insertToMerge(groupId, extendIds);
  }

  /**
   * 批量将分组中的外设删除.
   *
   * @param groupId .
   * @param extendIds .
   * @return .
   */
  public boolean delFromMerge(Integer groupId, Collection<String> extendIds) {
    return baseMapper.delFromMerge(groupId, extendIds);
  }

  public boolean delAllFromMerge(Integer groupId) {
    return baseMapper.delAllFromMerge(groupId);
  }

  public long countChildGroup(Integer pid) {
    LambdaQueryWrapper<KvmAssetGroup> wrapper = Wrappers.lambdaQuery(KvmAssetGroup.class)
            .eq(KvmAssetGroup::getParentGroupId, pid);
    return count(wrapper);
  }
}
