package com.mediacomm.service;

import com.mediacomm.controller.KaitoEFeignClientApi;
import com.mediacomm.domain.KaitoCreateLayerRes;
import com.mediacomm.domain.KaitoDeviceDetail;
import com.mediacomm.domain.KaitoInitStatus;
import com.mediacomm.domain.KaitoInput;
import com.mediacomm.domain.KaitoInputDetail;
import com.mediacomm.domain.KaitoInputList;
import com.mediacomm.domain.KaitoIpcDetail;
import com.mediacomm.domain.KaitoIpcDetailList;
import com.mediacomm.domain.KaitoLayerDetail;
import com.mediacomm.domain.KaitoOutput;
import com.mediacomm.domain.KaitoOutputDetail;
import com.mediacomm.domain.KaitoOutputList;
import com.mediacomm.domain.KaitoScreen;
import com.mediacomm.domain.KaitoScreenDetail;
import com.mediacomm.domain.KaitoScreenList;
import com.mediacomm.domain.KaitoVideoServerInfo;
import com.mediacomm.domain.request.KaitoDeviceIdReq;
import com.mediacomm.domain.request.KaitoInputDetailReq;
import com.mediacomm.domain.request.KaitoIpcSourceChanelListReq;
import com.mediacomm.domain.request.KaitoLayerClearReq;
import com.mediacomm.domain.request.KaitoLayerDetailReq;
import com.mediacomm.domain.request.KaitoLayerLayoutReq;
import com.mediacomm.domain.request.KaitoLayerReq;
import com.mediacomm.domain.request.KaitoOutputDetailReq;
import com.mediacomm.domain.request.KaitoRequestBody;
import com.mediacomm.domain.request.KaitoResponseBody;
import com.mediacomm.domain.request.KaitoScreenOsdReq;
import com.mediacomm.domain.request.KaitoWriteSourceReq;
import com.mediacomm.domain.request.KaitoWriteWindowReq;
import com.mediacomm.domain.request.KaitoZOrderReq;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.message.reqeust.body.OpenVwPanelsRequestBody;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.entity.message.reqeust.body.VwEnableRequestBody;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.mapper.KaitoEntityMapper;
import com.mediacomm.util.mapper.KaitoInspectUtil;
import jakarta.annotation.Resource;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class KaitoECmdService extends KaitoCmd {
  private static final int PORT = 8000;
  @Resource
  private KaitoEFeignClientApi cli;
  @Resource
  private KaitoEntityMapper entityMapper;

  @Override
  public String getExtendModel() {
    return "e";
  }

  @Override
  public URI getUri(String masterIp) {
    return URI.create(String.format("http://%s:%d", masterIp, PORT));
  }

  @Override
  public KaitoVideoServerInfo getServerInfo(KvmMaster master) {
    KaitoResponseBody videoServerInfo = cli.getVideoServerInfo(
            getUri(master.getDeviceIp()), KaitoInspectUtil.buildKaitoReqBody(DEVICE_ID_REQUEST, master));
    KaitoVideoServerInfo info =
            JsonUtils.decode(videoServerInfo.getBody(), KaitoVideoServerInfo.class);
    if (info == null) {
      log.error("{} {}", "Kaito-E getServerInfo failed!", JsonUtils.encode(videoServerInfo));
    }
    return info;
  }

  @Override
  public List<PanelRect> getCurrentPanels(KvmMaster master, KvmVideoWall wall) {
    KaitoLayerReq requestParam = new KaitoLayerReq(0, wall.getDeviceId());
    KaitoResponseBody responseBody =
            cli.getScreenReadDetail(getUri(master.getDeviceIp()),
                    KaitoInspectUtil.buildKaitoReqBody(requestParam, master));
    List<PanelRect> panels = new ArrayList<>();
    KaitoScreenDetail screenDetail =
            JsonUtils.decode(responseBody.getBody(), KaitoScreenDetail.class);
    if (screenDetail == null) {
      log.error("Kaito-E getVwPanels error! {}", JsonUtils.encode(responseBody));
      return null;
    } else {
      for (KaitoLayerDetail layerDetail : screenDetail.getScreenLayers()) {
        PanelRect panelRect = entityMapper.toPanelRect(layerDetail, wall.getWallId());
        panels.add(panelRect);
      }
      return panels;
    }
  }

  @Override
  public PanelRect openPanel(KvmMaster master, PanelRectRequestBody requestBody) {
    KaitoLayerDetail requestParam =
            entityMapper.toKaitoLayerDetail(requestBody.getPanelRect(), requestBody.getId());
    KaitoResponseBody responseBody =
            cli.createLayer(getUri(master.getDeviceIp()),
                    KaitoInspectUtil.buildKaitoReqBody(requestParam, master));
    KaitoCreateLayerRes detail =
            JsonUtils.decode(responseBody.getBody(), KaitoCreateLayerRes.class);
    if (detail == null) {
      log.error("{} {}", "Kaito-E openVwPanel failed!", JsonUtils.encode(responseBody));
      return null;
    } else {
      return entityMapper.toPanelRect(detail.getLayer(), requestBody.getId());
    }
  }

  @Override
  public void openPanels(KvmMaster master, KvmVideoWall wall, OpenVwPanelsRequestBody requestBody) {
    KaitoLayerLayoutReq detailListReq = new KaitoLayerLayoutReq(wall.getDeviceId(), 0);
    for (PanelRect panel : requestBody.getPanelData().getPanels()) {
      KaitoLayerLayoutReq.Layer layer =
              entityMapper.toKaitoLayerLayoutReqLayer(panel, wall.getWallId());
      detailListReq.getLayers().add(layer);
    }
    URI uri = getUri(master.getDeviceIp());
    cli.createLayers(uri, KaitoInspectUtil.buildKaitoReqBody(detailListReq, master));
  }

  @Override
  public void moveVwPanel(KvmMaster master, KvmVideoWall wall, PanelRectRequestBody requestBody) {
    URI uri = getUri(master.getDeviceIp());
    List<PanelRect> currentPanels = getCurrentPanels(master, wall);
    for (PanelRect currentPanel : currentPanels) {
      // 换源
      if (currentPanel.getXpos() == requestBody.getPanelRect().getXpos()
              && currentPanel.getYpos() == requestBody.getPanelRect().getYpos()
              && currentPanel.getWidth() == requestBody.getPanelRect().getWidth()
              && currentPanel.getHeight() == requestBody.getPanelRect().getHeight()
              && !currentPanel.getVideoSrcId().equals(requestBody.getPanelRect().getVideoSrcId())) {
        KaitoWriteSourceReq kaitoWriteSourceReq =
                entityMapper.toKaitoWriteSourceReq(requestBody.getPanelRect(),
                        wall.getWallId());
        cli.writeSource(uri, KaitoInspectUtil.buildKaitoReqBody(kaitoWriteSourceReq, master));
        return;
      }
    }
    KaitoWriteWindowReq kaitoWriteWindowReq = entityMapper.toKaitoWriteWindowReq(
            requestBody.getPanelRect(),requestBody.getId());
    cli.writeWindow(uri, KaitoInspectUtil.buildKaitoReqBody(kaitoWriteWindowReq, master));
  }

  @Override
  public void closeAllPanels(KvmMaster master, KvmVideoWall wall) {
    URI uri = getUri(master.getDeviceIp());
    KaitoLayerClearReq clearReq = new KaitoLayerClearReq(0, wall.getDeviceId());
    cli.clearLayer(uri, KaitoInspectUtil.buildKaitoReqBody(clearReq, master));
  }

  @Override
  public void closePanel(KvmMaster master, KvmVideoWall wall, PanelRectRequestBody requestBody) {
    URI uri = getUri(master.getDeviceIp());
    KaitoLayerDetailReq kaitoLayerDetailReq = new KaitoLayerDetailReq(0,
            wall.getDeviceId(), requestBody.getPanelRect().getPanelId());
    cli.deleteLayer(uri, KaitoInspectUtil.buildKaitoReqBody(kaitoLayerDetailReq, master));
  }

  @Override
  public KaitoScreenDetail.Osd enableOsd(KvmMaster master, KvmVideoWall wall, VwEnableRequestBody requestBody) {
    URI uri = getUri(master.getDeviceIp());
    KaitoScreenOsdReq requestParam = new KaitoScreenOsdReq(0, wall.getDeviceId(),
            requestBody.isEnable() ? 1 : 0);
    KaitoResponseBody responseBody = cli.writeOSD(uri, KaitoInspectUtil.buildKaitoReqBody(requestParam, master));
    KaitoScreenDetail osd = JsonUtils.decode(responseBody.getBody(), KaitoScreenDetail.class);
    if (osd == null) {
      log.error("Write osd data failed! {}", JsonUtils.encode(responseBody));
      return null;
    }
    return osd.getOsd();
  }

  @Override
  public KaitoScreenDetail.Osd getOsd(KvmMaster master, KvmVideoWall wall) {
    URI uri = getUri(master.getDeviceIp());
    KaitoLayerReq requestParam = new KaitoLayerReq(0, wall.getDeviceId());
    KaitoResponseBody responseBody =
            cli.getScreenReadDetail(uri, KaitoInspectUtil.buildKaitoReqBody(requestParam, master));
    KaitoScreenDetail screenDetail =
            JsonUtils.decode(responseBody.getBody(), KaitoScreenDetail.class);
    if (screenDetail != null) {
      // 需要计算上偏移量
      int posX = screenDetail.getOutputMode().getSize().getX();
      int posY = screenDetail.getOutputMode().getSize().getY();
      screenDetail.getOsd().setX(screenDetail.getOsd().getX() - posX);
      screenDetail.getOsd().setY(screenDetail.getOsd().getY() - posY);
      return screenDetail.getOsd();
    } else {
      log.error("Get osd data failed! {}", JsonUtils.encode(responseBody));
      return null;
    }
  }

  @Override
  public KaitoInitStatus getInitStatus(KvmMaster master) {
    KaitoRequestBody<KaitoDeviceIdReq> deviceIdReqBody = KaitoInspectUtil.buildKaitoReqBody(DEVICE_ID_REQUEST, master);
    KaitoResponseBody initStatus = cli.getInitStatus(getUri(master.getDeviceIp()), deviceIdReqBody);
    return JsonUtils.decode(initStatus.getBody(), KaitoInitStatus.class);
  }

  @Override
  public KaitoDeviceDetail getDeviceDetail(KvmMaster master) {
    KaitoResponseBody deviceDetail = cli.getDeviceDetail(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoReqBody(DEVICE_ID_REQUEST, master));
    return JsonUtils.decode(deviceDetail.getBody(), KaitoDeviceDetail.class);
  }

  @Override
  public List<KvmAsset> getAssets(KvmMaster master) {
    KaitoResponseBody inputList = cli.getInputReadList(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoReqBody(DEVICE_ID_REQUEST, master));
    KaitoInputList inputs = JsonUtils.decode(inputList.getBody(), KaitoInputList.class);
    List<KvmAsset> assets = new ArrayList<>();
    if (inputs != null) {
      for (KaitoInput input : inputs.getInputs()) {
        if (input.isOnline()) {
          KvmAsset kvmAsset = entityMapper.toKvmAsset(input, master.getMasterId());
          assets.add(kvmAsset);
        }
      }
    }
    KaitoIpcSourceChanelListReq ipcSourceChanelListReq = new KaitoIpcSourceChanelListReq();
    KaitoResponseBody ipcList =
            cli.getIpcSourceChannelList(getUri(master.getDeviceIp()),
                    KaitoInspectUtil.buildKaitoReqBody(ipcSourceChanelListReq, master));
    KaitoIpcDetailList ipcDetailList =
            JsonUtils.decode(ipcList.getBody(), KaitoIpcDetailList.class);
    if (ipcDetailList != null) {
      for (KaitoIpcDetail kaitoIpcDetail : ipcDetailList.getSourceList()) {
        KvmAsset ipc = entityMapper.toKvmAsset(kaitoIpcDetail, master.getMasterId());
        assets.add(ipc);
      }
    }

    KaitoResponseBody outputList = cli.getOutputReadList(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoReqBody(DEVICE_ID_REQUEST, master));
    KaitoOutputList outputs = JsonUtils.decode(outputList.getBody(), KaitoOutputList.class);
    if (outputs != null) {
      for (KaitoOutput output : outputs.getOutputs()) {
        KvmAsset kvmAsset = entityMapper.toKvmAsset(output, master.getMasterId());
        assets.add(kvmAsset);
      }
    }
    return assets;
  }

  @Override
  public List<KvmVideoWall> getVideoWalls(KvmMaster master) {
    KaitoResponseBody screenList = cli.getScreenReadList(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoReqBody(DEVICE_ID_REQUEST, master));
    KaitoScreenList screens = JsonUtils.decode(screenList.getBody(), KaitoScreenList.class);
    List<KvmVideoWall> videoWalls = new ArrayList<>();
    if (screens != null) {
      for (KaitoScreen screen : screens.getScreens()) {
        KaitoLayerReq layerReq = new KaitoLayerReq(0, screen.getScreenId());
        KaitoRequestBody<KaitoLayerReq> layerReqBody = KaitoInspectUtil.buildKaitoReqBody(layerReq, master);
        KaitoResponseBody screenDetail = cli.getScreenReadDetail(getUri(master.getDeviceIp()), layerReqBody);
        KaitoScreenDetail kaitoScreenDetail =
                JsonUtils.decode(screenDetail.getBody(), KaitoScreenDetail.class);
        if (kaitoScreenDetail != null) {
          KvmVideoWall wall =
                  entityMapper.toKvmVideoWall(kaitoScreenDetail, master.getMasterId());
          videoWalls.add(wall);
        }
      }
    }
    return videoWalls;
  }

  @Override
  public List<KvmSlot> getSlots(KvmMaster master) {
    KaitoDeviceDetail detail = getDeviceDetail(master);
    if (detail != null) {
      return entityMapper.toKvmSlotList(detail.getSlotList(), master.getMasterId());
    }
    return new ArrayList<>();
  }

  @Override
  public KaitoInputDetail getInputDetail(KvmMaster master, KaitoInputDetailReq requestBody) {
    KaitoResponseBody deviceDetail = cli.getInputDetail(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoReqBody(requestBody, master));
    return JsonUtils.decode(deviceDetail.getBody(), KaitoInputDetail.class);
  }

  @Override
  public KaitoOutputDetail getOutputDetail(KvmMaster master, KaitoOutputDetailReq requestBody) {
    KaitoResponseBody deviceDetail = cli.getOutputReadDetail(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoReqBody(requestBody, master));
    return JsonUtils.decode(deviceDetail.getBody(), KaitoOutputDetail.class);
  }

  @Override
  public void writeZIndex(KvmMaster master, KaitoZOrderReq requestBody) {
    cli.writeZIndex(getUri(master.getDeviceIp()), KaitoInspectUtil.buildKaitoReqBody(requestBody, master));
  }
}
