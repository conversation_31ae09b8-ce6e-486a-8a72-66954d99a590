package com.mediacomm.util;

import com.google.common.base.Strings;
import java.io.FileWriter;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.TimeSeriesOperations;
import org.springframework.data.redis.core.options.RangeOptions;
import org.springframework.data.redis.core.options.TimeSeriesOptions;
import org.springframework.data.redis.core.protocol.entity.KeyedValue;
import org.springframework.data.redis.core.protocol.entity.Label;
import org.springframework.data.redis.core.protocol.entity.Range;
import org.springframework.data.redis.core.protocol.entity.Value;
import org.springframework.stereotype.Component;

/**
 * redis.
 */
@Component
@Slf4j
public class RedisUtil {

  private final StringRedisTemplate redisTemplate;

  private final TimeSeriesOperations<String, String> tsOperations;


  @Autowired
  public RedisUtil(StringRedisTemplate redisTemplate,
                   TimeSeriesOperations<String, String> tsOperations) {
    this.redisTemplate = redisTemplate;
    this.tsOperations = tsOperations;
  }

  public static String redisKey(String... item) {
    return String.join(":", item);
  }

  /**
   * 是否存在对应的建.
   */
  public boolean haskey(@NonNull String key) {
    return Boolean.TRUE.equals(redisTemplate.hasKey(key));
  }

  /**
   * 获取所有keys.
   *
   * @param pattern 正则.
   */
  public Set<String> getKeys(String pattern) {
    ScanOptions options = ScanOptions.scanOptions()
            .match(pattern)
            .build();
    Set<String> keys = new HashSet<>();
    Cursor<String> cs = redisTemplate.scan(options);
    while (cs.hasNext()) {
      String value = cs.next();
      keys.add(value);
    }
    cs.close();
    return keys;
    /*return Optional.ofNullable(redisTemplate.keys(pattern))
        .orElse(new HashSet<>());*/
  }

  /**
   * 设置键值过期时间，以秒为单位.
   */
  public boolean expire(@NonNull String key, long timeout) {
    return expire(key, timeout, TimeUnit.SECONDS);
  }

  /**
   * 设置键值过期时间.
   */
  public boolean expire(@NonNull String key, long timeout, TimeUnit unit) {
    Boolean res = redisTemplate.expire(key, timeout, unit);
    return Boolean.TRUE.equals(res);
  }

  /**
   * 设置过期时间.
   */
  public boolean expireAt(String key, Date date) {
    return Boolean.TRUE.equals(redisTemplate.expireAt(key, date));
  }

  /**
   * 移除 key 的过期时间，key 将持久保持.
   */
  public boolean persist(String key) {
    return Boolean.TRUE.equals(redisTemplate.persist(key));
  }

  /**
   * 获取键值过期时间，返回以秒为单位.
   */
  public long getExpire(@NonNull String key) {
    return getExpire(key, TimeUnit.SECONDS);
  }

  /**
   * 获取键值过期时间.
   */
  public long getExpire(@NonNull String key, TimeUnit timeUnit) {
    return Optional.ofNullable(this.redisTemplate.getExpire(key, timeUnit))
            .orElse(0L);
  }

  /**
   * 设置永久缓存.
   */
  public boolean set(@NonNull String key, String value) {
    return set(key, value, -1);
  }

  /**
   * 设置缓存，以秒为单位.
   */
  public boolean set(@NonNull String key, String value, long timeout) {
    return set(key, value, timeout, TimeUnit.SECONDS);
  }

  /**
   * 设置缓存.
   *
   * @param timeout 如果为-l,则永久缓存
   */
  public boolean set(String key, String value, long timeout, TimeUnit timeUnit) {
    if (Strings.isNullOrEmpty(key)) {
      return false;
    }
    if (timeout == -1) {
      redisTemplate.opsForValue().set(key, value);
    } else {
      redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }
    return Boolean.TRUE;
  }

  /**
   * 仅当 new key 不存在时，将 oldKey 改名为 new key.
   */
  public boolean renameIfAbsent(String oldKey, String newKey) {
    return Boolean.TRUE.equals(redisTemplate.renameIfAbsent(oldKey, newKey));
  }

  /**
   * hash存储.
   *
   * @param key      key.
   * @param childKey map key.
   * @param value    map value.
   */
  public void hset(String key, String childKey, String value) {
    redisTemplate.opsForHash().put(key, childKey, value);
  }

  /**
   * hash存储.
   *
   * @param key  key.
   * @param data map.
   */
  public void hset(String key, Map<String, String> data) {
    redisTemplate.opsForHash().putAll(key, data);
  }

  /**
   * hash删除.
   *
   * @param key      主键.
   * @param childKey 子键.
   */
  public void hdel(String key, Object... childKey) {
    redisTemplate.opsForHash().delete(key, childKey);
  }

  /**
   * 获取缓存.
   *
   * @param key .
   * @return .
   */
  @NonNull
  public Optional<String> getStr(String key) {
    return Optional.ofNullable(redisTemplate.opsForValue().get(key));
  }

  /**
   * 获取hashKey对应的所有键值.
   *
   * @param key key.
   * @return .
   */
  @NonNull
  public Optional<Map<String, String>> hmget(String key) {
    Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
    return Optional.of(entries).map(Map::entrySet).map(entries1 -> {
      Map<String, String> map = new HashMap<>(entries1.size());
      entries1.forEach(entry -> map.put(entry.getKey().toString(), entry.getValue().toString()));
      return map;
    });
  }

  public Optional<String> hget(String key, String childKey) {
    return Optional.ofNullable(redisTemplate.opsForHash().get(key, childKey)).map(String::valueOf);
  }

  /**
   * 获取hashKey对应的所有键值.
   *
   * @param key key.
   * @return .
   */
  @NonNull
  public Optional<Set<String>> hkeys(String key) {
    return Optional.ofNullable(redisTemplate.opsForSet().members(key));
  }

  /**
   * 获取列表指定范围的缓存.
   *
   * @param key   key.
   * @param start 起点.
   * @param end   终点.
   * @return .
   */
  @NonNull
  public Optional<List<String>> lget(String key, long start, long end) {
    return Optional.ofNullable(redisTemplate.opsForList().range(key, start, end));
  }

  /**
   * 批量删除缓存.
   */
  public boolean batchDel(Collection<String> keys) {
    if (keys == null || keys.isEmpty()) {
      return false;
    }
    keys.forEach(this::del);
    return Boolean.TRUE;
  }

  /**
   * Pipeline operation.
   */
  public void batchHashSet(Map<String, Map<String, String>> data) {
    if (data != null && !data.isEmpty()) {
      redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
        data.forEach((key, value) -> {
          if (value != null && !value.isEmpty()) {
            connection.hashCommands().hMSet(
                    key.getBytes(StandardCharsets.UTF_8), value.entrySet().stream()
                            .collect(Collectors.toMap(
                                    entry -> entry.getKey().getBytes(StandardCharsets.UTF_8),
                                    entry -> entry.getValue().getBytes(StandardCharsets.UTF_8)))
            );
          }
        });
        return null;
      });
    }
  }

  /**
   * .
   *
   * @param keys .
   * @return Object -> LinkHashMap.
   */
  public Map<String, Map<String, String>> batchHashGet(@NonNull Collection<String> keys) {
    Map<String, Map<String, String>> data = new HashMap<>();
    List<Object> objs = redisTemplate.executePipelined((RedisCallback<Map<String, Map<String, String>>>) connection -> {
      keys.forEach((key) -> connection.hashCommands().hGetAll(key.getBytes(StandardCharsets.UTF_8)));
      return null;
    });
    if (keys.size() == objs.size()) {
      List<String> keyList = new ArrayList<>(keys);
      int i = 0;
      for (Object obj : objs) {
        if (obj instanceof LinkedHashMap<?, ?>) {
          data.put(keyList.get(i), new HashMap<>((LinkedHashMap<String, String>) obj));
        }
        i++;
      }
    }
    return data;
  }

  /**
   * 批量获取缓存.
   *
   * @param keys .
   * @return .
   */
  public void backup(@NonNull Collection<String> keys, String fileName) {
    try (FileWriter writer = new FileWriter(fileName, StandardCharsets.UTF_8)) {
      for (String key : keys) {
        DataType dataType = type(key);
        if (dataType != null) {
          if ("string".equalsIgnoreCase(dataType.toString())) {
            String value = getStr(key).orElse("");
            writer.write(key + "=" + value + System.lineSeparator());
          } else if ("hash".equalsIgnoreCase(dataType.toString())) {
            Map<String, String> map = hmget(key).orElse(new HashMap<>());
            writer.write(key + "=" + JsonUtils.encode(map) + System.lineSeparator());
          }
        }
      }
    } catch (Exception e) {
      log.error("RedisUtil.backup error", e);
    }
  }

  /**
   * 删除缓存.
   *
   * @param key key.
   * @return .
   */
  public boolean del(String key) {
    return Boolean.TRUE.equals(redisTemplate.delete(key));
  }

  /**
   * 序列化key.
   */
  public byte[] dump(String key) {
    return redisTemplate.dump(key);
  }


  /**
   * 返回 key 所储存的值的类型.
   */
  public DataType type(String key) {
    return redisTemplate.type(key);
  }

  public boolean lock(String lockName, String lockValue) {
    return Boolean.TRUE.equals(
            redisTemplate.opsForValue().setIfAbsent(lockName, lockValue, 15L, TimeUnit.SECONDS));
  }

  /**
   * TimeSeries Create.
   *
   * @param retention 过期时间(单位为毫秒)
   * @param labels    标签值对,表示键的元数据标签并用作二级索引
   */
  public void tsCreate(@NonNull String key, long retention, Label[] labels) {
    tsOperations.create(key, new TimeSeriesOptions().retention(retention).labels(labels));
  }

  /**
   * 将样本附加到时间序列.
   *
   * @param key   key
   * @param value value
   */
  public long tsAdd(@NonNull String key, double value) {
    return tsOperations.add(KeyedValue.just(key).put(0, value));
  }

  /**
   * 将样本附加到时间序列.
   *
   * @param key       key
   * @param value     value
   * @param retention 过期时间
   */
  public long tsAdd(@NonNull String key, double value, long retention) {
    return tsOperations.add(KeyedValue.just(key).put(Instant.now().toEpochMilli(), value),
            new TimeSeriesOptions().retention(retention));
  }

  /**
   * 将样本附加到时间序列.
   *
   * @param key       key
   * @param value     value
   * @param retention 过期时间
   * @param labels    元数据
   */
  public long tsAdd(@NonNull String key, double value, long retention, Label[] labels) {
    return tsOperations.add(KeyedValue.just(key).put(0, value),
            new TimeSeriesOptions().retention(retention).labels(labels));
  }

  /**
   * 将新样本附加到一个或多个时间序列
   * (TS.MADD {key timestamp value}...).
   */
  public List<Long> tsMadd(@NonNull Map<String, Double> keyValue) {
    return tsOperations.mAdd(keyValue.entrySet().stream().filter(item -> item.getKey() != null)
            .map(item -> KeyedValue.just(item.getKey()).put(0, item.getValue()))
            .toArray(KeyedValue[]::new));
  }

  /**
   * 删除给定时间序列的两个时间戳之间的所有样本.
   *
   * @param key  key
   * @param from 删除的开始时间戳
   * @param to   删除的结束时间戳
   */
  public long tsDel(@NonNull String key, long from, long to) {
    return tsOperations.del(key, from, to);
  }

  /**
   * 获取给定时间序列中时间戳最高的样本.
   */
  public Value tsGet(@NonNull String key) {
    return tsOperations.get(key);
  }

  /**
   * 从与特定过滤器匹配的每个时间序列中获取具有最高时间戳的样本.
   * 注意：在 Redis 集群上运行时， MGET 命令不能成为事务的一部分.
   */
  public Range tsMget(@NonNull Label[] filters) {
    return tsOperations.mGet(new RangeOptions().filters(filters));
  }

  /**
   * 查询样本范围.
   */
  public List<Value> tsRange(@NonNull String key) {
    return tsOperations.range(key);
  }

  /**
   * 查询样本范围.
   */
  public List<Value> tsRange(@NonNull String key, long from, long to) {
    return tsOperations.range(key, from, to);
  }

  /**
   * 查询样本范围.
   */
  public List<Value> tsRange(@NonNull String key, long from, long to, @NonNull Label[] filters) {
    return tsOperations.range(key, from, to, new RangeOptions().filters(filters));
  }

  /**
   * 通过正向过滤器查询跨多个时间序列的范围.
   */
  public List<Range> tsMrange() {
    return tsOperations.mRange();
  }

  /**
   * 通过正向过滤器查询跨多个时间序列的范围.
   */
  public List<Range> tsMrange(RangeOptions options) {
    return tsOperations.mRange(options);
  }

  /**
   * 通过正向过滤器查询跨多个时间序列的范围.
   */
  public List<Range> tsMrange(long from, long to) {
    return tsOperations.mRange(from, to);
  }

  /**
   * 通过正向过滤器查询跨多个时间序列的范围.
   */
  public List<Range> tsMrange(long from, long to, RangeOptions options) {
    return tsOperations.mRange(from, to, options);
  }

  /**
   * 清空所有缓存.
   */
  public void clear() {
    redisTemplate.execute((RedisCallback<Object>) connection -> {
      connection.serverCommands().flushAll();
      return null;
    });
  }
}
