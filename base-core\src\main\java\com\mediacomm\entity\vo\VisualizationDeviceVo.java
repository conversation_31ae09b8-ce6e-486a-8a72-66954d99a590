package com.mediacomm.entity.vo;

import com.mediacomm.entity.dao.VisualizationDevice;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VisualizationDeviceVo extends VisualizationDevice {
  private String roomName;
  private String sceneName;
  private String modelName;

  public VisualizationDeviceVo(VisualizationDevice device) {
    setId(device.getId());
    setName(device.getName());
    setDeviceIp(device.getDeviceIp());
    setHardcode(device.getHardcode());
    setDeviceModel(device.getDeviceModel());
    setDefaultScene(device.getDefaultScene());
    setProperties(device.getProperties());
    setRoomId(device.getRoomId());
    setVersion(device.getVersion());
  }
}
