package com.mediacomm.protocol;


import com.mediacomm.domain.Operate;
import com.mediacomm.domain.OperateParam;
import com.mediacomm.domain.Ptz;
import com.mediacomm.util.TcpClient;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class PelcoD implements Protocol {

  private Map<String, TcpClient> ptzClientMap = new HashMap<>();

  private TcpClient getClient(Ptz ptz) throws IOException {
    if (ptzClientMap.containsKey(ptz.getId())) {
      return ptzClientMap.get(ptz.getId());
    }
    TcpClient client = new TcpClient(ptz.getIp(), ptz.getPort());
    ptzClientMap.put(ptz.getId(), client);
    return client;
  }

  private static final byte up = 0X08;
  private static final byte down = 0x10;
  private static final byte left = 0x04;
  private static final byte right = 0x02;
  private static final byte zoomOut = 0x20;
  private static final byte zoomIn = 0x40;
  private static final byte loadPreset = 0x07;
  private static final byte savePreset = 0x03;

  private boolean sendCmd(TcpClient client, int address, String operate, OperateParam param)
      throws IOException {
    byte[] data = new byte[7];
    data[0] = (byte) 0xff;
    data[1] = (byte) address;
    byte speed = 0x2f;
    switch (operate) {
      case "left-up":
        data[2] = (byte) 0x00;
        data[3] = left | up;
        data[4] = speed;
        data[5] = speed;
        break;
      case "left-down":
        data[2] = (byte) 0x00;
        data[3] = left | down;
        data[4] = speed;
        data[5] = speed;
        break;
      case "right-up":
        data[2] = (byte) 0x00;
        data[3] = right | up;
        data[4] = speed;
        data[5] = speed;
        break;
      case "right-down":
        data[2] = (byte) 0x00;
        data[3] = right | down;
        data[4] = speed;
        data[5] = speed;
        break;
      case "up":
        data[2] = (byte) 0x00;
        data[3] = up;
        data[4] = (byte) 0x00;
        data[5] = speed;
        break;
      case "down":
        data[2] = (byte) 0x00;
        data[3] = down;
        data[4] = (byte) 0x00;
        data[5] = speed;
        break;
      case "left":
        data[2] = (byte) 0x00;
        data[3] = left;
        data[4] = speed;
        data[5] = (byte) 0x00;
        break;
      case "right":
        data[2] = (byte) 0x00;
        data[3] = right;
        data[4] = speed;
        data[5] = (byte) 0x00;
        break;
      case "zoom-out":
        data[2] = (byte) 0x00;
        data[3] = zoomOut;
        data[4] = (byte) 0x00;
        data[5] = (byte) 0x00;
        break;
      case "zoom-in":
        data[2] = (byte) 0x00;
        data[3] = zoomIn;
        data[4] = (byte) 0x00;
        data[5] = (byte) 0x00;
        break;
      case "stop-zoom":
      case "stop-move":
        data[2] = (byte) 0x00;
        data[3] = (byte) 0x00;
        data[4] = (byte) 0x00;
        data[5] = (byte) 0x00;
        break;
      case "load-preset":
        data[2] = (byte) 0x00;
        data[3] = loadPreset;
        data[4] = (byte) 0x00;
        data[5] = (byte) param.getPreset();
        break;
      case "save-preset":
        data[2] = (byte) 0x00;
        data[3] = savePreset;
        data[4] = (byte) 0x00;
        data[5] = (byte) param.getPreset();
        break;
      case "home":
        data[2] = (byte) 0x00;
        data[3] = loadPreset;
        data[4] = (byte) 0x00;
        data[5] = (byte) 0x00;
        break;
      default:
        return false;
    }
    data[6] = data[1];
    for (int i = 2; i < 6; i++) {
      data[6] = (byte) (data[6] + data[i]);
    }
    client.write(data);
    // client.close();
    return true;
  }

  @Override
  public boolean doOperate(Ptz ptz, Operate opt) {
    try {
      TcpClient client = getClient(ptz);
      return sendCmd(client, ptz.getAddress(), opt.getOperate(), opt.getParam());
    } catch (Exception e) {
      ptzClientMap.remove(ptz.getId());
      log.error(e.getMessage(), e);
      return false;
    }
  }
}
