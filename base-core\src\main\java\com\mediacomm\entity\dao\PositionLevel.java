package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.TinyIntToBooleanTypeHandler;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.PositionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "position_level", autoResultMap = true)
@Data
@Schema(name = "position_level", description = "房间等级")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PositionLevel extends SkyLinkDbEntity {
  @TableId(value = "level_name")
  private PositionType levelName;
  private String levelTitle; // 对应级别枚举值的名称
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private Boolean meeting; // 是否可用于会议
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private Boolean kvm;  // 是否可用于KVM
}
