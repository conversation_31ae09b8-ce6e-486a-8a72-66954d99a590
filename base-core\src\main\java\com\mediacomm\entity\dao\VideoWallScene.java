package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.TinyIntToBooleanTypeHandler;
import com.mediacomm.entity.message.LayoutData;
import com.mediacomm.entity.message.PanelData;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "video_wall_scene", description = "电视墙预案")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VideoWallScene extends SkyLinkDbEntity {
  @TableId(value = "scene_id", type = IdType.AUTO)
  private Integer sceneId; // 预案Id
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private boolean pollingScenesEnable; // 启动轮询预案
  private String name;
  private Integer wallId; // 所属电视墙Id
  @TableField(typeHandler = JacksonTypeHandler.class)
  private LayoutData layoutData; // 布局信息
  @TableField(typeHandler = JacksonTypeHandler.class)
  private PanelData panelData; // 窗口信息
}
