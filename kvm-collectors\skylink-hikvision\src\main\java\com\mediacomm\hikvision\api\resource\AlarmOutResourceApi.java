package com.mediacomm.hikvision.api.resource;


import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.resource.alarmout.SearchRequest;
import com.mediacomm.hikvision.entity.resource.alarmout.TimeRangeRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * Auto Create on 2022-08-24 14:02:46.
 *
 * <AUTHOR>
 */
public class AlarmOutResourceApi {

  /**
   * 增量获取报警输出数据.
   *
   * @param config           ArtemisConfig
   * @param timeRangeRequest request
   * @return 报警输出数据
   * @throws Exception Exception
   */
  public String timeRange(ArtemisConfig config, TimeRangeRequest timeRangeRequest)
      throws Exception {
    String timeRangeDataApi = Constants.ARTEMIS_PATH + "/api/resource/v1/alarmOut/timeRange";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, timeRangeDataApi);
    String body = JsonUtils.encode(timeRangeRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  /**
   * 查询报警输出列表.
   *
   * @param config        ArtemisConfig
   * @param searchRequest request
   * @return 报警输出列表
   * @throws Exception Exception
   */
  public String search(ArtemisConfig config, SearchRequest searchRequest) throws Exception {
    String searchDataApi = Constants.ARTEMIS_PATH + "/api/resource/v1/alarmOut/search";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, searchDataApi);
    String body = JsonUtils.encode(searchRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

}
