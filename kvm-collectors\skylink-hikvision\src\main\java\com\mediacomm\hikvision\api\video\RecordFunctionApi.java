package com.mediacomm.hikvision.api.video;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.video.record.LockRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * Auto Create on 2022-08-24 17:49:35.
 *
 * <AUTHOR>
 */
public class RecordFunctionApi {

  /**
   * 录像锁定与解锁.
   *
   * @param config      ArtemisConfig
   * @param lockRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String lock(ArtemisConfig config, LockRequest lockRequest) throws Exception {
    String lockDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/record/lock";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, lockDataApi);
    String body = JsonUtils.encode(lockRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

}
