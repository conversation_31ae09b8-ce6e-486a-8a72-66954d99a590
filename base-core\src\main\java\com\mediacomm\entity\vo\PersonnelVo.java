package com.mediacomm.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.entity.dao.Personnel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PersonnelVo extends Personnel {
  private String departmentName;

  /**
   * 数据对象到前端数据的转换.
   *
   * @param personnel .
   */
  public PersonnelVo(Personnel personnel) {
    setPersonnelId(personnel.getPersonnelId());
    setPersonnelDesc(personnel.getPersonnelDesc());
    setPersonnelMail(personnel.getPersonnelMail());
    setPersonnelEnable(personnel.isPersonnelEnable());
    setDepartment(personnel.getDepartment());
    setJobNumber(personnel.getJobNumber());
    setPersonnelTel(personnel.getPersonnelTel());
    setPersonnelName(personnel.getPersonnelName());
    setPersonnelCreateTime(personnel.getPersonnelCreateTime());
  }
}
