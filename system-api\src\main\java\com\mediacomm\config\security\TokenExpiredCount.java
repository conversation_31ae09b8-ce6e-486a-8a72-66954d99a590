package com.mediacomm.config.security;

import com.mediacomm.handler.websocket.WsSessionManager;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.util.RedisUtil;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 定时清除过期token.
 */
@Configuration
public class TokenExpiredCount {

  @Autowired
  private RedisUtil redisUtil;

  /**
   * 删除过期的token.
   */
  @Scheduled(fixedRate = 1000)
  public void checkTokenExpiredTime() {
    Optional<Map<String, String>> tokenMap = redisUtil.hmget(RedisKey.SA_TK_ID);
    Optional<Map<String, String>> expiredTimeMap = redisUtil.hmget(RedisKey.SA_TK_ET);
    expiredTimeMap.ifPresent(etMap -> etMap.keySet().forEach(
        key -> {
          if (Long.parseLong(etMap.get(key))
              - System.currentTimeMillis() / 1000 < 0) {
            tokenMap.ifPresent(tkMap -> redisUtil.hdel(RedisKey.SA_TK_ID, key)); // 删除过期token
            redisUtil.hdel(RedisKey.SA_TK_ET, key); // 删除token记录的过期时间
            synchronized (WsSessionManager.class) {
              WsSessionManager.removeAndCloseByToken(key);
            }
          }
        }
    ));
  }
}
