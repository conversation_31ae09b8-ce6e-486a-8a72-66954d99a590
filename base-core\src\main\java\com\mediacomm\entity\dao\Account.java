package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.mediacomm.config.db.TinyIntToBooleanTypeHandler;
import com.mediacomm.entity.vo.BusinessAuthVo;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 账号表.
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "account", description = "账号对象")
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Account extends SkyLinkDbEntity {
  @Schema(hidden = true)
  @TableId(value = "account_id", type = IdType.AUTO)
  private Integer accountId;
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private Integer personnel; // 关联人员Id
  @TableField(typeHandler = JacksonTypeHandler.class)
  private BusinessAuthVo businessAuth; // 授权的系统的业务
  @NotNull(message = "The account name must be not null.")
  @Size(min = 6, message = "The password must be longer than 6 characters.")
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private String accountPassword;
  @NotNull(message = "The account name must be not null.")
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private String accountName;
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private Integer accountHomePage; // 账号主页
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private boolean accountEnable; // 账号启用
  private String accountDesc; // 账号描述
  private String accountCreator; // 账号创建者
  private Long accountCreateTime; // 账号创建时间
}
