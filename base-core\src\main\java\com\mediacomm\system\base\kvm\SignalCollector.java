package com.mediacomm.system.base.kvm;

import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.DeviceModelSignal;
import com.mediacomm.entity.dao.EnvDevice;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.dao.Signal;
import com.mediacomm.entity.dao.SignalValue;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.SignalsAlarmEventRequestBody;
import com.mediacomm.entity.vo.EnvDeviceVo;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.entity.vo.KvmSlotVo;
import com.mediacomm.system.service.DeviceModelSignalService;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.mq.RabbitSender;
import com.mediacomm.util.task.SkyLinkTaskPool;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * .
 */
public abstract class SignalCollector {
  @Resource
  protected DeviceModelSignalService deviceModelSignalService;
  @Resource
  protected RedisUtil redisUtil;
  @Resource
  private RabbitSender sender;
  @Resource
  private SkyLinkTaskPool taskPool;
  // key -> signalId
  private Map<String, DeviceSignalValue> signalValueMap = new HashMap<>();
  private Map<String, Signal> signalMap = new HashMap<>();

  /**
   * 根据设备型号ID、信号ID生成实际新的信号.
   *
   * @param deviceModelId 设备型号ID，用于查找设备型号信号列表.
   * @param modelSignalId 模型信号ID，用于在设备型号信号列表中查找具体的信号.
   * @param actSignalId 实际信号ID，用于缓存和返回信号对象时使用.
   * @return 对应的信号对象，如果找不到则返回null.
   */
  protected Signal getSignal(Integer deviceModelId, String modelSignalId, String actSignalId) {
    String cacheId = String.format("%d.%s", deviceModelId, actSignalId);
    Signal signal = signalMap.get(cacheId);
    if (signal == null) {
      DeviceModelSignal ds = deviceModelSignalService.getById(deviceModelId);
      if (ds == null) {
        return null;
      }
      for (Signal dsSignal : ds.getSignals()) {
        if (dsSignal.getSignalId().equals(modelSignalId)) {
          signal = dsSignal;
          signal.setSignalId(actSignalId);
          signalMap.put(cacheId, signal);
          break;
        }
      }
    }
    return signal;
  }

  protected Signal getSignal(Integer deviceModelId, String modelSignalId) {
    Signal signal = signalMap.get(modelSignalId);
    if (signal == null) {
      DeviceModelSignal ds = deviceModelSignalService.getById(deviceModelId);
      if (ds != null) {
        for (Signal dsSignal : ds.getSignals()) {
          if (dsSignal.getSignalId().equals(modelSignalId)) {
            signal = dsSignal;
            signalMap.put(modelSignalId, signal);
            break;
          }
        }
      }
    }
    return signal;
  }

  protected DeviceSignalValue setValue(Signal signal, SignalValue value, KvmMaster master) {
    DeviceSignalValue signalValue = setValue(signal, value);
    signalValue.setMasterId(master.getMasterId());
    signalValue.setDeviceId(master.getMasterId());
    signalValue.setDeviceName(master.getName());
    return signalValue;
  }

  protected DeviceSignalValue setValue(Signal signal, SignalValue value, KvmAsset kvmAsset) {
    DeviceSignalValue signalValue = setValue(signal, value);
    signalValue.setMasterId(kvmAsset.getMasterId());
    signalValue.setDeviceId(kvmAsset.getAssetId());
    signalValue.setDeviceName(kvmAsset.getName());
    return signalValue;
  }

  protected DeviceSignalValue setValue(Signal signal, SignalValue value, EnvDevice envDevice) {
    DeviceSignalValue signalValue = setValue(signal, value);
    signalValue.setMasterId(envDevice.getId());
    signalValue.setDeviceId(envDevice.getId());
    signalValue.setDeviceName(envDevice.getName());
    return signalValue;
  }

  protected DeviceSignalValue setValue(Signal signal, SignalValue value, KvmSlot slot) {
    DeviceSignalValue signalValue = setValue(signal, value);
    signalValue.setMasterId(slot.getMasterId());
    signalValue.setDeviceId(slot.getSlotId());
    signalValue.setDeviceName(slot.getName());
    return signalValue;
  }

  protected void checkMasterSignalValue(Collection<KvmMaster> kvmMasters, DeviceType type) {
    kvmMasters.forEach((kvmMaster) -> checkDeviceSignalValue(
            type.getSubSystem(), kvmMaster.getMasterId(),
            RedisSignalKey.getDeviceStatusKey(type.getDeviceType(),
                    kvmMaster.getMasterId())));
  }

  protected void checkEnvSignalValue(Collection<EnvDeviceVo> envDevices) {
    envDevices.forEach((envDevice) -> checkDeviceSignalValue(
            DeviceType.valueOf(envDevice.getDeviceType()).getSubSystem(), envDevice.getId(),
            RedisSignalKey.getDeviceStatusKey(envDevice.getDeviceType(),
                    envDevice.getId())));
  }

  protected void checkAssetSignalValue(Collection<KvmAssetVo> assets) {
    assets.forEach((asset) -> checkDeviceSignalValue(
            DeviceType.valueOf(asset.getDeviceType()).getSubSystem(),
            asset.getMasterId(),
            RedisSignalKey.getDeviceStatusKey(asset.getDeviceType(),
                    asset.getAssetId())));
  }

  protected void checkSlotSignalValue(Collection<KvmSlotVo> slots) {
    slots.forEach((slot) -> checkDeviceSignalValue(
            DeviceType.valueOf(slot.getDeviceType()).getSubSystem(),
            slot.getMasterId(),
            RedisSignalKey.getDeviceStatusKey(slot.getDeviceType(),
                    slot.getSlotId())));
  }

  /**
   * 将信号值缓存到指定的Map中.
   */
  protected void addDeviceSignalValue(KvmMaster master, String redisSignalKey,
                                      SignalValue signalValue, Map<String, String> signalValueMap) {
    Signal signal = getSignal(master.getDeviceModel(), redisSignalKey);
    if (signal != null) {
      DeviceSignalValue deviceSignalValue = setValue(signal, signalValue, master);
      signalValueMap.put(signal.getSignalId(), JsonUtils.encode(deviceSignalValue));
    }
  }

  /**
   * 将信号值缓存到指定的Map中.
   */
  protected void addDeviceSignalValue(KvmAssetVo asset, String redisSignalKey,
                                      SignalValue signalValue, Map<String, String> signalValueMap) {
    Signal signal = getSignal(asset.getDeviceModel(), redisSignalKey);
    if (signal != null) {
      DeviceSignalValue deviceSignalValue = setValue(signal, signalValue, asset);
      signalValueMap.put(signal.getSignalId(), JsonUtils.encode(deviceSignalValue));
    }
  }

  protected void addDeviceSignalValue(KvmAssetVo asset, String modelSignalId, String redisSignalKey,
                                      String newSignalName, SignalValue signalValue,
                                      Map<String, String> signalValueMap) {
    Signal signal = getSignal(asset.getDeviceModel(), modelSignalId, redisSignalKey);
    if (signal != null) {
      signal.setSignalName(newSignalName);
      DeviceSignalValue deviceSignalValue = setValue(signal, signalValue, asset);
      signalValueMap.put(signal.getSignalId(), JsonUtils.encode(deviceSignalValue));
    }
  }

  /**
   * 添加计数属性.
   *
   * @param asset 外设.
   * @param key 属性key.
   * @param totalNum 实际总数.
   * @return true:添加成功, false:添加失败.
   */
  protected boolean addNumProperty(KvmAsset asset, String key, int totalNum) {
    Integer num = Property.findValueByKey(asset.getProperties(), key,
            null, Integer.class);
    if (num == null && totalNum > 0) {
      num = totalNum;
      asset.getProperties().add(new Property(key, String.valueOf(num)));
      return true;
    }
    return false;
  }

  protected void checkDeviceSignalValue(SubSystemType type, String masterId,
                                        String redisKey) {
    SignalsAlarmEventRequestBody signalAlarmEventRequestBody = SignalsAlarmEventRequestBody
            .builder()
            .subSystemType(type)
            .redisKey(redisKey)
            .build();
    MqRequest<SignalsAlarmEventRequestBody> mq = new MqRequest<>();
    mq.setMasterId(masterId);
    mq.setBody(signalAlarmEventRequestBody);
    taskPool.addAsyncTask(() ->  sender.syncSend(RoutingKey.MONITOR_DEVICE_ALARM_BUILD, mq));
  }

  private DeviceSignalValue setValue(Signal signal, SignalValue value) {
    DeviceSignalValue deviceSignalValue = signalValueMap.get(signal.getSignalId());
    if (deviceSignalValue == null) {
      deviceSignalValue = new DeviceSignalValue();
      deviceSignalValue.setSignalId(signal.getSignalId());
      deviceSignalValue.setSignalName(signal.getSignalName());
      deviceSignalValue.setMeasurement(signal.getMeasurementUnit());
      deviceSignalValue.setExpire(100);
      signalValueMap.put(signal.getSignalId(), deviceSignalValue);
    }
    deviceSignalValue.setStatusName(signal.getNormalDesc());
    deviceSignalValue.setUpdateTime(new Date().getTime());
    deviceSignalValue.setSignalValue(signal, value);
    return deviceSignalValue;
  }
}
