package com.mediacomm.hikvision.api.video;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.video.ptzs.ControllingRequest;
import com.mediacomm.hikvision.entity.video.ptzs.SelZoomRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * Auto Create on 2022-08-24 17:49:35.
 *
 * <AUTHOR>
 */
public class PtzsFunctionApi {

  /**
   * 根据监控点编号进行云台操作.
   *
   * @param controllingRequest 请求Body
   * @return json
   * @throws Exception Exception
   */
  public static String controlling(ArtemisConfig config, ControllingRequest controllingRequest)
      throws Exception {
    String controllingDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/ptzs/controlling";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, controllingDataApi);
    String body = JsonUtils.encode(controllingRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  /**
   * 监控点3D放大.
   *
   * @param selZoomRequest 请求Body
   * @return json
   * @throws Exception Exception
   */
  public static String selZoom(ArtemisConfig config, SelZoomRequest selZoomRequest)
      throws Exception {
    String selZoomDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/ptzs/selZoom";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, selZoomDataApi);
    String body = JsonUtils.encode(selZoomRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }
}
