package com.mediacomm.hikvision.api.video;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.video.cameras.PlaybackUrlsRequest;
import com.mediacomm.hikvision.entity.video.cameras.PreviewUrlsRequest;
import com.mediacomm.hikvision.entity.video.cameras.PreviewUrlsResponse;
import com.mediacomm.hikvision.entity.video.cameras.TalkUrlsRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;

/**
 * Auto Create on 2022-08-24 17:49:35.
 *
 * <AUTHOR>
 */
public class CamerasFunctionsApi {

  /**
   * 获取监控点预览取流URLv2.
   */
  public static PreviewUrlsResponse previewUrls(ArtemisConfig config,
                                                PreviewUrlsRequest previewUrlsRequest)
      throws Exception {
    String previewUrlsDataApi = Constants.ARTEMIS_PATH + "/api/video/v2/cameras/previewURLs";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, previewUrlsDataApi);
    String body = JsonUtils.encode(previewUrlsRequest);
    String artemisString =
        ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
            ContentType.CONTENT_TYPE_JSON);
    return JsonUtils.decode(artemisString, PreviewUrlsResponse.class);

  }

  /**
   * 获取监控点回放取流URLv2.
   *
   * @param config              ArtemisConfig
   * @param playbackUrlsRequest Request
   * @return 流RTSP地址
   * @throws Exception Exception
   */
  public String playbackUrls(ArtemisConfig config, PlaybackUrlsRequest playbackUrlsRequest)
      throws Exception {
    String playbackUrlsDataApi = Constants.ARTEMIS_PATH + "/api/video/v2/cameras/playbackURLs";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, playbackUrlsDataApi);
    String body = JsonUtils.encode(playbackUrlsRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  //

  /**
   * 查询对讲URL.
   *
   * @param config          ArtemisConfig
   * @param talkUrlsRequest Request
   * @return 对讲URL
   * @throws Exception Exception
   */
  public static String talkUrls(ArtemisConfig config, TalkUrlsRequest talkUrlsRequest)
      throws Exception {
    String talkUrlsDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/cameras/talkURLs";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, talkUrlsDataApi);
    String body = JsonUtils.encode(talkUrlsRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }
}
