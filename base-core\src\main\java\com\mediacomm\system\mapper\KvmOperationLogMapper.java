package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mediacomm.entity.dao.KvmOperationLog;
import com.mediacomm.entity.vo.KvmOperationLogVo;
import java.util.Collection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * KVM操作日志.
 */
@Mapper
public interface KvmOperationLogMapper extends BaseMapper<KvmOperationLog> {

  /**
   * 获取操作日志总数.
   */
  @Select("select COUNT(*) "
      + "from kvm_operation_log as kol JOIN kvm_master as km "
      + "on kol.master_id = km.master_id "
      + "where kol.operation_time between #{startTime} and #{endTime}")
  int countBetweenStartAndEndTime(
      @Param("startTime") long startTime, @Param("endTime") long endTime);

  /**
   * 获取操作日志总数.
   */
  @Select("select COUNT(*) "
      + "from kvm_operation_log "
      + "where master_id = #{masterId} AND operation_time between #{startTime} and #{endTime} ")
  int countBetweenStartAndEndTimeByMasterId(
      @Param("startTime") long startTime, @Param("endTime") long endTime,
      @Param("masterId") String masterId);

  /**
   * .
   */
  @Select("select kol.*, km.name as master_name "
      + "from kvm_operation_log as kol JOIN kvm_master as km "
      + "on kol.master_id = km.master_id "
      + "where kol.operation_time between #{startTime} and #{endTime}")
  Collection<KvmOperationLogVo> allBetweenStartAndEndTime(
      @Param("startTime") long startTime, @Param("endTime") long endTime);

  /**
   * .
   */
  @Select("select kol.*, km.name as master_name "
      + "from kvm_operation_log as kol JOIN kvm_master as km "
      + "on kol.master_id = km.master_id "
      + "where kol.operation_time between #{startTime} and #{endTime} "
      + "order by kol.operation_time DESC "
      + "limit #{offset}, #{pageSize}")
  Collection<KvmOperationLogVo> pageBetweenStartAndEndTime(
      @Param("offset") int offset, @Param("pageSize") int pageSize,
      @Param("startTime") long startTime, @Param("endTime") long endTime);

  /**
   * .
   */
  @Select("select kol.*, km.name as master_name "
      + "from kvm_operation_log as kol JOIN kvm_master as km "
      + "on kol.master_id = km.master_id and kol.master_id = #{masterId} "
      + "where kol.operation_time between #{startTime} and #{endTime}")
  Collection<KvmOperationLogVo> findBetweenStartAndEndTimeByMasterId(
      @Param("startTime") long startTime, @Param("endTime") long endTime,
      @Param("masterId") String masterId);

  /**
   * .
   */
  @Select("select kol.*, km.name as master_name "
      + "from kvm_operation_log as kol JOIN kvm_master as km "
      + "on kol.master_id = km.master_id and kol.master_id = #{masterId} "
      + "where kol.operation_time between #{startTime} and #{endTime} "
      + "order by kol.operation_time DESC "
      + "limit #{offset}, #{pageSize}")
  Collection<KvmOperationLogVo> pageBetweenStartAndEndTimeByMasterId(
      @Param("offset") int offset, @Param("pageSize") int pageSize,
      @Param("startTime") long startTime, @Param("endTime") long endTime,
      @Param("masterId") String masterId);
}
