package com.mediacomm.hikvision.entity.video.cruiseroutes;

import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 *
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateRequest {
  private String cameraIndexCode;
  private Integer cruiseIndex;
  private String cruiseName;
  private ArrayList<Points> points;
}
