package com.mediacomm.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KvmMouseDataDto implements Serializable {
  private String txId;
  private List<String> mouseKeys;
  private int width;
  private int height;
  private int moveX;
  private int moveY;
  private double delay;
  private boolean scrollDirection;
  private int scroll;
}
