package com.mediacomm.config.websocket.client.notify;

import com.mediacomm.caesar.domain.CaesarChangeSeatPanelsSignal;
import com.mediacomm.caesar.domain.CaesarDevice;
import com.mediacomm.caesar.domain.CaesarDeviceSn;
import com.mediacomm.caesar.domain.CaesarDeviceStatus;
import com.mediacomm.caesar.domain.CaesarRedundantMode;
import com.mediacomm.caesar.domain.CaesarRx;
import com.mediacomm.caesar.domain.CaesarSeat;
import com.mediacomm.caesar.domain.CaesarSeatChange;
import com.mediacomm.caesar.domain.CaesarServerStatus;
import com.mediacomm.caesar.domain.CaesarSwitchSeatSignal;
import com.mediacomm.caesar.domain.CaesarSwitchVwSignal;
import com.mediacomm.caesar.domain.CaesarTx;
import com.mediacomm.caesar.domain.CaesarUser;
import com.mediacomm.caesar.domain.CaesarVideoWall;
import com.mediacomm.caesar.domain.CaesarVwScene;
import com.mediacomm.caesar.domain.kaito.KaitoVideoWallChange;
import com.mediacomm.config.websocket.client.notify.domain.VideoWallChange;
import com.mediacomm.util.websocket.client.NoticeTemplate;

/**
 * 接收Caesar WebSocket消息.
 */
public class CaesarMsg {

  /**
   * .
   */
  public static class CaesarTxNotice extends NoticeTemplate<CaesarTx> {}

  /**
   * .
   */
  public static class CaesarRxNotice extends NoticeTemplate<CaesarRx> {}

  /**
   * .
   */
  public static class CaesarUserNotice extends NoticeTemplate<CaesarUser> {}

  /**
   * .
   */
  public static class CaesarSeatNotice extends NoticeTemplate<CaesarSeat> {}

  /**
   * .
   */
  public static class CaesarDeviceNotice extends NoticeTemplate<CaesarDevice> {}

  /**
   * .
   */
  public static class CaesarDeviceSnNotice extends NoticeTemplate<CaesarDeviceSn> {}

  /**
   * .
   */
  public static class CaesarServerStatusNotice extends NoticeTemplate<CaesarServerStatus> {}

  /**
   * .
   */
  public static class CaesarDeviceStatusNotice extends NoticeTemplate<CaesarDeviceStatus> {}

  /**
   * .
   */
  public static class CaesarSwitchSeatSignalNotice extends NoticeTemplate<CaesarSwitchSeatSignal> {}

  public static class CaesarChangeSeatPanelsNotice
      extends NoticeTemplate<CaesarChangeSeatPanelsSignal> {}

  /**
   * .
   */
  public static class CaesarVwSceneNotice extends NoticeTemplate<CaesarVwScene> {}

  /**
   * .
   */
  public static class CaesarVideoWallNotice extends NoticeTemplate<CaesarVideoWall> {}

  /**
   * .
   */
  public static class CaesarVideoWallChangeNotice extends NoticeTemplate<VideoWallChange> {}

  /**
   * .
   */
  public static class KaitoVideoWallChangeNotice extends NoticeTemplate<KaitoVideoWallChange> {}

  /**
   * .
   */
  public static class CaesarSeatChangeNotice extends NoticeTemplate<CaesarSeatChange> {}

  /**
   * .
   */
  public static class CaesarSwitchVwSignalNotice extends NoticeTemplate<CaesarSwitchVwSignal> {}

  /**
   * .
   */
  public static class CaesarRedundantModeNotice extends NoticeTemplate<CaesarRedundantMode> {}
}
