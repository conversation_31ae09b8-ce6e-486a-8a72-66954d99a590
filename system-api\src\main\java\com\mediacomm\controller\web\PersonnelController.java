package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Personnel;
import com.mediacomm.entity.vo.PersonnelVo;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.PersonnelService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.sysenum.OperateType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "人员管理", description = "人员配置的API")
@RestController
@RequestMapping(ResUrlDef.PERSONNEL)
public class PersonnelController extends SkyLinkController<Personnel, PersonnelService> {

  @Operation(summary = "获取所有人员信息")
  @GetMapping("/personnels")
  public Result<Collection<PersonnelVo>> getPersonnels() {
    return Result.ok(service.all());
  }

  @Operation(summary = "获取指定部门内的人员信息")
  @Parameter(name = "departmentId", description = "部门Id", required = true)
  @Parameter(name = "bound", description = "是否已被账号绑定")
  @GetMapping("/department")
  public Result<Collection<Personnel>> getPersonnelsByDepartmentId(@RequestParam("departmentId")
                                                                   Integer departmentId,
                                                                   @RequestParam(value = "bound",
                                                                       required = false)
                                                                   Boolean bound) {
    return bound != null && bound ? Result.ok(service.listByDepartmentIdAndBound(departmentId)) :
        Result.ok(service.listByForeignKey(departmentId));
  }

  @Operation(summary = "通过Id获取指定人员信息")
  @GetMapping("/{id}")
  public Result<Personnel> getPersonnel(@PathVariable Integer id) {
    return Result.ok(service.oneById(id));
  }

  /**
   * 修改指定人员.
   */
  @OperationLogRecord(title = "修改指定人员", operateType = OperateType.UPDATE,
      requestBody = "#{personnel}")
  @Operation(summary = "通过Id修改指定人员信息")
  @PutMapping("/{id}")
  public Result<Personnel> updatePersonnel(@PathVariable Integer id,
                                           @RequestBody Personnel personnel) {
    personnel.setPersonnelId(id);
    service.updateById(personnel);
    return Result.ok();
  }

  @OperationLogRecord(title = "删除指定人员", operateType = OperateType.DELETE)
  @Operation(summary = "通过Id删除指定人员信息")
  @DeleteMapping("/{id}")
  public Result<Personnel> delPersonnel(@PathVariable Integer id) {
    service.removeById(id);
    return Result.ok();
  }

  @OperationLogRecord(title = "批量删除人员", operateType = OperateType.DELETE, requestBody = "#{ids}")
  @Operation(summary = "通过Id批量删除人员信息")
  @DeleteMapping("/personnels")
  public Result<Personnel> delPersonnels(@RequestBody Collection<Integer> ids) {
    service.removeBatchByIds(ids);
    return Result.ok();
  }

  @OperationLogRecord(title = "新增人员", operateType = OperateType.INSERT,
      requestBody = "#{personnel}")
  @Operation(summary = "新增人员信息")
  @PostMapping
  public Result<Personnel> addPersonnel(@RequestBody Personnel personnel) {
    service.save(personnel);
    return Result.ok();
  }

}
