package com.mediacomm.db;

import com.mediacomm.util.RedisUtil;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * .
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class RedisTest {
  @Autowired
  RedisUtil redisUtil;

  @Test
  public void testGetKeys() {
    System.out.println(redisUtil.getKeys("spring*"));
    System.out.println("redisUtil.getStr(\"springboot\") = " + redisUtil.getStr("springboot"));
  }

  @Test
  public void testMapSet() {
    redisUtil.hset("testMapSet", "1", "1");
    redisUtil.hset("testMapSet", "2", "2");
    redisUtil.hset("testMapSet", "3", "3");
    redisUtil.hset("testMapSet", "4", "4");
    redisUtil.hset("testMapSet", "5", "5");
    Optional<Map<String, String>> map = redisUtil.hmget("testMapSet");
    Assert.assertTrue(map.isPresent());
    int i = 1;
    for (Map.Entry<String, String> entry : map.get().entrySet()) {
      Assert.assertEquals(entry.getKey(), String.valueOf(i));
      Assert.assertEquals(entry.getValue(), String.valueOf(i));
      i++;
    }
  }

  @Test
  public void testMapSetAll() {
    Map<String, String> mapData = new HashMap<>();
    mapData.put("1", "1");
    mapData.put("2", "2");
    mapData.put("3", "3");
    redisUtil.hset("testMapSet2", mapData);
    Optional<Map<String, String>> map = redisUtil.hmget("testMapSet2");
    Assert.assertTrue(map.isPresent());
    int i = 1;
    for (Map.Entry<String, String> entry : map.get().entrySet()) {
      Assert.assertEquals(entry.getKey(), String.valueOf(i));
      Assert.assertEquals(entry.getValue(), String.valueOf(i));
      i++;
    }
  }
}
