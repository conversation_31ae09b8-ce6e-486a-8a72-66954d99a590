[supervisord]
nodaemon = true
user = root
logfile = /var/log/supervisor/supervisord.log
pidfile = /var/log/supervisor/supervisord.pid

[inet_http_server]
port = 0.0.0.0:9002
username = root
password = mediacomm

[supervisorctl]
serverurl = http://localhost:9002
username = root
password = mediacomm

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:nginx]
command=/usr/sbin/nginx -g 'daemon off;'
autorestart = true
redirect_stderr = true
stdout_logfile = /var/log/supervisor/nginx.log
priority = 1

[program:service]
directory = /
command = java -jar system-api-0.0.1-SNAPSHOT.jar
autorestart = true
redirect_stderr = true
stdout_logfile = /var/log/supervisor/skylink-server.log
priority = 2
