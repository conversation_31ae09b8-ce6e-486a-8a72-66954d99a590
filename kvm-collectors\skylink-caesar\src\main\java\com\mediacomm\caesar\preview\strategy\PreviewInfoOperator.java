package com.mediacomm.caesar.preview.strategy;

import com.mediacomm.caesar.preview.PreviewInfoGetter;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import java.util.Collection;
import java.util.Map;

/**
 * .
 */
public interface PreviewInfoOperator extends PreviewInfoGetter {


  void setPollingPreviewChannels(Collection<KvmPreviewAsso> channels);

  /**
   * 配置实时预览数据.
   *
   * @param txId 预览TX.
   * @param channel 预览通道.
   * @param is4k 是否为4k信号.
   * @param requestKey 请求KEY.
   * @param isImportant 是否为重要请求.
   * @return 配置成功返回true.
   */
  boolean setRealTimePreview(String txId, KvmPreviewAsso channel,
                             boolean is4k, String requestKey, boolean isImportant);

  /**
   * 配置实时预览数据.
   *
   * @param txId 预览TX，当前必须是在预览状态.
   * @param channel 预览通道.
   * @param is4k 是否为4k信号.
   * @return 配置成功返回true.
   */
  boolean setRealTimePreview(String txId, KvmPreviewAsso channel, boolean is4k);

  /**
   * 停止实时预览.
   *
   * @param txId txid.
   * @return 停止成功，返回true.
   */
  boolean stopRealTimePreview(String txId);

  /**
   * 配置轮询数据.
   *
   * @param txId TX ID.
   * @param requestKey 请求KEY.
   * @param isImportant 是否为重要请求.
   * @return .
   */
  boolean setPollingPreview(String txId, String requestKey, boolean isImportant);

  /**
   * 配置轮询数据.
   *
   * @param txId tx id，此TX必须是在预览状态.
   * @return .
   */
  boolean setPollingPreview(String txId);

  /**
   * 停止轮询预览.
   *
   * @param txId  TX ID.
   * @return 如果停止成功，返回true.
   */
  boolean stopPollingPreview(String txId);

  /**
   * 更新请求key对应的tx集合，如果一个TX没有对应的requestkey，预览信息就会被删除.
   */
  void updateRequestKeyTxes(String requestKey, boolean isRequestImportant, Collection<String> txes);

  void updateLastPreviewUsedTime(String txId, long milli);

  void updateRequestKeyLastUsedTime(String requestKey, long milli);

  /**
   * 获取仅被requestKey引用的TX的最近的使用时间.
   *
   * @param requestKey .
   * @return 最近的使用时间，如果没有仅被requestKey以用的TX，返回0.
   */
  long getTxConfinedToRequestLastUsedTime(String requestKey);

  /**
   * 获取请求key最后使用的时间.
   */
  Map<String, Long> getAllRequestKeyLastUsedTime();

  void removeRequestKey(String requestKey);

  void clearData();
}
