package com.mediacomm.caesar.domain.vp7;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum R2P4FStatus {
  UNKNOWN(-1),
  SUCCESS(1),
  NOT_SIGNAL(2),
  READ_TIMEOUT(3),
  WRITE_TIMEOUT(4),
  SOURCE_LIMIT(5);

  private int code;

  public static R2P4FStatus getByCode(int code) {
    for (R2P4FStatus status : values()) {
      if (status.code == code) {
        return status;
      }
    }
    return UNKNOWN;
  }
}
