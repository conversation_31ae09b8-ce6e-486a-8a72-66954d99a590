package com.mediacomm.caesar.snapshot;

import static com.mediacomm.caesar.snapshot.PreviewTestHelper.lockAtPath;
import static com.mediacomm.caesar.snapshot.PreviewTestHelper.unlockAtPath;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.google.common.collect.Maps;
import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.domain.RedundantModeEnum;
import com.mediacomm.caesar.preview.PreviewInfoGetter;
import com.mediacomm.caesar.preview.PreviewManager;
import com.mediacomm.caesar.preview.device.FourScreenRxPreviewOperatorImpl;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.preview.strategy.PreviewMap;
import com.mediacomm.caesar.preview.strategy.PreviewStrategy;
import com.mediacomm.caesar.preview.strategy.impl.DeviceDataGetterImpl;
import com.mediacomm.caesar.preview.strategy.impl.FourScreenRxPreviewStrategy;
import com.mediacomm.caesar.task.PollingPreviewRunner;
import com.mediacomm.caesar.task.PreviewThread;
import com.mediacomm.caesar.util.CaesarDbServiceUtil;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.task.SkyLinkTaskPool;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * .
 */
public class FourScreenRxPreviewStrategyTest {
  private final String masterId = UUID.randomUUID().toString();
  private final RedisUtil redisUtil = mock(RedisUtil.class);

  @Before
  public void before() {
    Path path = Paths.get("/tmp/snapshot/caesar/");
    if (!path.toFile().exists()) {
      return;
    }
    for (File file : path.toFile().listFiles()) {
      file.delete();
    }
  }

  @Test
  public void testAddPreview() throws IllegalAccessException {
    // 四画面Rx预览只有轮巡
    int cnt = 10;
    ObjectIds request = SimplePreviewStrategyTest.makeRequest(true, 0, cnt);
    DeviceDataGetter dataGetter = mock(DeviceDataGetter.class);
    FourScreenRxPreviewStrategy strategy = new FourScreenRxPreviewStrategy(dataGetter);
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");
    strategy.addPreview(request);

    Assert.assertTrue(previewMap.getPollingPreviewChannels().isEmpty());
    for (int i = 0; i < cnt; i++) {
      String txId = request.getIds().get(i);
      Assert.assertTrue(previewMap.isPollingPreviewing(txId));
      Assert.assertTrue(previewMap.isPreviewing(txId));
      PreviewInfoGetter.PollingPreviewStatus pollingStatus = previewMap.getPollingPreviewStatus(txId);
      Assert.assertEquals(PreviewInfoGetter.PollingPreviewStatus.POLLING, pollingStatus);
      // 上次使用时间
      long time = previewMap.getLastPreviewUsedTime(txId);
      Assert.assertTrue(System.currentTimeMillis() - time < 1000);
    }
  }

  @Test
  public void testGetPreview() throws IOException, InterruptedException, IllegalAccessException {
    int previewTxCnt = 10;
    CaesarRunnerForeignMock cmdServerMock = new CaesarRunnerForeignMock(2, previewTxCnt, masterId);
    when(redisUtil.getStr(RedisKey.KC_SC_SS)).thenReturn(Optional.of(String.valueOf(0)));
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, masterId)))
            .thenReturn(Optional.of(String.valueOf(RedundantModeEnum.ALONE)));
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, masterId)))
            .thenReturn(Optional.of(String.valueOf(RedundantModeEnum.ALONE)));
    when(redisUtil.lock(RedisKey.KC_SC_SL, String.valueOf(System.currentTimeMillis()))).thenReturn(true);
    cmdServerMock.setRedisUtil(redisUtil);
    DeviceDataGetter deviceDataGetter = new DeviceDataGetterImpl(cmdServerMock, masterId);
    SkyLinkTaskPool taskPool = new PreviewTestHelper.TaskRunnerMock();
    PreviewManager previewManager = mockFourRxPreviewManager(cmdServerMock, taskPool);
    PreviewMapMock previewMapMock = new PreviewMapMock(cmdServerMock.previewAssoMap);
    changePreviewStrategy(previewManager, cmdServerMock, previewMapMock, taskPool, deviceDataGetter);

    int maxPollingChannelCnt = previewTxCnt * 2 * 2; // 全部都是4K，两次预览的TX合在一起轮询
    int maxWaitTime = (int) (maxPollingChannelCnt * 1.0
            / cmdServerMock.previewAssoMap.keySet().size() * 500);
    for (int iterator = 0; iterator < 4; iterator++) {
      ObjectIds request = makeRandomRequest(cmdServerMock.idTxMap, true, previewTxCnt);
      previewManager.addPreview(masterId, request);
      // 等待足够时间获取第一张图
      int waitTime = maxWaitTime;
      while (waitTime > 0) {
        int sleepTime = Math.min(waitTime, FourScreenRxPreviewStrategy.PREVIEW_TIME_OUT_MILLI / 2);
        Thread.sleep(sleepTime);
        waitTime -= sleepTime;
        request.getIds().forEach(id -> previewManager.onGetSnapshot(masterId, id));
      }
      for (int i = 0; i < 60; i++) {
        Thread.sleep(100);
        PreviewTestHelper
                .checkTxPreview(request.getIds(), true, deviceDataGetter::makeTxSavePath,
                        maxWaitTime);
        request.getIds().forEach(id -> previewManager.onGetSnapshot(masterId, id));
      }
    }
    System.out.println(String
            .format("%dms waiting time for %d preview tx and %s polling channel.", maxWaitTime,
                    previewTxCnt, cmdServerMock.previewAssoMap.toString()));
    previewManager.refreshSnapshotPreviewStatus(Collections.emptySet());
  }

  static class CaesarRunnerForeignMock extends CaesarDbServiceUtil {
    @Setter
    @Getter
    private RedisUtil redisUtil;
    public KvmMaster master;
    public Map<String, KvmAsset> idTxMap = new HashMap<>();
    public Map<String, KvmAsset> idRxMap = new HashMap<>();
    public Map<String, Collection<KvmPreviewAsso>> previewAssoMap = new HashMap<>();
    private KvmAssetService kvmAssetService;

    public static String getPreviewUrl(KvmPreviewAsso asso) {
      return Paths
              .get(System.getProperty("java.io.tmpdir"),
                      FourScreenRxPreviewOperatorImpl.class.getSimpleName(),
                      asso.getRxId() + "." + asso.getSeq() + ".txt")
              .toString();
    }

    public CaesarRunnerForeignMock(int previewRxCnt, int txCnt, String masterId) {
      master = new KvmMaster();
      master.setMasterId(masterId);
      // Tx
      for (int i = 0; i < txCnt; i++) {
        KvmAsset asset = new KvmAsset();
        asset.setAssetId(UUID.randomUUID().toString());
        asset.setHardcode("hardcode." + i);
        if (i < txCnt / 2) {
          asset.getProperties()
                  .add(new Property("resolution", "2K"));
        } else {
          asset.getProperties()
                  .add(new Property("resolution", "4K"));
        }
        asset.setDeviceId(i);
        asset.setMasterId(masterId);
        asset.setDeviceModel(DeviceType.CAESAR_TX.getDeviceTypeId());
        idTxMap.put(asset.getAssetId(), asset);
      }
      // 4Rx
      for (int i = 0; i < previewRxCnt; i++) {
        KvmAsset asset = new KvmAsset();
        asset.setAssetId(UUID.randomUUID().toString());
        asset.setHardcode("hardcode." + i);
        asset.setDeviceId(i);
        asset.setMasterId(masterId);
        asset.setDeviceModel(DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId());
        asset.getCollectorProperties().add(
                new Property(PropertyKeyConst.PREVIEW_ADDRESS, "127.0.0.1"));
        idRxMap.put(asset.getAssetId(), asset);
      }

      for (String rxId : idRxMap.keySet()) {
        Collection<KvmPreviewAsso> assos = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
          KvmPreviewAsso asso = new KvmPreviewAsso();
          asso.setRxId(rxId);
          asso.setSeq(i + 1);
          asso.setUrl(getPreviewUrl(asso));
          assos.add(asso);
        }
        previewAssoMap.put(rxId, assos);
      }
      kvmAssetService = new KvmAssetServiceMock(idTxMap, idRxMap);
    }

    @Override
    public CaesarPreviewStrategyMode getStrategyMode(String masterId) {
      return CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX;
    }

    @Override
    public KvmAssetService getKvmAssetService() {
      return kvmAssetService;
    }
  }

  static class KvmAssetServiceMock extends KvmAssetService {
    Map<String, KvmAsset> assetsMock = new HashMap<>();

    @SafeVarargs
    public KvmAssetServiceMock(Map<String, KvmAsset>... assets) {
      for (Map<String, KvmAsset> asset : assets) {
        assetsMock.putAll(asset);
      }
    }

    @Override
    public Collection<KvmAssetVo> allByDeviceModelId(Integer modelId, String masterId) {
      Collection<KvmAssetVo> vos = new ArrayList<>();
      assetsMock.forEach((assetId, asset) -> {
        if (Objects.equals(asset.getDeviceModel(), modelId)) {
          KvmAssetVo vo = new KvmAssetVo(asset);
          vos.add(vo);
        }
      });
      return vos;
    }

    @Override
    public KvmAsset getById(Serializable id) {
      KvmAsset asset = assetsMock.get(id);
      return asset;
    }
  }

  static class FourScreenRxPreviewOperatorImplMock extends FourScreenRxPreviewOperatorImpl {

    private final SkyLinkTaskPool taskRunner;

    public FourScreenRxPreviewOperatorImplMock(SkyLinkTaskPool taskRunner,
                                               DeviceDataGetter deviceDataGetter) {
      super(taskRunner, deviceDataGetter);
      this.taskRunner = taskRunner;
    }

    @Override
    public CompletableFuture<Map<String, Boolean>> openPanelAndGetPreview(
            Map<String, KvmPreviewAsso> txChannelMap,
            Map<String, String> txSavePathMap,
            Collection<KvmPreviewAsso> unusedChannels) {
      // 根据rx id分组
      Map<String, Map<String, KvmPreviewAsso>> group = txChannelMap.entrySet().stream().collect(
              Collectors.groupingBy(item -> item.getValue().getRxId(),
                      Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
      CompletableFuture<Map<String, Boolean>> result = new CompletableFuture<>();
      result.complete(new HashMap<>());
      for (Map.Entry<String, Map<String, KvmPreviewAsso>> entry : group.entrySet()) {
        result = result.thenCombine(taskRunner.addAsyncTask(
                        () -> {
                          Map<String, Boolean> res = new HashMap<>();
                          for (String s : txSavePathMap.keySet()) {
                            KvmPreviewAsso asso = entry.getValue().get(s);
                            if (asso != null) {
                              String path =
                                      CaesarRunnerForeignMock.getPreviewUrl(asso);
                              lockAtPath(path);
                              try {
                                File file = new File(path);
                                file.getParentFile().mkdirs();
                                FileWriter fos = new FileWriter(path);
                                BufferedWriter writer = new BufferedWriter(fos);
                                // 写入TX名称与更新时间
                                writer.write(String.format("%s%s%d", s, ":",
                                        System.currentTimeMillis()));
                                writer.close();
                                fos.close();
                                res.put(s, true);
                              } catch (Exception e) {
                                e.printStackTrace();
                                res.put(s, false);
                              } finally {
                                unlockAtPath(path);
                              }
                              String url = asso.getUrl();
                              String savePath = txSavePathMap.get(s);
                              lockAtPath(entry.getValue().get(s).getUrl());
                              lockAtPath(txSavePathMap.get(s));
                              try {
                                File file = new File(savePath);
                                file.getParentFile().mkdirs();

                                FileInputStream fis = new FileInputStream(url);
                                FileOutputStream fos = new FileOutputStream(savePath);
                                fos.getChannel().transferFrom(fis.getChannel(), 0, fis.getChannel().size());
                                fos.close();
                                fis.close();
                              } catch (IOException e) {
                                e.printStackTrace();
                                res.put(s, true);
                              } finally {
                                unlockAtPath(savePath);
                                unlockAtPath(url);
                              }
                            }
                          }
                          return res;
                        }),
                (lhs, rhs) -> {
                  Map<String, Boolean> newResult = new HashMap<>(lhs);
                  newResult.putAll(rhs);
                  return newResult;
                }
        );
      }
      return result;
    }
  }

  static class PreviewMapMock extends PreviewMap {
    /**
     * 轮询预览的通道.
     */
    private Set<KvmPreviewAsso> pollingPreviewChannels = new HashSet<>();

    PreviewMapMock(Map<String, Collection<KvmPreviewAsso>> pollingPreviewChannels) {
      for (Map.Entry<String, Collection<KvmPreviewAsso>> entry : pollingPreviewChannels.entrySet()) {
        this.pollingPreviewChannels.addAll(entry.getValue());
      }
    }

    @Override
    public Collection<KvmPreviewAsso> getPollingPreviewChannels() {
      return new HashSet<>(pollingPreviewChannels);
    }

    @Override
    public void setPollingPreviewChannels(Collection<KvmPreviewAsso> channels) {

    }
  }

  public ObjectIds makeRandomRequest(Map<String, KvmAsset> assets, boolean important, int cnt) {
    ObjectIds request = new ObjectIds();
    request.setImportant(important);
    request.setRequestKey(UUID.randomUUID().toString());

    request.getIds().addAll(
            PreviewTestHelper.getRandomElements(assets.entrySet(), cnt).stream()
                    .map((item) -> item.getValue().getAssetId()).toList());
    return request;
  }

  private PreviewManager mockFourRxPreviewManager(CaesarDbServiceUtil runner,
                                                  SkyLinkTaskPool taskPool) throws IllegalAccessException {
    PreviewManager manager = new PreviewManager();
    FieldUtil.setPropertyValue(manager, "caesarDbServiceUtil", runner);
    FieldUtil.setPropertyValue(manager, "taskPool", taskPool);
    return manager;
  }

  private void changePreviewStrategy(PreviewManager manager,
                                     CaesarDbServiceUtil runner,
                                     PreviewMap previewMap, SkyLinkTaskPool taskPool,
                                     DeviceDataGetter deviceDataGetter) throws IllegalAccessException {
    Map<String, PreviewStrategy> previewStrategyMap = Maps.newHashMap();
    DeviceDataGetter dataGetter = mock(DeviceDataGetter.class);
    FourScreenRxPreviewStrategy previewStrategy = new FourScreenRxPreviewStrategy(dataGetter);  // 创建预览策略
    previewStrategyMap.put(masterId, previewStrategy);
    Map<String, List<PreviewThread>> previewThreadMap = Maps.newHashMap();
    FieldUtil.setPropertyValue(previewStrategy, "currentPreview", previewMap);
    FieldUtil.setPropertyValue(manager, "previewStrategyMap", previewStrategyMap);
    FieldUtil.setPropertyValue(manager, "previewThreadMap", previewThreadMap);
    FourScreenRxPreviewOperatorImplMock operatorImplMock =
            new FourScreenRxPreviewOperatorImplMock(taskPool, deviceDataGetter);
    previewThreadMap.put(masterId, List.of(
            new PollingPreviewRunner(previewStrategy.getPreviewInfoOpt(), deviceDataGetter, operatorImplMock)));
    previewStrategy.resetChannels();
    previewThreadMap.values().forEach(previewThreads -> previewThreads.forEach(taskPool::addTask));
  }
}
