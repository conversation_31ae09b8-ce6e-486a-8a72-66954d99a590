package com.mediacomm.caesar.domain.vp7;

import java.util.Collection;
import lombok.Data;

/**
 * .
 */
@Data
public class Vp7Status {
  private String ip;
  private String model;
  private String version;
  private String communicationStatus; // 通信状态
  private double temperature;
  private double cpuUsage;
  private double memoryUsage;
  private double diskUsage;
  private Collection<Integer> powerStatus; // 0表示正常，1表示未供电，2表示异常
  private Collection<Integer> fanStatus; // 风扇转速，单位为rpm
  private Collection<Integer> linkStatus; // 光纤链路 0表示正常，1表示未链接
  private Collection<Integer> videoLineStatus; // 视频输出端口状态 0表示正常，1表示未接入
  private String bannerLogoHash; // 条幅Logo图片hash值
  private String bgImageHash; // 底图图片hash值
}
