update skylink.device_model_signal t
set  t.signals = '[{"signalId":"link.status","aiControl":{},"normalDesc":"正常","signalName":"通信状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查设备网线","thresholdName":"通信异常"}],"outputEnable":false},{"signalId":"temperature","aiControl":{},"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85.0}],"outputEnable":false,"measurementUnit":"℃"},{"signalId":"cpuRate","aiControl":{},"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0.0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdName":"负荷过高","lowerThreshold":85.0}],"aiUpperLimit":100.0,"outputEnable":false,"measurementUnit":"%"},{"signalId":"memRate","aiControl":{},"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0.0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdName":"过高","lowerThreshold":85.0}],"aiUpperLimit":100.0,"outputEnable":false,"measurementUnit":"%"},{"signalId":"diskRate","aiControl":{},"normalDesc":"正常","signalName":"磁盘使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0.0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdName":"过高","lowerThreshold":85.0}],"aiUpperLimit":100.0,"outputEnable":false,"measurementUnit":"%"},{"signalId":"fanSpeed","aiControl":{},"normalDesc":"正常","signalName":"风扇转速","signalType":"ANALOG","inputEnable":true,"signalGroup":"fan","aiLowerLimit":0.0,"aiUpperLimit":12000.0,"outputEnable":false,"measurementUnit":"rpm"},{"signalId":"powerStatus","aiControl":{},"normalDesc":"正常","signalName":"电源状态","signalType":"STATUS","inputEnable":true,"signalGroup":"power","outputEnable":false,"statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":0,"thresholdName":"电源供电正常"},{"alarmLevel":"LEVEL_2","statusValue":1,"thresholdDesc":"检查电源线","thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_4","statusValue":2,"thresholdDesc":"检查电源","thresholdName":"电源故障"}]},{"signalId":"videoLineStatus","aiControl":{},"normalDesc":"正常","signalName":"视频口接线状态","signalType":"STATUS","inputEnable":true,"signalGroup":"channel","outputEnable":false,"statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":1,"thresholdName":"未接入"},{"alarmLevel":"LEVEL_0","statusValue":0,"thresholdName":"正常"}]},{"signalId":"opticalStatus","aiControl":{},"normalDesc":"正常","signalName":"光口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"power","outputEnable":false,"statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":0,"thresholdName":"已链接"},{"alarmLevel":"LEVEL_4","statusValue":1,"thresholdName":"未链接"}]}]'
where t.device_model_id = 2402;

update skylink.device_model_signal t
set  t.signals = '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查网线","thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源状态","signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"2","thresholdDesc":"检查电源线","thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_4","statusValue":"3","thresholdDesc":"检查电源","thresholdName":"电源故障"}]},{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"diskRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"磁盘使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"度","statusThresholds":[]},{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态","signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_4","statusValue":"0","thresholdDesc":"","thresholdName":"未接线"},{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"","thresholdName":"异常"}]},{"signalId":"loginUser","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"登录用户","signalType":"STRING","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"mouseStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"鼠标线状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"keyboardStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"键盘线状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"udiskStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"U盘状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"touchStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"触屏线状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]}]'
where t.device_model_id = 1101;

update skylink.device_model_signal t
set  t.signals = '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查网线","thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源状态","signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"2","thresholdDesc":"检查电源线","thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_4","statusValue":"3","thresholdDesc":"检查电源","thresholdName":"电源故障"}]},{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"diskRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"磁盘使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"度","statusThresholds":[]},{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态","signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_4","statusValue":"0","thresholdDesc":"","thresholdName":"未接线"},{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"","thresholdName":"异常"}]},{"signalId":"loginUser","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"登录用户","signalType":"STRING","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"mouseStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"鼠标线状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"keyboardStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"键盘线状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"udiskStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"U盘状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"touchStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"触屏线状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]}]'
where t.device_model_id = 1201;

update skylink.device_model_signal t
set  t.signals = '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查网线","thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源状态","signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"2","thresholdDesc":"检查电源线","thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_4","statusValue":"3","thresholdDesc":"检查电源","thresholdName":"电源故障"}]},{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"diskRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"磁盘使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"度","statusThresholds":[]},{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态","signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_4","statusValue":"0","thresholdDesc":"","thresholdName":"未接线"},{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"","thresholdName":"异常"}]},{"signalId":"loginUser","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"登录用户","signalType":"STRING","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"mouseStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"鼠标线状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"keyboardStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"键盘线状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"udiskStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"U盘状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"touchStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"触屏线状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]}]'
where t.device_model_id = 1202;
