package com.mediacomm.protocol;

import com.mediacomm.domain.Operate;
import com.mediacomm.domain.Ptz;
import com.mediacomm.util.InspectValue;

/**
 * 创建对应协议的策略控制类.

 * <AUTHOR>
 */
public class ProtocolContext {
  private Protocol protocol;

  /**
   * 根据消息创建指定协议对象.
   *
   * @param protocol 协议.
   */
  public ProtocolContext(String protocol) {
    switch (protocol) {
      case InspectValue.PELCO_D -> this.protocol = new PelcoD();
      case InspectValue.PELCO_P -> this.protocol = new PelcoP();
      case InspectValue.VISCA -> this.protocol = new Visca();
      case InspectValue.PELCO_HIK -> this.protocol = new PelcoHik();
      default -> {
      }
    }
  }

  /**
   * 调用对应协议的操作指令.

   * @param ptz 云台.
   * @param opt 操作.
   * @return 存在已定义的指令则返回true.
   */
  public Boolean doOperate(Ptz ptz, Operate opt) {
    return protocol.doOperate(ptz, opt);
  }
}
