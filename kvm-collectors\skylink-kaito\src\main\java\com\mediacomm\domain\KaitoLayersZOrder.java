package com.mediacomm.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Collection;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoLayersZOrder {
  private int deviceId;
  private Collection<LayerZOrder> layersZOrder;
  private int screenId;

  @Data
  public static class LayerZOrder {
    private int layerId;
    private int zOrder;
  }
}
