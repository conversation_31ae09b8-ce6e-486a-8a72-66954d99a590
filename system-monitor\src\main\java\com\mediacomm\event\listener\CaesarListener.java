package com.mediacomm.event.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.domain.caesar.CaesarExtender;
import com.mediacomm.domain.caesar.CaesarValue;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.ExtenderVideoLink;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.entity.vo.KvmMasterVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarListener extends EvenListener {
  @Resource
  protected KvmMasterService kvmMasterService;
  @Resource
  protected KvmAssetService assetService;
  @Resource
  RedisUtil redisUtil;

  Map<String, CaesarValue> caesarValueMap = new HashMap<>();

  @Override
  public String getValue(String request) {
    MqRequest req = JsonUtils.decode(request, new TypeReference<>() {
    });
    KvmMasterVo master = kvmMasterService.oneById(req.getMasterId());
    if (master == null) {
      return Result.failureStr("Caesar device no exist.", ResponseCode.EX_FAILURE_400);
    }
    String rk = RedisSignalKey.getDeviceStatusKey(master.getDeviceType().getDeviceType(), master.getMasterId());
    String descRk = RedisSignalKey.getDeviceDecKey(master.getDeviceType().getDeviceType(), master.getMasterId());
    Optional<String> updateTime = redisUtil.hget(descRk, RedisSignalKey.UPDATE_TIME);
    if (updateTime.isPresent()) {
      long time = Long.parseLong(updateTime.get());
      CaesarValue oldValue = caesarValueMap.get(master.getMasterId());
      if (oldValue != null && time == oldValue.getUpdateTime()) {
        return Result.okStr(oldValue);
      }
    }
    Optional<Map<String, String>> deviceSignalValue = redisUtil.hmget(rk);
    Optional<Map<String, String>> deviceDesc = redisUtil.hmget(descRk);
    if (deviceSignalValue.isPresent() && deviceDesc.isPresent()) {
      List<DeviceSignalValue> ds = new ArrayList<>();
      // master signal
      addDeviceSignalValue(deviceSignalValue.get(), ds);
      CaesarValue value = new CaesarValue();
      deviceDesc.get().forEach((k, v) -> {
        switch (k) {
          case RedisSignalKey.TOTAL_RX_NUMBER -> value.setTotalRxNumber(Integer.parseInt(v));
          case RedisSignalKey.TOTAL_TX_NUMBER -> value.setTotalTxNumber(Integer.parseInt(v));
          case RedisSignalKey.ONLINE_TX_NUMBER -> value.setOnlineTxNumber(Integer.parseInt(v));
          case RedisSignalKey.ONLINE_RX_NUMBER -> value.setOnlineRxNumber(Integer.parseInt(v));
          case RedisSignalKey.UPDATE_TIME -> value.setUpdateTime(Long.parseLong(v));
          default -> log.warn("Master description field that was not processed {}", k);
        }
      });
      value.getSignalValues().put(master.getMasterId(), ds);
      // 批量读取assets signal
      Collection<KvmAssetVo> assets = assetService.allByMasterId(req.getMasterId());
      Collection<String> cacheKeys = new ArrayList<>();
      assets.forEach(asset -> {
        cacheKeys.add(RedisSignalKey.getDeviceStatusKey(asset.getDeviceType(), asset.getAssetId()));
        cacheKeys.add(RedisSignalKey.getDeviceDecKey(asset.getDeviceType(), asset.getAssetId()));
      });
      Map<String, Map<String, String>> assetSignalAndDescMap = redisUtil.batchHashGet(cacheKeys);
      for (KvmAssetVo asset : assets) {
        if (asset.getDeviceType().equals(DeviceType.CAESAR_VP7.getDeviceType())) {
          continue;
        }
        String assetRedisKey = RedisSignalKey.getDeviceStatusKey(asset.getDeviceType(),
                asset.getAssetId());
        String assetDescKey = RedisSignalKey.getDeviceDecKey(asset.getDeviceType(),
                asset.getAssetId());
        Map<String, String> assetSignalValue = assetSignalAndDescMap.get(assetRedisKey);
        Map<String, String> assetDesc = assetSignalAndDescMap.get(assetDescKey);
        CaesarExtender extender = new CaesarExtender();
        extender.setId(asset.getAssetId());
        List<DeviceSignalValue> assetSignalValueList = new ArrayList<>();
        addDeviceSignalValue(assetSignalValue, assetSignalValueList);
        assetDesc.forEach((k, v) -> {
          switch (k) {
            case RedisSignalKey.LINK_STATUS_1 -> extender.setPort1(Integer.parseInt(v));
            case RedisSignalKey.LINK_STATUS_2 -> extender.setPort2(Integer.parseInt(v));
            case RedisSignalKey.CONNECTION_STATUS -> {
              List<ExtenderVideoLink> links = JsonUtils.decodeList(v, ExtenderVideoLink.class);
              extender.setOtherSideAssets(links);
            }
            default -> log.warn("Asset description field that was not processed {}", k);
          }
        });
        value.getSignalValues().put(asset.getAssetId(), assetSignalValueList);
        value.getExtenderMap().put(asset.getAssetId(), extender);
      }
      caesarValueMap.put(master.getMasterId(), value);
      return Result.okStr(value);
    }
    return Result.failureStr("Data not ready.", ResponseCode.EX_NOTFOUND_404);
  }

  @Override
  public DeviceType getDeviceType() {
    return DeviceType.CAESAR;
  }
}
