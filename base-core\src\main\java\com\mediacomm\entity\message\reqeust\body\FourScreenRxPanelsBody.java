package com.mediacomm.entity.message.reqeust.body;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mediacomm.entity.message.LayoutData;
import java.util.Collection;
import lombok.Data;

/**
 * 云视对接前端凯撒rx四画面窗口结构.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FourScreenRxPanelsBody {
  private int decoderId;
  private LayoutData layout;
  private Collection<FourScreenRxPanelBody> panels;
}
