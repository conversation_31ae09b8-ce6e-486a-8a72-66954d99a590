package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.AssetGroupType;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "kvm_asset_group", description = "外设分组")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KvmAssetGroup extends SkyLinkDbEntity {
  @TableId(value = "asset_group_id", type = IdType.AUTO)
  @Hidden
  private Integer assetGroupId;
  private String name;
  private Integer roomId;
  private AssetGroupType groupType;
  private String backgroundColor; // 背景颜色
  private String typefaceColor; // 字体颜色
  private Integer parentGroupId = 0;
  private Integer seq = 0;
}
