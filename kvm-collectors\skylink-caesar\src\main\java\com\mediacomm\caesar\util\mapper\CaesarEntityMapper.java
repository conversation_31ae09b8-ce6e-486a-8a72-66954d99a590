package com.mediacomm.caesar.util.mapper;

import com.mediacomm.caesar.domain.CaesarKvmDecoder;
import com.mediacomm.caesar.domain.CaesarPanelRect;
import com.mediacomm.caesar.domain.CaesarRx;
import com.mediacomm.caesar.domain.CaesarSeat;
import com.mediacomm.caesar.domain.CaesarSeatPanelRect;
import com.mediacomm.caesar.domain.CaesarServer;
import com.mediacomm.caesar.domain.CaesarTx;
import com.mediacomm.caesar.domain.CaesarUser;
import com.mediacomm.caesar.domain.CaesarVideoPanels;
import com.mediacomm.caesar.domain.CaesarVideoWall;
import com.mediacomm.caesar.domain.Slot;
import com.mediacomm.caesar.domain.kaito.KaitoGroupData;
import com.mediacomm.caesar.domain.kaito.KaitoInputData;
import com.mediacomm.caesar.domain.kaito.KaitoIpcData;
import com.mediacomm.caesar.domain.kaito.KaitoVideoWall;
import com.mediacomm.caesar.util.mapper.annotations.ToCaesarVideoSrcId;
import com.mediacomm.caesar.util.mapper.annotations.ToSkylinkVideoSrcId;
import com.mediacomm.caesar.util.mapper.annotations.ToSkylinkVideoSrcName;
import com.mediacomm.caesar.util.mapper.jpa.KvmAssetContext;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.KvmSeatDecoder;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.dao.KvmUser;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.FourScreenRxPanel;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.message.VideoPanels;
import com.mediacomm.entity.message.reqeust.body.FourScreenRxPanelBody;
import java.util.List;
import org.mapstruct.BeanMapping;
import org.mapstruct.Builder;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.control.DeepClone;

/**
 * CaesarEntityMapper.
 */
@Mapper(componentModel = "spring", uses = CaesarEntityMapperResolver.class,
    builder = @Builder(disableBuilder = true),
    mappingControl = DeepClone.class)
public interface CaesarEntityMapper {

  CaesarVideoPanels fromVideoPanels(VideoPanels videoPanels, @Context KvmAssetContext context);

  VideoPanels toVideoPanels(CaesarVideoPanels caesarVideoPanels, @Context String masterId);

  @Mapping(target = "status", ignore = true)
  PanelRect toPanelRect(KaitoVideoWall.Layer kaitoLayer, @Context KvmVideoWall videoWall);

  @Mapping(target = "status", ignore = true)
  KaitoVideoWall.Layer fromPanelRect(PanelRect panelRect);

  @Mapping(source = "videoSrcId", target = "videoSrcId", qualifiedBy = ToCaesarVideoSrcId.class)
  CaesarPanelRect fromPanelRect(PanelRect panelRect, @Context KvmAssetContext context);

  PanelRect toPanelRect(CaesarPanelRect caesarPanelRect, @Context String masterId);

  @Mapping(source = "videoSrcId", target = "videoSrcId", qualifiedBy = ToCaesarVideoSrcId.class)
  CaesarSeatPanelRect fromFourScreenRxPanel(FourScreenRxPanelBody rxPanel,
                                            @Context KvmAssetContext context);

  @Mapping(source = "videoSrcId", target = "videoSrcId", qualifiedBy = ToCaesarVideoSrcId.class)
  CaesarSeatPanelRect toSeatPanelRect(PanelRect panelRect, @Context KvmAssetContext context);

  @Mapping(source = "videoSrcId", target = "videoSrcId", qualifiedBy = ToSkylinkVideoSrcId.class)
  @Mapping(source = "videoSrcId", target = "videoSrcName",
      qualifiedBy = ToSkylinkVideoSrcName.class)
  FourScreenRxPanel toFourScreenRxPanel(CaesarSeatPanelRect rxPanel,
                                        @Context KvmAssetContext context);

  @Mapping(target = "assetId", ignore = true)
  @Mapping(source = "id", target = "deviceId")
  @Mapping(target = "version", ignore = true)
  @Mapping(source = "name", target = "alias")
  @Mapping(target = "properties", ignore = true)
  @Mapping(target = "deviceModel", ignore = true)
  @Mapping(target = "deviceIp", ignore = true)
  @Mapping(target = "masterId", ignore = true)
  KvmAsset toKvmAsset(CaesarTx caesarTx, @Context String masterId);

  @Mapping(target = "assetId", ignore = true)
  @Mapping(source = "id", target = "deviceId")
  @Mapping(target = "version", ignore = true)
  @Mapping(source = "name", target = "alias")
  @Mapping(target = "properties", ignore = true)
  @Mapping(target = "deviceModel", ignore = true)
  @Mapping(target = "deviceIp", ignore = true)
  @Mapping(target = "masterId", ignore = true)
  KvmAsset toKvmAsset(CaesarRx caesarRx, @Context String masterId);

  @Mapping(source = "name", target = "alias")
  @Mapping(source = "inputId", target = "deviceId")
  KvmAsset toKvmAsset(KaitoInputData input, @Context String masterId, @Context Integer groupId);

  @Mapping(source = "name", target = "alias")
  @Mapping(source = "sourceId", target = "deviceId")
  KvmAsset toKvmAsset(KaitoIpcData ipc, @Context String masterId, @Context Integer groupId);

  @Mapping(target = "slotId", ignore = true)
  @Mapping(source = "slotNumber", target = "deviceId")
  @Mapping(source = "fpgaVersion", target = "version")
  @Mapping(target = "name", ignore = true)
  @Mapping(target = "collectorProperties", ignore = true)
  @Mapping(target = "deviceModel", ignore = true)
  @Mapping(target = "properties", ignore = true)
  @Mapping(target = "masterId", ignore = true)
  KvmSlot toKvmSlot(Slot slot, @Context String masterId);

  List<KvmVideoWall> toKvmVideoWallList(List<CaesarVideoWall> caesarVideoWalls,
                                        @Context String masterId);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "id", target = "deviceId")
  @Mapping(source = "name", target = "name")
  @Mapping(source = "row", target = "rowCount")
  @Mapping(source = "col", target = "colCount")
  @Mapping(source = "devices", target = "decoders")
  @Mapping(source = "totalWidth", target = "singleW")
  @Mapping(source = "totalHeight", target = "singleH")
  KvmVideoWall toKvmVideoWall(CaesarVideoWall caesarVideoWall, @Context String masterId);

  @Mapping(source = "id", target = "deviceId")
  @Mapping(source = "name", target = "name")
  KvmVideoWall toKvmVideoWall(KaitoVideoWall kaitoVideoWall, @Context KaitoGroupData groupData,
                              @Context String masterId);

  List<KvmSeat> toKvmSeatList(List<CaesarSeat> caesarSeatList, @Context String masterId);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "id", target = "deviceId")
  @Mapping(source = "name", target = "name")
  @Mapping(source = "row", target = "rowCount")
  @Mapping(source = "col", target = "colCount")
  @Mapping(source = "kvmDecoders", target = "decoders")
  KvmSeat toKvmSeat(CaesarSeat caesarSeat, @Context String masterId);

  @Mapping(source = "channel", target = "channelId")
  @Mapping(source = "deviceId", target = "deviceId")
  @Mapping(target = "seatId", ignore = true)
  KvmSeatDecoder toKvmSeatDecoder(CaesarKvmDecoder caesarKvmDecoder);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "fpgaVersion", target = "version")
  @Mapping(source = "name", target = "alias")
  KvmMaster toKvmMaster(CaesarServer caesarServer);

  List<KvmUser> toKvmUserList(List<CaesarUser> caesarUserList, @Context String masterId);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "index", target = "indexInDevice")
  @Mapping(source = "name", target = "userName")
  @Mapping(source = "level", target = "userLevel")
  KvmUser toKvmUser(CaesarUser caesarUser, @Context String masterId);
}
