package com.mediacomm.caesar.domain.vp7;

import java.util.Collection;
import com.google.j2objc.annotations.Property;
import lombok.Data;

@Data
public class R2P4FResponse {
  private int code; // 0表示成功，-1表示失败
  private String message;
  private long timestamp; // 请求开窗与下载图片对应为一致的时间戳
  private int channel; // 可用通道数
  private Collection<TxStatus> result;

  @Data
  public static class TxStatus {
    @Property("txid")
    private int txid;
    private int status; // 1-成功，2-无信号，3-读数据超时，4-写数据超时，5-资源受限，-1-其他错误
  }

}
