package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mediacomm.entity.dao.PictureMaterial;
import java.util.Collection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface PictureMaterialMapper extends BaseMapper<PictureMaterial> {
  @Select("select image_id from picture_material where image_id = #{imageId}")
  Collection<Long> findAllImageId(Long imageId);
}
