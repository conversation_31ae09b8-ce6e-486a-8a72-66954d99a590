package com.mediacomm.caesar.domain;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * CaesarRx.
 */
@Data
public class CaesarRx implements CaesarDeviceSameField {
  private int id;
  private String name;
  private String sn;
  private String deviceType;
  private String deviceModel;
  private String softVersion;
  private String systemVersion;
  private String fpgaVersion;
  private int rxType; // 0 -> rx, 1 -> vp7, 2 -> vp6
  private boolean redundant;
  private int videoNumber;
  private int videoResolutionType;
  private int videoIntfType;
  private int link1Port;
  private int link2Port;
  private List<CaesarProperty> properties = new ArrayList<>();
}
