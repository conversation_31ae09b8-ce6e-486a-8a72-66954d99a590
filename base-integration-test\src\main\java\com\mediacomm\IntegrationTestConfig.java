package com.mediacomm;

import org.mockito.Mockito;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.TimeSeriesOperations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
@ComponentScan(
    basePackages = "com.mediacomm",
    excludeFilters = @ComponentScan.Filter(
        type = FilterType.ASSIGNABLE_TYPE,
        classes = com.mediacomm.config.thread.ThreadPool.class
    )
)
@EnableAutoConfiguration
public class IntegrationTestConfig {
  // 开启对 com.mediacomm 包下组件的扫描与自动配置，使测试上下文中可以找到 service/dao 等 bean

  @Bean
  @Primary
  public TimeSeriesOperations<String, String> timeSeriesOperations() {
    // 返回一个 Mockito mock，避免在测试中需要真实的 RedisTimeSeries 实现
    return Mockito.mock(TimeSeriesOperations.class);
  }

  // 提供一个简单的线程池 Bean 以满足依赖，避免原始 ThreadPool.createThreadPool 被调用
  @Bean
  @Primary
  public ThreadPoolTaskExecutor createThreadPool() {
    ThreadPoolTaskExecutor executor =
        new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(2);
    executor.setMaxPoolSize(4);
    executor.setQueueCapacity(100);
    executor.initialize();
    return executor;
  }
}
