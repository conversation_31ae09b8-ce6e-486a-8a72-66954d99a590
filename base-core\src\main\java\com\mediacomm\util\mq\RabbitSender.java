package com.mediacomm.util.mq;

import com.mediacomm.config.rabbitmq.TopicRabbitConfig;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Rabbit发送端.
 */
@Component
@Slf4j
public class RabbitSender {

  private RabbitTemplate rabbitTemplate;

  public RabbitSender(@Autowired RabbitTemplate rabbitTemplate) {
    rabbitTemplate.setReplyTimeout(15000);
    this.rabbitTemplate = rabbitTemplate;
  }
  /**
   * 异步发送消息.
   *
   * @param topic 主题.
   * @param msg 消息内容.
   */
  public <T> void asyncSend(String topic, MqRequest<T> msg) {
    rabbitTemplate.convertAndSend(TopicRabbitConfig.TOPIC_EXCHANGE, topic, JsonUtils.encode(msg));
  }

  /**
   * 同步发送消息.
   *
   * @param topic 主题.
   * @param msg 消息内容.
   * @return .
   */
  public <T> Object syncSend(String topic, MqRequest<T> msg) {
    return rabbitTemplate.convertSendAndReceive(TopicRabbitConfig.TOPIC_EXCHANGE, topic,
        JsonUtils.encode(msg));
  }
}
