package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.ListVersionJsonTypeHandler;
import com.mediacomm.config.db.ListPropertyJsonTypeHandler;
import com.mediacomm.entity.Property;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备.
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "kvm_asset", description = "kvm外围设备对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KvmAsset extends SkyLinkDbEntity {
  @Schema(hidden = true)
  @TableId(value = "asset_id", type = IdType.ASSIGN_UUID)
  private String assetId; // 设备Id
  private Integer deviceId; // 设备的实际Id
  private String alias; // 设备别名
  private String name; // 设备名称
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> collectorProperties = new ArrayList<>(); // 采集扩展属性，云视上型号中扩展的可编辑属性
  private String deviceIp; // 设备Ip
  private Integer deviceModel; // 设备类型Id
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> properties = new ArrayList<>(); // 设备扩展属性，从设备接口中获取回来的属性
  @TableField(typeHandler = ListVersionJsonTypeHandler.class)
  private List<Version> version; // 设备版本
  private String hardcode; // 设备硬件编码
  private String masterId; // 设备的主机Id

  public void resetVersion() {
    this.version = new ArrayList<>();
  }
}
