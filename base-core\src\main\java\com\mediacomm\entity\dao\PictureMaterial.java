package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.FileType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
public class PictureMaterial extends SkyLinkDbEntity {
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  private String name;
  private Integer parentId;
  // 解决long类型精度丢失问题
  @JsonSerialize(using = ToStringSerializer.class)
  private Long imageId;
  private FileType type;
}
