package com.mediacomm.system.variable.sysenum;

/**
 * .
 */
public enum SubSystemType {
  /**
   * 系统自诊断.
   */
  SELF_DIAGNOSIS,
  /**
   * kvm设备.
   */
  KVM,
  /**
   * 中控.
   */
  CENTER_CONTROL,
  /**
   * 交换机.
   */
  SWITCH_DEVICE,
  /**
   * 监控终端.
   */
  BUSINESS_TERMINAL,
  /**
   * 可视化终端.
   */
  VISUALIZATION_TERMINAL,
  /**
   * 外围设备-Rx.
   */
  PER_EQUIPMENT_RX,
  /**
   * 外围设备-其他.
   */
  PER_EQUIPMENT_OTHER,
  /**
   * 外围设备-Tx.
   */
  PER_EQUIPMENT_TX,
  /**
   * 板卡.
   */
  INTERNAL_BOARD,
  /**
   * 视频墙.
   */
  VIDEO_WALL;

  /**
   * 系统告警等级.
   */
  public enum AlarmLevel {
    /**
     * 告警等级4.
     */
    LEVEL_4,
    /**
     * 告警等级3.
     */
    LEVEL_3,
    /**
     * 告警等级2.
     */
    LEVEL_2,
    /**
     * 告警等级1.
     */
    LEVEL_1,
    /**
     * 未发生告警0.
     */
    LEVEL_0;
  }
}
