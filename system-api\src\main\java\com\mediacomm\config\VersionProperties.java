package com.mediacomm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * .
 */
@Configuration
@ConfigurationProperties(prefix = "system")
@PropertySource("classpath:META-INF/VERSION.properties")
@EnableConfigurationProperties(VersionProperties.class)
@Data
public class VersionProperties {
  private String appVersion;
  private String apiVersion;
  private String dbVersion;
  private String redisVersion;
}
