package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.KvmUser;
import com.mediacomm.entity.vo.KvmUserVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.KvmUserMapper;
import java.util.Collection;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class KvmUserService extends SkyLinkServiceImpl<KvmUserMapper, KvmUser> {

  /**
   * 查询所有或通过主机Id筛选.
   *
   * @param masterId 主机Id.
   * @return .
   */
  public Collection<KvmUserVo> all(String masterId) {
    return masterId == null ? baseMapper.findAll() : baseMapper.findAllByMasterId(masterId);
  }

  /**
   * save or update kvmUsers.
   */
  public void saveOrUpdate(List<KvmUser> kvmUsers, String masterId) {
    for (KvmUser kvmUser : kvmUsers) {
      LambdaQueryWrapper<KvmUser> lambdaQueryWrapper = Wrappers.lambdaQuery(KvmUser.class)
          .eq(KvmUser::getIndexInDevice, kvmUser.getIndexInDevice())
          .and(wrapper -> wrapper.eq(KvmUser::getMasterId, masterId));
      KvmUser old = getOne(lambdaQueryWrapper);
      if (old != null) {
        kvmUser.setUserId(old.getUserId());
        updateById(kvmUser);
      } else {
        save(kvmUser);
      }
    }
  }
}
