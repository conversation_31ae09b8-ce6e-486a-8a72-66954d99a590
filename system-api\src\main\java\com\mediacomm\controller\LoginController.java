package com.mediacomm.controller;

import com.google.common.base.Strings;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Account;
import com.mediacomm.entity.vo.AccountVo;
import com.mediacomm.pojo.LoginDto;
import com.mediacomm.pojo.PayloadDto;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.AccountService;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.util.JwtUtils;
import com.mediacomm.util.RedisUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "登录管理")
@RestController
public class LoginController extends SkyLinkController<Account, AccountService> {

  @Autowired
  RedisUtil redisUtil;

  /**
   * .
   */
  @Operation(summary = "登录系统，JWT的主体内容部分时间字段单位为秒")
  @PostMapping(ResUrlDef.LOGIN)
  public Result<?> login(@RequestBody @Validated LoginDto loginDto) {
    AccountVo account = service.oneByName(loginDto.getUsername());
    if (account == null) {
      return Result.failure("Account is not exist!", ResponseCode.EX_FAILURE_400);
    } else if (!account.isAccountEnable()) {
      return Result.failure("Account is disabled!", ResponseCode.EX_FAILURE_400);
    } else if (!account.isPersonnelEnable()) {
      return Result.failure("Personnel is disabled!", ResponseCode.EX_FAILURE_400);
    }
    if (loginDto.getPassword().equals(account.getAccountPassword())) {
      PayloadDto payloadDto = JwtUtils.getDefaultPayloadDto(account);
      String token = JwtUtils.generateKey(payloadDto);
      if (!Strings.isNullOrEmpty(token)) {
        temporarilySaveToken(payloadDto, token);
        return Result.ok("success", token);
      }
    }
    return Result.failure("Error account info!", ResponseCode.EX_FAILURE_400);
  }

  /**
   * .
   */
  @GetMapping(ResUrlDef.TOKEN_REFRESH)
  public Result<?> tokenRefresh(HttpServletRequest request) {
    String token =  JwtUtils.parseJwt(request);
    if (!Strings.isNullOrEmpty(token)) {
      PayloadDto payloadDto = JwtUtils.verifyTokenByHmac(token);
      if (payloadDto != null) {
        payloadDto.setExp(System.currentTimeMillis() / 1000 + JwtUtils.EXPIRATION);
        String newToken = JwtUtils.generateKey(payloadDto);
        temporarilySaveToken(payloadDto, newToken);
        return Result.ok("success", newToken);
      }
    }
    return Result.failure("Error token!", ResponseCode.EX_UNAUTHORIZED_401);
  }

  /**
   * .
   */
  @GetMapping(ResUrlDef.LOGOUT)
  public Result<?> logout(HttpServletRequest request) {
    String token =  JwtUtils.parseJwt(request);
    if (!Strings.isNullOrEmpty(token)) {
      PayloadDto payloadDto = JwtUtils.verifyTokenByHmac(token);
      if (payloadDto != null) {
        redisUtil.hdel(RedisKey.SA_TK_ID, payloadDto.getJti());
        redisUtil.hdel(RedisKey.SA_TK_ET, payloadDto.getJti());
      }
    }
    return Result.ok();
  }

  private void temporarilySaveToken(PayloadDto payloadDto, String token) {
    redisUtil.hset(RedisKey.SA_TK_ID, payloadDto.getJti(),
        token.substring(JwtUtils.TOKEN_PREFIX.length()).trim());
    redisUtil.hset(RedisKey.SA_TK_ET, payloadDto.getJti(), String.valueOf(payloadDto.getExp()));
  }


}
