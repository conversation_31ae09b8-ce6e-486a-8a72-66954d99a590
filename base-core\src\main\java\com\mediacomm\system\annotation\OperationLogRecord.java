package com.mediacomm.system.annotation;

import com.mediacomm.system.variable.sysenum.OperateType;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作记录.
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLogRecord {
  /**
   * 操作标题.
   *
   * @return 标题.
   */
  String title() default "";

  /**
   * 操作类型.
   *
   * @return OperateType.
   */
  OperateType operateType() default OperateType.OTHER;

  /**
   * 请求消息体.
   *
   * @return Json数组字符串.
   */
  String requestBody() default "[]";
}
