package com.mediacomm.switcher;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.config.rabbitmqtt.IMqttSender;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.Signal;
import com.mediacomm.entity.dao.SignalValue;
import com.mediacomm.entity.vo.EnvDeviceVo;
import com.mediacomm.switcher.config.SwitcherMqttCallbackImpl;
import com.mediacomm.switcher.domain.SwitcherMqttResponse;
import com.mediacomm.switcher.domain.SwitcherNotify;
import com.mediacomm.switcher.domain.SwitcherRpcConstants;
import com.mediacomm.switcher.domain.SwitcherRpcRequestBody;
import com.mediacomm.switcher.domain.body.SwitcherRegisterStatusBody;
import com.mediacomm.switcher.domain.body.SwitcherRegisterStatusResponseBody;
import com.mediacomm.switcher.domain.body.SwitcherStatusBody;
import com.mediacomm.system.base.kvm.SignalCollector;
import com.mediacomm.system.service.EnvDeviceService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Slf4j
@Component
public class KvmSwitcherCollector extends SignalCollector {
  @Resource
  private EnvDeviceService envDeviceService;
  @Resource
  SwitcherMqttCallbackImpl mqttCallback;
  @Resource
  private IMqttSender iMqttSender;
  @Resource
  private RedisUtil redisUtil;

  @PostConstruct
  public void init() {
    mqttCallback.subscribe(SwitcherRpcConstants.extNotify(SwitcherRpcConstants.STATUS));
    mqttCallback.subscribe(
        SwitcherRpcConstants.extServerRequest(SwitcherRpcConstants.GET_REGISTER_STATUS));
  }

  @Scheduled(cron = "*/10 * * * * ?")
  @SchedulerLock(name = "switcherStatus", lockAtLeastFor = "PT4S", lockAtMostFor = "PT4S")
  public void publishSwitcherStatusReq() {
    Collection<EnvDeviceVo> txes = envDeviceService
        .allByDeviceType(DeviceType.SWITCHER_TX.getDeviceType());
    Collection<EnvDeviceVo> rxes = envDeviceService
        .allByDeviceType(DeviceType.SWITCHER_RX.getDeviceType());
    Collection<EnvDeviceVo> deviceVos =
        Stream.of(txes, rxes).flatMap(Collection::stream).toList();
    for (EnvDeviceVo deviceVo : deviceVos) {
      checkLinkStatus(deviceVo);
    }
  }

  /**
   * 通过mqttInputChannel通道获取数据.
   */
  @ServiceActivator(inputChannel = "mqttInputChannel")
  public void handleTopicMessage(Message<?> message) {
    Object receivedTopic = message.getHeaders().get("mqtt_receivedTopic");
    if (receivedTopic != null) {
      String topic = receivedTopic.toString();
      String method = topic.substring(topic.lastIndexOf("/") + 1);
      String payload = new String((byte[]) message.getPayload(), StandardCharsets.UTF_8);
      log.debug("Switcher get notify message.topic:{} . payload:{}", topic, payload);
      SwitcherStatusBody body = null;
      EnvDeviceVo switcher = null;
      switch (method) {
        case SwitcherRpcConstants.STATUS -> {
          SwitcherNotify<SwitcherStatusBody> notify = JsonUtils.decode(payload,
              new TypeReference<>() {
              });
          if (notify != null && notify.getBody() != null) {
            body = notify.getBody();
            switcher = envDeviceService.oneById(notify.getSource());
          }
        }
        case SwitcherRpcConstants.GET_REGISTER_STATUS -> {
          SwitcherRpcRequestBody<SwitcherRegisterStatusBody> notify = JsonUtils.decode(payload,
              new TypeReference<>() {
              });
          if (notify != null && notify.getParams() != null) {
            SwitcherRegisterStatusBody registerStatusBody = notify.getParams();
            EnvDeviceVo switcherSnMac = envDeviceService.oneBySnAndMac(registerStatusBody.getSn(),
                registerStatusBody.getMac());
            SwitcherMqttResponse<SwitcherRegisterStatusResponseBody> reqMsg =
                new SwitcherMqttResponse<>();
            reqMsg.setMsgId(notify.getMsgId());
            if (switcherSnMac != null) {
              reqMsg.setResult(0);
              reqMsg.setReason("Success");
              SwitcherRegisterStatusResponseBody resBody = new SwitcherRegisterStatusResponseBody();
              resBody.setId(switcherSnMac.getId());
              String status =
                  Property.findValueByKey(switcherSnMac.getProperties(), "register_ack", "false");
              resBody.setStatus(Boolean.parseBoolean(status));
              reqMsg.setResponse(resBody);
            } else {
              reqMsg.setResult(1);
              reqMsg.setReason("Unregister");
              SwitcherRegisterStatusResponseBody resBody = new SwitcherRegisterStatusResponseBody();
              resBody.setId("");
              resBody.setStatus(false);
              reqMsg.setResponse(resBody);
            }
            iMqttSender.sendToMqtt(JsonUtils.encode(reqMsg).getBytes(StandardCharsets.UTF_8),
                SwitcherRpcConstants.extRpcResponse(notify.getSource(),
                    SwitcherRpcConstants.GET_REGISTER_STATUS));
          }

        }
        default -> log.warn("Unprocessed message topic {}", topic);
      }
      if (body != null && switcher != null) {
        if (!switcher.getName().equals(body.getName())) {
          log.warn("Ip addresses of two devices on the network may conflict."
                  + "Db device {} ip is {}."
                  + "Notify device {} ip is {}.",
              switcher.getName(), switcher.getDeviceIp(),
              body.getName(), body.getIp());
        }
        doStatusMessage(body, switcher);
      } else if (body == null) {
        log.warn("Error status format {}", payload);
      } else {
        log.warn("Error switch not found {}", payload);
      }
    }

  }

  private void doStatusMessage(SwitcherStatusBody statusBody, EnvDeviceVo switcher) {
    if (!statusBody.getVersion().equals(switcher.getVersion())) {
      switcher.setVersion(statusBody.getVersion());
      envDeviceService.updateById(switcher);
    }
    // 切换器数量可达到1000个，为减少网络带宽，
    // 所以这里仅对单个切换器的信号值进行批量缓存，map只含有一个切换器的所有信号量
    Map<String, Map<String, String>> deviceSignalValueInCache = new HashMap<>();
    Map<String, String> signalValueMap = new HashMap<>();
    Map<String, String> descMap = new HashMap<>();
    String redisKey = RedisSignalKey.getDeviceStatusKey(switcher.getDeviceType(), switcher.getId());
    String redisDescKey = RedisSignalKey.getDeviceDecKey(switcher.getDeviceType(),
        switcher.getId());
    deviceSignalValueInCache.put(redisKey, signalValueMap);
    deviceSignalValueInCache.put(redisDescKey, descMap);
    // link
    Signal linkStausSignal = getSignal(switcher.getDeviceModel(), RedisSignalKey.LINK_STATUS);
    DeviceSignalValue linkSignalValue = setValue(linkStausSignal,
        new SignalValue(Boolean.FALSE), switcher);
    signalValueMap.put(RedisSignalKey.LINK_STATUS, JsonUtils.encode(linkSignalValue));
    // cpu
    Signal cpuStausSignal = getSignal(switcher.getDeviceModel(), RedisSignalKey.CPU_RATE);
    DeviceSignalValue cpuSignalValue = setValue(cpuStausSignal,
        new SignalValue(statusBody.getCpu_usage()), switcher);
    signalValueMap.put(RedisSignalKey.CPU_RATE, JsonUtils.encode(cpuSignalValue));
    // men
    Signal menStausSignal = getSignal(switcher.getDeviceModel(), RedisSignalKey.MEM_RATE);
    DeviceSignalValue menSignalValue = setValue(menStausSignal,
        new SignalValue(statusBody.getMemory_usage()), switcher);
    signalValueMap.put(RedisSignalKey.MEM_RATE, JsonUtils.encode(menSignalValue));
    // temp
    Signal tempStausSignal = getSignal(switcher.getDeviceModel(), RedisSignalKey.TEMPERATURE);
    DeviceSignalValue tempSignalValue = setValue(tempStausSignal,
        new SignalValue(statusBody.getTemperature()), switcher);
    signalValueMap.put(RedisSignalKey.TEMPERATURE, JsonUtils.encode(tempSignalValue));
    // 网口接线状态lan
    processSignalGroup(statusBody.getLan(), "netStatus", "netStatus",
            switcher, signalValueMap, PropertyKeyConst.LAN_NUMBER);
    // fanSpeed
    processSignalGroup(statusBody.getFan(), "fanSpeed", "fanSpeed",
            switcher, signalValueMap, PropertyKeyConst.FAN_NUMBER);
    // power
    processSignalGroup(statusBody.getAc_power(), "acPowerStatus", "acPowerStatus",
            switcher, signalValueMap, "totalAcPowerNumber");
    processSignalGroup(statusBody.getDc_power(), "dcPowerStatus", "dcPowerStatus",
            switcher, signalValueMap, "totalDcPowerNumber");
    // optical Status
    processSignalGroup(statusBody.getLink(), "opticalStatus", "opticalStatus",
            switcher, signalValueMap, PropertyKeyConst.LINK_NUMBER);
    // videoLineStatus
    processSignalGroup(statusBody.getDp(), "videoLineStatus", "videoLineStatus",
            switcher, signalValueMap, PropertyKeyConst.VIDEO_DP_NUMBER);
    // usbStatus
    processSignalGroup(statusBody.getUsb(), "usbStatus", "usbStatus",
            switcher, signalValueMap, PropertyKeyConst.USB_NUMBER);
    if (statusBody.getResolution() != null) {
      String resolution = String.join(",", statusBody.getResolution());
      Signal resolutionSignal = getSignal(switcher.getDeviceModel(), RedisSignalKey.RESOLUTION);
      DeviceSignalValue resolutionSignalValue = setValue(resolutionSignal,
              new SignalValue(resolution), switcher);
      signalValueMap.put(RedisSignalKey.RESOLUTION, JsonUtils.encode(resolutionSignalValue));
    }
    descMap.put(RedisSignalKey.UPDATE_TIME, String.valueOf(System.currentTimeMillis()));
    redisUtil.batchHashSet(deviceSignalValueInCache);
    checkDeviceSignalValue(DeviceType.valueOf(switcher.getDeviceType()).getSubSystem(),
        switcher.getId(), redisKey);
  }

  /**
   * 将超时未收到切换器通知的状态置为通信异常.
   */
  private void checkLinkStatus(EnvDeviceVo switcher) {
    String redisKey = RedisSignalKey.getDeviceStatusKey(switcher.getDeviceType(), switcher.getId());
    String redisDescKey = RedisSignalKey.getDeviceDecKey(switcher.getDeviceType(),
        switcher.getId());
    Optional<String> linkStatus = redisUtil.hget(redisKey, RedisSignalKey.LINK_STATUS);
    // 已经是通信异常的数值了，直接返回
    if (linkStatus.isPresent() && Boolean.parseBoolean(linkStatus.get())) {
      return;
    }
    Optional<String> updateTime = redisUtil.hget(redisDescKey, RedisSignalKey.UPDATE_TIME);
    if (updateTime.isPresent()) {
      long updateTimeLong = Long.parseLong(updateTime.get());
      long nowTime = System.currentTimeMillis();
      if (nowTime - updateTimeLong > 15000) {
        log.warn("The switcher {} notification message timed out.", switcher.getDeviceIp());
        Signal linkStausSignal = getSignal(switcher.getDeviceModel(), RedisSignalKey.LINK_STATUS);
        DeviceSignalValue linkSignalValue = setValue(linkStausSignal,
            new SignalValue(Boolean.TRUE), switcher);
        redisUtil.hset(redisKey, RedisSignalKey.LINK_STATUS, JsonUtils.encode(linkSignalValue));
        redisUtil.hset(redisDescKey, RedisSignalKey.UPDATE_TIME, String.valueOf(nowTime));
        checkDeviceSignalValue(DeviceType.valueOf(switcher.getDeviceType()).getSubSystem(),
            switcher.getId(), redisKey);
      }
    }
  }

  /**
   * 添加计数属性.
   */
  private boolean addNumProperty(EnvDeviceVo switcher, String key, int totalNum) {
    Integer num = Property.findValueByKey(switcher.getProperties(), key,
        null, Integer.class);
    if (num == null && totalNum > 0) {
      num = totalNum;
      switcher.getProperties().add(new Property(key, String.valueOf(num)));
      return true;
    }
    return false;
  }

  private <T> void processSignalGroup(List<T> values, String newSignalId, String signalIdFromModel,
                                  EnvDeviceVo switcher, Map<String, String> signalValueMap,
                                  String updateNumKey) {
    if (values == null || values.isEmpty()) {
      return;
    }
    int count = 1;
    for (T i : values) {
      String signalId = newSignalId + "." + count;
      Signal statusSignal = getSignal(switcher.getDeviceModel(), signalIdFromModel,
              signalId);
      if (statusSignal != null) {
        statusSignal.setSignalName(statusSignal.getSignalName() + "." + count);
        try {
          DeviceSignalValue statusSignalValue =
                  setValue(statusSignal, new SignalValue(i), switcher);
          signalValueMap.put(signalId, JsonUtils.encode(statusSignalValue));
        } catch (Exception e) {
          log.error("processSignalGroup error", e);
          return;
        }
      }
      count++;
    }
    if (StringUtils.isNotBlank(updateNumKey) && addNumProperty(switcher, updateNumKey, values.size())) {
      envDeviceService.updateById(switcher);
    }
  }
}
