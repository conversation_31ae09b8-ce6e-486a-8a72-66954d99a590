version: "3.8"

services:
  skylink-server-app:
    image: *************/mediacomm/skylink-server:V6.0.0.beta-17
    network_mode: host
    container_name: skylink-application
    restart: always
    environment:
      - "REDIS_SERVICE_HOST=localhost"
      - "REDIS_HOST_PORT=6379"
      - "DATABASE_HOST=localhost"
      - "DATABASE_USER=root"
      - "DATABASE_PASSWORD=123456"
      - "DATABASE_NAME=skylink"
      - "DATABASE_DRIVER_CLASS=com.mysql.cj.jdbc.Driver"
      - "DATABASE_DRIVER_NAME=mysql"
      - "DATABASE_PORT=3306"
      - "RABBITMQ_SERVICE_HOST=localhost"
      - "RABBITMQ_HOST_PORT=5672"
      - "RABBITMQ_DEFAULT_USER=admin"
      - "RABBITMQ_DEFAULT_PASS=123456"
      - "DATABASE_JDBC_URL_PARAMS=serverTimezone=Asia/Shanghai&createDatabaseIfNotExist=true"
      - "Entrypoint_1_NAME=运维监控软件"
      - "Entrypoint_1_PORT=80"
      - "Entrypoint_1_PATH=/#/monitor/home/<USER>"
      - "Entrypoint_1_TAGS=服务端,集控平台"
      - "Entrypoint_2_NAME=会议预定软件"
      - "Entrypoint_2_PORT=80"
      - "Entrypoint_2_PATH=/#/meeting/home/<USER>"
      - "Entrypoint_2_TAGS=服务端,集控平台"
      - "Entrypoint_3_NAME=可视化交互软件"
      - "Entrypoint_3_PORT=80"
      - "Entrypoint_3_PATH=/#/visualization/home/<USER>"
      - "Entrypoint_3_TAGS=服务端,集控平台"
      - "Entrypoint_5_NAME=录播软件"
      - "Entrypoint_5_PORT=80"
      - "Entrypoint_5_PATH=/#/director/home/<USER>"
      - "Entrypoint_5_TAGS=服务端,录播平台"
      - "Entrypoint_6_NAME=可视化交互软件web操作端"
      - "Entrypoint_6_PORT=80"
      - "Entrypoint_6_PATH=/browser.html"
      - "Entrypoint_6_TAGS=客户端,集控平台"

  rabbitmq:
    image: rabbitmq:3.12.2-management
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=123456
      - RABBITMQ_ERLANG_COOKIE=MEDIACOMM_SKYLINK_SECRET_COOKIE_STRING_FOR_RABBITMQ
    network_mode: bridge
    ports:
      - "5672:5672" #JMS Port
      - "15672:15672" #Management Port
      - "1883:1883"
    command: "/bin/bash -c \"rabbitmq-plugins enable --offline rabbitmq_mqtt; rabbitmq-server\""
    container_name: rabbitmq
    hostname: rabbitmq
    restart: always

  mysql:
    container_name: mysql
    build:
      context: .
      dockerfile: mysql/Dockerfile
    image: skylink/mysql:8.0.19-13
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      TZ: Asia/Shanghai
      LANG: C.UTF_8
    network_mode: bridge
    ports:
      - "3306:3306"
    healthcheck:
       test: [ "CMD", "mysqladmin" ,"ping", "-h", "localhost" ]
       interval: 5s
       timeout: 10s
       retries: 10
    restart: always

  redis:
    image: redis/redis-stack-server:7.2.0-v6
    container_name: redis
    environment:
      TZ: Asia/Shanghai
    restart: always
    network_mode: bridge
    ports:
      - "6379:6379"
