package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.DeviceGroup;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.DeviceGroupService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.sysenum.OperateType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "设备分组", description = "设备分组配置的API")
@RestController
@RequestMapping(ResUrlDef.DEVICE_GROUP)
public class DeviceGroupController extends SkyLinkController<DeviceGroup, DeviceGroupService> {
  @Operation(summary = "获取所有分组信息")
  @GetMapping("/groups")
  public Result<Collection<DeviceGroup>> getDeviceGroups() {
    return Result.ok(service.list());
  }

  /**
   * .
   */
  @OperationLogRecord(title = "修改设备分组", operateType = OperateType.UPDATE, requestBody = "#{group}")
  @Operation(summary = "根据Id修改指定设备分组")
  @PutMapping("/{id}")
  public Result<?> updateDeviceGroupById(@PathVariable Integer id,
                                                   @RequestBody DeviceGroup group) {
    group.setGroupId(id);
    service.updateById(group);
    return Result.ok();
  }

  @OperationLogRecord(title = "删除设备分组", operateType = OperateType.DELETE)
  @Operation(summary = "根据Id删除指定设备分组")
  @DeleteMapping("/{id}")
  public Result<?> delDeviceGroupById(@PathVariable Integer id) {
    service.removeById(id);
    return Result.ok();
  }

  @OperationLogRecord(title = "新增设备分组", operateType = OperateType.INSERT, requestBody = "#{group}")
  @Operation(summary = "新增设备分组")
  @PostMapping
  public Result<?> addDeviceGroup(@RequestBody DeviceGroup group) {
    service.save(group);
    return Result.ok();
  }
}
