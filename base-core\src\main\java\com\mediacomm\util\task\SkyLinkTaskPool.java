package com.mediacomm.util.task;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.function.Supplier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Component
public class SkyLinkTaskPool {
  @Qualifier("createThreadPool")
  @Autowired
  private ThreadPoolTaskExecutor threadPoolTaskExecutor;

  public void addTask(Runnable task) {
    threadPoolTaskExecutor.execute(task);
  }

  public <T> CompletableFuture<T> addAsyncTask(Supplier<T> task) {
    return CompletableFuture.supplyAsync(task, threadPoolTaskExecutor);
  }

  public long getActiveCount() {
    return threadPoolTaskExecutor.getActiveCount();
  }

  public Future<?> submit(Runnable task) {
    return threadPoolTaskExecutor.submit(task);
  }

  public void stop() {
    threadPoolTaskExecutor.shutdown();
  }
}
