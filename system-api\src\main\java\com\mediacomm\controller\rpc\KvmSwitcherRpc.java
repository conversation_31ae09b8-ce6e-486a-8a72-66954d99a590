package com.mediacomm.controller.rpc;

import cn.hutool.core.lang.Validator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.mediacomm.config.rabbitmqtt.IMqttSender;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.EnvDevice;
import com.mediacomm.entity.message.reqeust.SwitcherDevice;
import com.mediacomm.entity.message.reqeust.body.PositionRequestBody;
import com.mediacomm.entity.message.reqeust.body.RegisterRequestBody;
import com.mediacomm.entity.message.reqeust.body.ScanRequestBody;
import com.mediacomm.entity.message.reqeust.body.SwitcherDevicesBody;
import com.mediacomm.entity.message.reqeust.body.UpgradePackagePathRequestBody;
import com.mediacomm.entity.message.reqeust.body.UpgradeRequestBody;
import com.mediacomm.entity.vo.EnvDeviceVo;
import com.mediacomm.pojo.SwitcherPreInfoDto;
import com.mediacomm.switcher.domain.SwitcherRpcConstants;
import com.mediacomm.switcher.domain.SwitcherRpcRequestBody;
import com.mediacomm.switcher.domain.body.UpgradePackageInfo;
import com.mediacomm.switcher.domain.body.UpgradePackageList;
import com.mediacomm.switcher.domain.body.UpgradePackageVersion;
import com.mediacomm.switcher.util.update.SwitcherVersion;
import com.mediacomm.switcher.util.update.UpdateUtils;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.EnvDeviceService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import com.mediacomm.util.BuildMqRequestBodyUtils;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.RpcSenderUtils;
import com.mediacomm.util.SkyLinkStringUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigInteger;
import java.nio.file.Files;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * KvmSwitcherRpc.
 */
@Slf4j
@Tag(name = "远程Kvm切换器调用方法")
@RestController
@RequestMapping(ResUrlDef.KVM_SWITCHER_RPC)
public class KvmSwitcherRpc extends SkyLinkController<EnvDevice, EnvDeviceService> {
  private static final String UPGRADE_PACKAGE_PATH = System.getProperty("user.home") + "/upgrade/";
  private static final String FILE_EXTENSION = ".swu";

  @Autowired
  private RpcSenderUtils senderUtils;
  @Autowired
  private RedisUtil redisUtil;
  @Autowired
  private EnvDeviceService envDeviceService;
  @Autowired
  private IMqttSender iMqttSender;


  @Operation(summary = "扫描切换器设备")
  @PostMapping("/scan")
  public Result<?> scan(@RequestBody ScanRequestBody scanBody) {
    return doCmd(RoutingOperation.SCAN, scanBody);
  }

  @Operation(summary = "获取已经注册的设备")
  @GetMapping("/get-registered")
  public Result<Collection<EnvDeviceVo>> getRegistered() {
    Collection<EnvDeviceVo> envDeviceVos =
        service.allByDeviceType(DeviceType.SWITCHER_TX.getDeviceType());
    return Result.ok(envDeviceVos);
  }

  @Operation(summary = "设置切换器IP")
  @PostMapping("/setIp")
  public Result<?> setIp(@RequestBody SwitcherDevicesBody requestBody) {
    return doCmd(RoutingOperation.SET_IP, requestBody);
  }

  @Operation(summary = "注册切换器")
  @PostMapping("/register")
  public Result<?> register(@RequestBody RegisterRequestBody requestBody) {
    return doCmd(RoutingOperation.REGISTER, requestBody);
  }

  @Operation(summary = "解绑切换器")
  @PostMapping("/unregister")
  public Result<?> unregister(@RequestBody SwitcherDevicesBody requestBody) {
    return doCmd(RoutingOperation.UNREGISTER, requestBody);
  }

  @Operation(summary = "上传升级包")
  @PostMapping("/upload-upgrade-package")
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = {@Content(
      mediaType = "multipart/form-data",
      schema = @Schema(type = "object"),
      schemaProperties = {
          @SchemaProperty(
              name = "multipartFile",
              schema = @Schema(type = "string", format = "binary")
          )}
  )})
  public Result<String> uploadUpgradePackage(MultipartFile multipartFile) {
    if (multipartFile.getSize() == 0) {
      return Result.failure("文件的大小为0", ResponseCode.EX_FAILURE_400);
    }
    SwitcherVersion switcherVersion = checkUpgradePackage(multipartFile);
    if (switcherVersion == null) {
      return Result.failure("升级包校验失败", ResponseCode.EX_FAILURE_400);
    }
    String packageMd5 = getPackageMd5(multipartFile);
    if (Strings.isNullOrEmpty(packageMd5)) {
      return Result.failure("获取文件md5失败", ResponseCode.EX_FAILURE_400);
    }
    createDirectoryIfNotExists();
    File newFile = new File(UPGRADE_PACKAGE_PATH + packageMd5 + FILE_EXTENSION);
    if (newFile.exists()) {
      return Result.failure("升级包已存在", ResponseCode.EX_FAILURE_400);
    }
    try {
      multipartFile.transferTo(newFile);
    } catch (IOException ex) {
      log.error("upload fail", ex);
      return Result.failure("upload fail", ResponseCode.EX_FAILURE_400);
    }
    clearUpgradePackage();
    return Result.ok("upload success");
  }

  @Operation(summary = "获取升级包列表")
  @GetMapping("/get-upgrade-list")
  public Result<?> getUpgradeList() {
    File upgradeFile = new File(UPGRADE_PACKAGE_PATH);
    if (!Files.exists(upgradeFile.toPath())) {
      return Result.ok("no upgrade package");
    }
    UpgradePackageList list = new UpgradePackageList();
    if (upgradeFile.isDirectory()) {
      File[] files = upgradeFile.listFiles();
      if (files != null) {
        for (File file : files) {
          if (file.isFile()) {
            try {
              SwitcherVersion version = UpdateUtils.getUpdatePkgInfo(file.toString());
              if (version != null) {
                UpgradePackageVersion packageVersion = new UpgradePackageVersion();
                packageVersion.setVersion(version);
                packageVersion.setLastModified(file.lastModified());
                packageVersion.setPath(file.getName().replace(".swu", ""));
                list.getVersions().add(packageVersion);
              }
            } catch (IOException ex) {
              log.error("获取升级包信息失败", ex);
            }
          }
        }
      }
    }
    return Result.ok(list);
  }

  @Operation(summary = "删除升级包")
  @PostMapping("/delete-upgrade-package")
  public Result<?> deleteUpgradePackage(@RequestBody UpgradePackagePathRequestBody requestBody) {
    if (requestBody.getPath() == null) {
      return Result.failure("path is null", ResponseCode.EX_FAILURE_400);
    }
    String fullPath = UPGRADE_PACKAGE_PATH + requestBody.getPath() + FILE_EXTENSION;
    File upgradeFile = new File(fullPath);
    if (upgradeFile.exists()) {
      if (upgradeFile.delete()) {
        return Result.ok("删除升级包成功");
      } else {
        return Result.failure("删除升级包失败", ResponseCode.EX_FAILURE_400);
      }
    } else {
      return Result.failure("升级包不存在", ResponseCode.EX_FAILURE_400);
    }
  }

  @Operation(summary = "升级切换器")
  @PostMapping("/upgrade")
  public Result<?> upgrade(@RequestBody UpgradeRequestBody requestBody) {
    String host = requestBody.getHost();
    if (!Validator.isIpv4(host)) {
      return Result.failure("host is not ip", ResponseCode.EX_FAILURE_400);
    }
    String version = requestBody.getVersion();
    String packagePath =
        "http://" + host + ":8181" + ResUrlDef.KVM_SWITCHER_RPC + "/download"
            + "?version=" + version;
    File upgradeFile = new File(UPGRADE_PACKAGE_PATH + version + FILE_EXTENSION);
    if (!upgradeFile.exists()) {
      return Result.failure("升级包不存在", ResponseCode.EX_FAILURE_400);
    }
    try {
      SwitcherVersion switcherVersion = UpdateUtils.getUpdatePkgInfo(upgradeFile.toString());
      if (switcherVersion != null) {
        String appVersion = switcherVersion.getApp();
        for (SwitcherDevice device : requestBody.getDevices()) {
          EnvDeviceVo envDeviceVo = envDeviceService.oneBySnAndMac(device.getSn(), device.getMac());
          if (envDeviceVo != null) {
            SwitcherRpcRequestBody<UpgradePackageInfo> rpcRequestBody = new SwitcherRpcRequestBody<>();
            rpcRequestBody.setSource(SwitcherRpcConstants.SERVER);
            rpcRequestBody.setParams(new UpgradePackageInfo(packagePath, appVersion));
            String msgId = SkyLinkStringUtil.uuid();
            rpcRequestBody.setMsgId(msgId);
            iMqttSender.sendToMqtt(JsonUtils.encode(rpcRequestBody),
                SwitcherRpcConstants.extRpcRequest(envDeviceVo.getId(),
                    SwitcherRpcConstants.UPGRADE));
          }
        }
      } else {
        return Result.failure("升级包校验失败", ResponseCode.EX_FAILURE_400);
      }
    } catch (IOException ex) {
      log.error("获取升级包信息失败", ex);
    }
    return Result.ok("upgrade start");
  }

  @Operation(summary = "下载升级包")
  @GetMapping("/download")
  public boolean downloadUpgradePackage(@RequestParam("version") String version, HttpServletResponse res) {
    File file = new File(UPGRADE_PACKAGE_PATH + version + FILE_EXTENSION);
    if (!file.exists()) {
      return false;
    }
    String fileName = version + FILE_EXTENSION;
    res.setHeader("Content-Disposition", "attachment;filename=" + fileName);
    byte[] buff = new byte[1024];
    BufferedInputStream bis = null;
    OutputStream os;
    try {
      os = res.getOutputStream();
      bis = new BufferedInputStream(new FileInputStream(file));
      int i = bis.read(buff);
      while (i != -1) {
        os.write(buff, 0, buff.length);
        os.flush();
        i = bis.read(buff);
      }
    } catch (IOException ex) {
      log.error("download fail", ex);
    } finally {
      if (bis != null) {
        try {
          bis.close();
        } catch (IOException ex) {
          log.error("bis close fail", ex);
        }
      }
    }
    System.out.println("success");
    return true;
  }

  @Operation(summary = "设置切换器位置")
  @PostMapping("/set-position")
  public Result<EnvDevice> setPosition(@RequestBody PositionRequestBody requestBody) {
    EnvDevice switcher = envDeviceService.getById(requestBody.getId());
    if (switcher != null) {
      Property position = Property.findFromArray("room", switcher.getCollectorProperties());
      if (position != null) {
        position.setPropertyValue(requestBody.getRoom());
      } else {
        switcher.getCollectorProperties().add(new Property("room", requestBody.getRoom()));
      }
      Property group = Property.findFromArray("group", switcher.getCollectorProperties());
      if (group != null) {
        group.setPropertyValue(requestBody.getGroup());
      } else {
        switcher.getCollectorProperties().add(new Property("group", requestBody.getGroup()));
      }
      envDeviceService.updateById(switcher);
      return Result.ok(switcher);
    }
    return Result.failure("Switcher device not found.", ResponseCode.EX_NOTFOUND_404);
  }

  @Operation(summary = "获取切换器概要信息")
  @GetMapping("/preliminary")
  public Result<SwitcherPreInfoDto> getPre() {
    SwitcherPreInfoDto infoDto = new SwitcherPreInfoDto();
    Collection<EnvDeviceVo> deviceVos = getSwitchers();
    deviceVos.forEach(deviceVo -> {
      String switcherGroup = Property.findValueByKey(deviceVo.getCollectorProperties(), "group", "");
      if (StringUtils.isNotEmpty(switcherGroup)) {
        infoDto.getPreInfos().computeIfAbsent(switcherGroup, k -> new ArrayList<>());
        String redisKey = RedisSignalKey.getDeviceStatusKey(deviceVo.getDeviceType(), deviceVo.getId());
        Optional<String> link = redisUtil.hget(redisKey, RedisSignalKey.LINK_STATUS);
        if (link.isPresent()) {
          DeviceSignalValue value = JsonUtils.decode(link.get(), DeviceSignalValue.class);
          if (value != null) {
            boolean linkValue = value.getSignalValue().getBitValue();
            SwitcherPreInfoDto.PreInfo preInfo = new SwitcherPreInfoDto.PreInfo();
            preInfo.setSwitcher(deviceVo);
            preInfo.setLinkStatus(linkValue);
            Optional<String> resolution = redisUtil.hget(redisKey, RedisSignalKey.RESOLUTION);
            if (resolution.isPresent()) {
              DeviceSignalValue resolutionValue = JsonUtils.decode(resolution.get(), DeviceSignalValue.class);
              if (resolutionValue != null) {
                preInfo.setResolution(resolutionValue.getSignalValue().getDescValue());
              }
            }
            infoDto.getPreInfos().get(switcherGroup).add(preInfo);
            DeviceType deviceType = DeviceType.valueOf(deviceVo.getDeviceType());
            if (deviceType.getSubSystem() == SubSystemType.PER_EQUIPMENT_RX) {
              infoDto.setRxNum(infoDto.getRxNum() + 1);
              if (linkValue) {
                infoDto.setRxLinkFailNum(infoDto.getRxLinkFailNum() + 1);
              }
            } else if (deviceType.getSubSystem() == SubSystemType.PER_EQUIPMENT_TX) {
              infoDto.setTxNum(infoDto.getTxNum() + 1);
              if (linkValue) {
                infoDto.setTxLinkFailNum(infoDto.getTxLinkFailNum() + 1);
              }
            } else {
              log.error("Unknown device type {}", deviceType);
            }
          } else {
            log.error("Error link signal value format {} -> {}!", redisKey, link.get());
          }
        }

      }
    });
    return Result.ok(infoDto);
  }

  @Operation(summary = "获取单个切换器的监控信息")
  @GetMapping("/value")
  public Result<Collection<DeviceSignalValue>> getValue(@RequestParam String deviceId) {
    if (StringUtils.isNotEmpty(deviceId)) {
      EnvDeviceVo switcher = envDeviceService.oneById(deviceId);
      if (switcher != null) {
        String redisKey = RedisSignalKey.getDeviceStatusKey(switcher.getDeviceType(), switcher.getId());
        Optional<Map<String, String>> deviceSignalValue = redisUtil.hmget(redisKey);
        Collection<DeviceSignalValue> values = new ArrayList<>();
        deviceSignalValue.ifPresent(stringStringMap -> stringStringMap.forEach((k, v) -> {
          DeviceSignalValue signalValue = JsonUtils.decode(v, DeviceSignalValue.class);
          if (signalValue != null) {
            values.add(signalValue);
          }
        }));
        return Result.ok(values);
      } else {
        return Result.failure("device not exist.", ResponseCode.EX_FAILURE_400);
      }
    }
    return Result.failure("deviceId is empty.", ResponseCode.EX_FAILURE_400);
  }

  @Operation(summary = "根据分组获取多个切换器监控信息")
  @GetMapping("/values")
  public Result<Map<String, List<DeviceSignalValue>>> getValues(@RequestParam String groupName) {
    Collection<EnvDeviceVo> deviceVos = getSwitchers();
    if (StringUtils.isNotEmpty(groupName)) {
      deviceVos.removeIf(deviceVo -> {
        String switcherGroup = Property.findValueByKey(deviceVo.getCollectorProperties(), "group", "");
        return !switcherGroup.equals(groupName);
      });
    } else {
      return Result.failure("groupName is empty.", ResponseCode.EX_FAILURE_400);
    }
    Map<String, List<DeviceSignalValue>> values = new HashMap<>();
    for (EnvDeviceVo switcher : deviceVos) {
      String redisKey = RedisSignalKey.getDeviceStatusKey(switcher.getDeviceType(), switcher.getId());
      Optional<Map<String, String>> deviceSignalValue = redisUtil.hmget(redisKey);
      if (deviceSignalValue.isPresent()) {
        values.put(switcher.getId(), new ArrayList<>());
        deviceSignalValue.get().forEach((k, v) -> {
          DeviceSignalValue signalValue = JsonUtils.decode(v, DeviceSignalValue.class);
          if (signalValue != null) {
            values.get(switcher.getId()).add(signalValue);
          }
        });
      }
    }
    return Result.ok(values);
  }

  private SwitcherVersion checkUpgradePackage(MultipartFile file) {
    try {
      return UpdateUtils.getUpdatePkgInfo(file.getInputStream());
    } catch (IOException e) {
      log.error("checkUpgradePackage fail", e);
    }
    return null;
  }

  private String getPackageMd5(MultipartFile file) {
    try {
      MessageDigest md = MessageDigest.getInstance("MD5");
      try (BufferedInputStream bis = new BufferedInputStream(file.getInputStream())) {
        byte[] buffer = new byte[1024];
        int read = 0;
        while ((read = bis.read(buffer)) != -1) {
          md.update(buffer, 0, read);
        }
        byte[] digest = md.digest();
        return String.format("%032x", new BigInteger(1, digest));
      } catch (IOException e) {
        log.error("getPackageMd5 fail", e);
        return null;
      }
    } catch (NoSuchAlgorithmException ex) {
      log.error("getPackageMd5 fail", ex);
      return null;
    }
  }

  private void createDirectoryIfNotExists() {
    File file = new File(UPGRADE_PACKAGE_PATH);
    if (!file.exists() && !file.mkdirs()) {
      log.error("create directory {} fail", UPGRADE_PACKAGE_PATH);
    }
  }

  /**
   * 清理升级包,保留5个.
   */
  private void clearUpgradePackage() {
    File file = new File(UPGRADE_PACKAGE_PATH);
    File[] files = file.listFiles();
    List<File> fileList = new ArrayList<>();
    if (files != null) {
      for (File f : files) {
        try {
          SwitcherVersion version = UpdateUtils.getUpdatePkgInfo(f.toString());
          if (version != null) {
            fileList.add(f);
          }
        } catch (IOException ex) {
          log.error("file {} is not upgrade file", f.getName(), ex);
        }
      }
    }
    if (fileList.size() > 5) {
      fileList.sort(Comparator.comparingLong(File::lastModified));
      int len = fileList.size() - 5;
      for (int i = 0; i < len; i++) {
        if (!fileList.get(i).delete()) {
          log.error("delete {} fail", files[i].getName());
        }
      }
    }
  }

  private <T> Result<T> doCmd(String topicDto, T requestBody) {
    return JsonUtils.decode(senderUtils.send(RoutingKey.KVM_SWITCHER + topicDto,
            BuildMqRequestBodyUtils.buildMqBody(requestBody)), new TypeReference<>() {
    });
  }

  private Collection<EnvDeviceVo> getSwitchers() {
    Collection<EnvDeviceVo> txes = envDeviceService
            .allByDeviceType(DeviceType.SWITCHER_TX.getDeviceType());
    Collection<EnvDeviceVo> rxes = envDeviceService
            .allByDeviceType(DeviceType.SWITCHER_RX.getDeviceType());
    Collection<EnvDeviceVo> deviceVos =
            new ArrayList<>(Stream.of(txes, rxes).flatMap(Collection::stream).toList());
    return deviceVos;
  }
}
