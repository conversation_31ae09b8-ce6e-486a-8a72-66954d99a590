package com.mediacomm.caesar.task;

import com.mediacomm.caesar.preview.strategy.PreviewInfoOperator;
import com.mediacomm.caesar.preview.strategy.PreviewStrategy;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class RefreshPreviewRunner extends PreviewThread {

  private PreviewStrategy strategy;
  private PreviewInfoOperator operator;

  public RefreshPreviewRunner(PreviewStrategy strategy, PreviewInfoOperator previewInfoOperator) {
    this.strategy = strategy;
    this.operator = previewInfoOperator;
  }

  @Override
  public String getThreadName() {
    return "RefreshPreviewRunner Thread";
  }

  @Override
  protected void execute() {
    strategy.refresh(operator);
    try {
      Thread.sleep(5000);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }
  }

  @Override
  public boolean isStop() {
    return stop;
  }

  @Override
  public void stop() {
    this.stop = true;
  }
}
