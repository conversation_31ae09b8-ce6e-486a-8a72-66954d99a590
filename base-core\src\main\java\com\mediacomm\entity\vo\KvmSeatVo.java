package com.mediacomm.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.KvmSeatDecoder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class KvmSeatVo extends KvmSeat {
  private String masterName;

  /**
   * .
   */
  public KvmSeatVo(KvmSeat seat) {
    setMasterId(seat.getMasterId());
    setName(seat.getName());
    setSeatId(seat.getSeatId());
    setDecoders(seat.getDecoders());
    setColCount(seat.getColCount());
    setRowCount(seat.getRowCount());
    setOptModelEnable(seat.isOptModelEnable());
    setPositionId(seat.getPositionId());
    setSeqInPosition(seat.getSeqInPosition());
    if (seat.getDecoders() != null) {
      for (KvmSeatDecoder decoder : seat.getDecoders()) {
        decoder.setSeatId(seat.getSeatId());
      }
    }
  }
}
