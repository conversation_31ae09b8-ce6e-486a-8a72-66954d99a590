package com.mediacomm.hikvision.api.video;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.video.cruiseroutes.ControllingRequest;
import com.mediacomm.hikvision.entity.video.cruiseroutes.DeleteRequest;
import com.mediacomm.hikvision.entity.video.cruiseroutes.SearchRequest;
import com.mediacomm.hikvision.entity.video.cruiseroutes.UpdateRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * Auto Create on 2022-08-24 17:49:35.
 *
 * <AUTHOR>
 */
public class CruiseRoutesFunctionsApi {


  /**
   * 删除巡航路径.
   *
   * @param config        ArtemisConfig
   * @param deleteRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String delete(ArtemisConfig config, DeleteRequest deleteRequest) throws Exception {
    String deleteDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/cruiseRoutes/delete";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, deleteDataApi);
    String body = JsonUtils.encode(deleteRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  //

  /**
   * 巡航路径控制.
   *
   * @param config             ArtemisConfig
   * @param controllingRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String controlling(ArtemisConfig config, ControllingRequest controllingRequest)
      throws Exception {
    String controllingDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/cruiseRoutes/controlling";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, controllingDataApi);
    String body = JsonUtils.encode(controllingRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  //

  /**
   * 查询巡航路径.
   *
   * @param config        ArtemisConfig
   * @param searchRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String search(ArtemisConfig config, SearchRequest searchRequest) throws Exception {
    String searchDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/cruiseRoutes/search";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, searchDataApi);
    String body = JsonUtils.encode(searchRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  //

  /**
   * 添加或修改巡航路径.
   *
   * @param config        ArtemisConfig
   * @param updateRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String update(ArtemisConfig config, UpdateRequest updateRequest) throws Exception {
    String updateDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/cruiseRoutes/update";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, updateDataApi);
    String body = JsonUtils.encode(updateRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }
}
