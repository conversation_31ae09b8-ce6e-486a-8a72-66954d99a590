package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部门表.
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "Department", description = "部门对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Department extends SkyLinkDbEntity {
  @Schema(hidden = true)
  @TableId(value = "department_id", type = IdType.AUTO) //指定自增策略
  private Integer departmentId;
  private String departmentDesc;
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private String departmentName;
}
