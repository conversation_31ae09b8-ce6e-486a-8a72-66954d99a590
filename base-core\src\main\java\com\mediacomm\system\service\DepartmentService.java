package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Department;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.DepartmentMapper;
import com.mediacomm.system.variable.ResponseCode;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class DepartmentService extends SkyLinkServiceImpl<DepartmentMapper, Department> {

  /**
   * 提前检查是否有重复的数据再插入.
   *
   * @param department Department.
   * @return 有重复数据时返回400，其他失败原因返回500，成功则返回200.
   */
  public Result<Department> saveDepartment(Department department) {
    Long count = baseMapper.selectCount(new QueryWrapper<Department>()
        .eq("department_name", department.getDepartmentName())
        .or()
        .eq("department_id", department.getDepartmentId()));
    if (count > 0) {
      return Result.failure("Data deduplication!", ResponseCode.EX_FAILURE_400);
    } else {
      return save(department) ? Result.ok() :
          Result.failure("SQL execute failure!", ResponseCode.EX_FAILURE_500);
    }
  }

  /**
   * 提前检查是否有重复的数据再修改.
   *
   * @param department Department.
   * @return 有重复数据返回400，其他失败原因返回500，成功则返回200.
   */
  public Result<Department> updateDepartmentById(Department department) {
    Long count = baseMapper.selectCount(new QueryWrapper<Department>()
        .ne("department_id", department.getDepartmentId())
        .eq("department_name", department.getDepartmentName()));
    if (count > 0) {
      return Result.failure("Data deduplication!", ResponseCode.EX_FAILURE_400);
    } else {
      return updateById(department) ? Result.ok() :
          Result.failure("SQL execute failure!", ResponseCode.EX_FAILURE_500);
    }
  }

}
