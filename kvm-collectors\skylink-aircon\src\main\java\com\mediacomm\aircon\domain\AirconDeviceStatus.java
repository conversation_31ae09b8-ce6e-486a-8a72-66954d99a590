package com.mediacomm.aircon.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AirconDeviceStatus {
  private double cpuRate;
  private int deviceId;
  private double diskRate;
  private double memRate;
  private boolean linkStatus;
  private String loginUser;
  private boolean powerStatus;
  private int input;
  private int inputHeight;
  private int inputWidth;
  private boolean isServer;
  private List<AirconServerStatus.NetStatus> netStatus;
  private String sn;
  @JsonProperty("typBStatus")
  private int typBStatus;
  private int videoLineStatus;
  private double temperature;
}
