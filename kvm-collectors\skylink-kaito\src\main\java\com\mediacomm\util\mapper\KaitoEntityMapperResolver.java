package com.mediacomm.util.mapper;

import com.mediacomm.domain.KaitoDeviceDetail;
import com.mediacomm.domain.KaitoInput;
import com.mediacomm.domain.KaitoIpcDetail;
import com.mediacomm.domain.KaitoLayerDetail;
import com.mediacomm.domain.KaitoOutput;
import com.mediacomm.domain.KaitoScreenDetail;
import com.mediacomm.domain.h.KaitoHIpc;
import com.mediacomm.domain.h.KaitoHLayer;
import com.mediacomm.domain.h.KaitoHScreenDetail;
import com.mediacomm.domain.request.KaitoLayerLayoutReq;
import com.mediacomm.domain.request.KaitoWriteSourceReq;
import com.mediacomm.domain.request.KaitoWriteWindowReq;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.KvmVideoWallDecoder;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.sysenum.BannerType;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.SkyLinkStringUtil;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.BeforeMapping;
import org.mapstruct.Context;
import org.mapstruct.MappingTarget;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Slf4j
@Component
public class KaitoEntityMapperResolver {
  @Resource
  private KvmAssetService assetService;
  @Resource
  private KvmVideoWallService videoWallService;

  @BeforeMapping
  public void toKvmAssetBefore(@NotNull KaitoInput kaitoInput, @MappingTarget KvmAsset kvmAsset,
                               @Context String masterId) {
    kvmAsset.setAssetId(SkyLinkStringUtil.uuid());
    kvmAsset.setMasterId(masterId);
    kvmAsset.setHardcode(String.format("%s.input.%d", masterId, kaitoInput.getInputId()));
    kvmAsset.setDeviceModel(DeviceType.KAITO02_INPUT.getDeviceTypeId());
  }

  @BeforeMapping
  public void toKvmAssetBefore(@NotNull KaitoHIpc kaitoHIpc, @MappingTarget KvmAsset kvmAsset,
                               @Context String masterId) {
    kvmAsset.setAssetId(SkyLinkStringUtil.uuid());
    kvmAsset.setMasterId(masterId);
    kvmAsset.setHardcode(String.format("%s.ipc.%d", masterId, kaitoHIpc.getSourceId()));
    kvmAsset.setDeviceModel(DeviceType.KAITO02_IPC.getDeviceTypeId());
  }

  @BeforeMapping
  public void toKvmAssetBefore(@NotNull KaitoIpcDetail kaitoIpcDetail, @MappingTarget KvmAsset kvmAsset,
                               @Context String masterId) {
    kvmAsset.setAssetId(SkyLinkStringUtil.uuid());
    kvmAsset.setMasterId(masterId);
    kvmAsset.setHardcode(String.format("%s.ipc.%d", masterId, kaitoIpcDetail.getSourceId()));
    kvmAsset.setDeviceModel(DeviceType.KAITO02_IPC.getDeviceTypeId());
    List<Property> property = new ArrayList<>();
    for (KaitoIpcDetail.Stream stream : kaitoIpcDetail.getFirstChannel().getStreamList()) {
      KaitoIpcDetail.Rtsp rtsp = stream.getProtocol().getRtsp();
      String fullPath = String.format("rtsp://%s:%s@%s", rtsp.getRtspUsrName(), rtsp.getRtspPassWord(),
              rtsp.getRtspUrl().substring(rtsp.getRtspUrl().indexOf(":") + 3));
      switch (stream.getStreamIndex()) {
        case 0 -> {
          property.add(new Property("mainStreamId", String.valueOf(stream.getStreamId())));
          property.add(new Property("mainStreamUrl", fullPath));
        }
        case 1 -> {
          property.add(new Property("subStreamId", String.valueOf(stream.getStreamId())));
          property.add(new Property("subStreamUrl", fullPath));
        }
        default -> log.warn("Undefine kaito stream index:" + stream.getStreamIndex());
      }
    }
    kvmAsset.setProperties(property);
  }

  @BeforeMapping
  public void toKvmAssetBefore(@NotNull KaitoOutput kaitoOutput, @MappingTarget KvmAsset kvmAsset,
                               @Context String masterId) {
    kvmAsset.setAssetId(SkyLinkStringUtil.uuid());
    kvmAsset.setMasterId(masterId);
    kvmAsset.setHardcode(String.format("%s.output.%d", masterId, kaitoOutput.getOutputId()));
    kvmAsset.setDeviceModel(DeviceType.KAITO02_OUTPUT.getDeviceTypeId());
  }

  @BeforeMapping
  public void toKvmVideoWallBefore(@NotNull KaitoScreenDetail kaitoScreenDetail,
                                   @MappingTarget KvmVideoWall kvmVideoWall,
                                   @Context String masterId) {
    List<Property> properties = new ArrayList<>();
    int posX = kaitoScreenDetail.getOutputMode().getSize().getX();
    int posY = kaitoScreenDetail.getOutputMode().getSize().getY();
    properties.add(new Property(PropertyKeyConst.POS_X, String.valueOf(posX)));
    properties.add(new Property(PropertyKeyConst.POS_Y, String.valueOf(posY)));
    List<KvmVideoWallDecoder> decoders = new ArrayList<>();
    for (KaitoScreenDetail.ScreenInterface screenInterface : kaitoScreenDetail.getOutputMode().getScreenInterfaces()) {
      KvmVideoWallDecoder decoder = new KvmVideoWallDecoder();
      decoder.setDeviceId(screenInterface.getOutputId());
      decoder.setXpos(screenInterface.getX() - posX);
      decoder.setYpos(screenInterface.getY() - posY);
      decoder.setHeight(screenInterface.getHeight());
      decoder.setWidth(screenInterface.getWidth());
      decoders.add(decoder);
    }
    int screenId = kaitoScreenDetail.getScreenId();
    kvmVideoWall.setUniqueSearchKey(String.join(".", masterId, "kaito",
            String.valueOf(screenId)));
    kvmVideoWall.setDeviceId(screenId);
    kvmVideoWall.setDeviceModel(DeviceType.KAITO02_VIDEO_WALL.getDeviceTypeId());
    kvmVideoWall.setMasterId(masterId);
    kvmVideoWall.setName(kaitoScreenDetail.getGeneral().getName());
    kvmVideoWall.setRowCount(kaitoScreenDetail.getOutputMode().getMosaic().getRow());
    kvmVideoWall.setColCount(kaitoScreenDetail.getOutputMode().getMosaic().getColumn());
    kvmVideoWall.setProperties(properties);
    kvmVideoWall.setDecoders(decoders);
    kvmVideoWall.setBannerType(BannerType.BANNER_KAITO);
  }

  @BeforeMapping
  public void toKvmVideoWallBefore(@NotNull KaitoHScreenDetail kaitoScreenDetail,
                                   @MappingTarget KvmVideoWall kvmVideoWall,
                                   @Context String masterId) {
    List<Property> properties = new ArrayList<>();
    int posX = kaitoScreenDetail.getOutputMode().getSize().getX();
    int posY = kaitoScreenDetail.getOutputMode().getSize().getY();
    properties.add(new Property(PropertyKeyConst.POS_X, String.valueOf(posX)));
    properties.add(new Property(PropertyKeyConst.POS_Y, String.valueOf(posY)));
    List<KvmVideoWallDecoder> decoders = new ArrayList<>();
    for (KaitoScreenDetail.ScreenInterface screenInterface : kaitoScreenDetail.getOutputMode().getScreenInterfaces()) {
      KvmVideoWallDecoder decoder = new KvmVideoWallDecoder();
      decoder.setDeviceId(screenInterface.getOutputId());
      decoder.setXpos(screenInterface.getX() - posX);
      decoder.setYpos(screenInterface.getY() - posY);
      decoder.setHeight(screenInterface.getHeight());
      decoder.setWidth(screenInterface.getWidth());
      decoders.add(decoder);
    }
    int screenId = kaitoScreenDetail.getScreenId();
    kvmVideoWall.setUniqueSearchKey(String.join(".", masterId, "kaito",
            String.valueOf(screenId)));
    kvmVideoWall.setDeviceId(screenId);
    kvmVideoWall.setDeviceModel(DeviceType.KAITO02_VIDEO_WALL.getDeviceTypeId());
    kvmVideoWall.setMasterId(masterId);
    kvmVideoWall.setName(kaitoScreenDetail.getGeneral().getName());
    kvmVideoWall.setRowCount(kaitoScreenDetail.getOutputMode().getMosaic().getRow());
    kvmVideoWall.setColCount(kaitoScreenDetail.getOutputMode().getMosaic().getColumn());
    kvmVideoWall.setProperties(properties);
    kvmVideoWall.setDecoders(decoders);
    kvmVideoWall.setBannerType(BannerType.BANNER_KAITO);
  }

  @BeforeMapping
  public void toPanelRectBefore(@NotNull KaitoLayerDetail kaitoLayerDetail, @MappingTarget PanelRect panelRect,
                                @Context Integer videoWallId) {
    KvmVideoWall wall = videoWallService.getById(videoWallId);
    KvmAssetVo asset = null;
    switch (kaitoLayerDetail.getSource().getSourceType()) {
      case 3 -> asset = assetService.oneByDeviceIdAndModelId(
              kaitoLayerDetail.getSource().getIpcSourceId(), DeviceType.KAITO02_IPC.getDeviceTypeId(),
              wall.getMasterId());
      case 1 -> asset = assetService.oneByDeviceIdAndModelId(
              kaitoLayerDetail.getSource().getInputId(), DeviceType.KAITO02_INPUT.getDeviceTypeId(),
              wall.getMasterId());
      default ->
        log.warn("Undefine kaito source type:" + kaitoLayerDetail.getSource().getSourceType());
    }
    Map<String, Integer> pos = KaitoInspectUtil.getScreenOffsetValue(wall);
    panelRect.setSeq(kaitoLayerDetail.getGeneral().getZorder());
    panelRect.setXpos(kaitoLayerDetail.getWindow().getX() - pos.get(PropertyKeyConst.POS_X));
    panelRect.setYpos(kaitoLayerDetail.getWindow().getY() - pos.get(PropertyKeyConst.POS_Y));
    panelRect.setWidth(kaitoLayerDetail.getWindow().getWidth());
    panelRect.setHeight(kaitoLayerDetail.getWindow().getHeight());
    if (asset != null) {
      panelRect.addFullScreenLayer(asset, 2);
    }
  }

  @BeforeMapping
  public void toPanelRectBefore(@NotNull KaitoHLayer kaitoHLayer, @MappingTarget PanelRect panelRect,
                                @Context Integer videoWallId) {
    KvmVideoWall wall = videoWallService.getById(videoWallId);
    KvmAssetVo asset = null;
    switch (kaitoHLayer.getSource().getSourceType()) {
      case 3 -> asset = assetService.oneByDeviceIdAndModelId(
              kaitoHLayer.getSource().getSourceId(), DeviceType.KAITO02_IPC.getDeviceTypeId(),
              wall.getMasterId());
      case 1 -> asset = assetService.oneByDeviceIdAndModelId(
              kaitoHLayer.getSource().getInputId(), DeviceType.KAITO02_INPUT.getDeviceTypeId(),
              wall.getMasterId());
      default ->
              log.warn("Undefine kaito source type: {}", kaitoHLayer.getSource().getSourceType());
    }
    Map<String, Integer> pos = KaitoInspectUtil.getScreenOffsetValue(wall);
    panelRect.setSeq(kaitoHLayer.getGeneral().getZorder());
    panelRect.setXpos(kaitoHLayer.getWindow().getX() - pos.get(PropertyKeyConst.POS_X));
    panelRect.setYpos(kaitoHLayer.getWindow().getY() - pos.get(PropertyKeyConst.POS_Y));
    panelRect.setWidth(kaitoHLayer.getWindow().getWidth());
    panelRect.setHeight(kaitoHLayer.getWindow().getHeight());
    if (asset != null) {
      panelRect.addFullScreenLayer(asset, 2);
    }
  }

  @BeforeMapping
  public void toKaitoLayerDetailBefore(@NotNull PanelRect panelRect, @MappingTarget KaitoLayerDetail kaitoLayerDetail,
                                       @Context Integer videoWallId) {
    KvmAsset asset = assetService.getById(panelRect.getVideoSrcId());
    KaitoLayerDetail.General general = new KaitoLayerDetail.General();
    general.setZorder(panelRect.getSeq());
    KaitoLayerDetail.Source source = new KaitoLayerDetail.Source();
    if (asset != null) {
      if (Objects.equals(asset.getDeviceModel(), DeviceType.KAITO02_INPUT.getDeviceTypeId())) {
        source.setSourceType(1);
        source.setInputId(asset.getDeviceId());
      } else if (Objects.equals(asset.getDeviceModel(), DeviceType.KAITO02_IPC.getDeviceTypeId())) {
        source.setSourceType(3);
        source.setIpcSourceId(asset.getDeviceId());
        String streamType = Property.findValueByKey(
                asset.getCollectorProperties(), PropertyKeyConst.STREAM_TYPE, "mainStreamId");
        int streamId = Integer.parseInt(
                Property.findValueByKey(asset.getProperties(), streamType, "0"));
        source.setStreamId(streamId);
        source.setInputId(255);
      }
      general.setName(asset.getName());
    }
    source.setCropId(255);
    kaitoLayerDetail.setSource(source);
    KaitoLayerDetail.Window window = new KaitoLayerDetail.Window();
    KvmVideoWall wall = videoWallService.getById(videoWallId);
    Map<String, Integer> pos = KaitoInspectUtil.getScreenOffsetValue(wall);
    window.setX(panelRect.getXpos() + pos.get(PropertyKeyConst.POS_X));
    window.setY(panelRect.getYpos() + pos.get(PropertyKeyConst.POS_Y));
    window.setWidth(panelRect.getWidth());
    window.setHeight(panelRect.getHeight());
    kaitoLayerDetail.setLayerId(panelRect.getPanelId() == -1 ? 0 : panelRect.getPanelId());
    kaitoLayerDetail.setDeviceId(0);
    kaitoLayerDetail.setWindow(window);
    kaitoLayerDetail.setScreenId(wall.getDeviceId());
    kaitoLayerDetail.setGeneral(general);
  }

  @BeforeMapping
  public void toKaitoWriteWindowReqBefore(@NotNull PanelRect panelRect,
                                          @MappingTarget KaitoWriteWindowReq kaitoWriteWindowReq,
                                          @Context Integer videoWallId) {
    KvmVideoWall wall = videoWallService.getById(videoWallId);
    Map<String, Integer> pos = KaitoInspectUtil.getScreenOffsetValue(wall);
    kaitoWriteWindowReq.setDeviceId(0);
    kaitoWriteWindowReq.setScreenId(wall.getDeviceId());
    kaitoWriteWindowReq.setX(panelRect.getXpos() + pos.get(PropertyKeyConst.POS_X));
    kaitoWriteWindowReq.setY(panelRect.getYpos() + pos.get(PropertyKeyConst.POS_Y));
  }

  @BeforeMapping
  public void toKaitoWriteSourceReqBefore(@NotNull PanelRect panelRect,
                                          @MappingTarget KaitoWriteSourceReq kaitoWriteSourceReq,
                                          @Context Integer videoWallId) {
    KvmVideoWall wall = videoWallService.getById(videoWallId);
    KvmAsset asset = assetService.getById(panelRect.getVideoSrcId());
    kaitoWriteSourceReq.setDeviceId(0);
    kaitoWriteSourceReq.setScreenId(wall.getDeviceId());
    if (asset != null) {
      if (Objects.equals(asset.getDeviceModel(), DeviceType.KAITO02_INPUT.getDeviceTypeId())) {
        kaitoWriteSourceReq.setSourceType(1);
        kaitoWriteSourceReq.setInputId(asset.getDeviceId());
      } else if (Objects.equals(asset.getDeviceModel(), DeviceType.KAITO02_IPC.getDeviceTypeId())) {
        String streamType = Property.findValueByKey(
                asset.getCollectorProperties(), PropertyKeyConst.STREAM_TYPE, "mainStreamId");
        int streamId = Integer.parseInt(
                Property.findValueByKey(asset.getProperties(), streamType, "0"));
        kaitoWriteSourceReq.setSourceType(3);
        kaitoWriteSourceReq.setStreamId(streamId);
        kaitoWriteSourceReq.setIpcSourceId(asset.getDeviceId());
        kaitoWriteSourceReq.setInputId(255);
      }
      kaitoWriteSourceReq.setName(asset.getName());
    }
    kaitoWriteSourceReq.setInterfaceType(0);
    kaitoWriteSourceReq.setCropId(255);
  }

  @BeforeMapping
  public void toKaitoLayerLayoutReqLayerBefore(@NotNull PanelRect panelRect,
                                               @MappingTarget KaitoLayerLayoutReq.Layer layer,
                                               @Context Integer videoWallId) {
    KaitoLayerDetail detail = new KaitoLayerDetail();
    toKaitoLayerDetailBefore(panelRect, detail, videoWallId);
    KaitoLayerLayoutReq.General general = new KaitoLayerLayoutReq.General();
    general.setZorder(detail.getGeneral().getZorder());
    general.setName(detail.getGeneral().getName());
    general.setLayerId(panelRect.getPanelId());
    layer.setGeneral(general);
    layer.setAudioStatus(detail.getAudioStatus());
    layer.setSource(detail.getSource());
    layer.setWindow(detail.getWindow());
  }

  @BeforeMapping
  public void toKvmSlotBefore(@NotNull KaitoDeviceDetail.Slot kaitoSlot,
                              @MappingTarget KvmSlot kvmSlot,
                              @Context String masterId) {
    kvmSlot.setMasterId(masterId);
    kvmSlot.setName(String.format("slot.%s", kaitoSlot.getSlotId()));
    kvmSlot.setDeviceModel(DeviceType.KAITO02_SLOT.getDeviceTypeId());
  }
}
