package com.mediacomm.entity.vo;

import com.mediacomm.entity.dao.EnvDevice;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class EnvDeviceVo extends EnvDevice {
  private String modelName;
  private String deviceType;

  public EnvDeviceVo(EnvDevice envDevice) {
    setId(envDevice.getId());
    setName(envDevice.getName());
    setDeviceIp(envDevice.getDeviceIp());
    setDeviceModel(envDevice.getDeviceModel());
    setHardcode(envDevice.getHardcode());
    setVersion(envDevice.getVersion());
    setCollectorProperties(envDevice.getCollectorProperties());
    setProperties(envDevice.getProperties());
  }
}
