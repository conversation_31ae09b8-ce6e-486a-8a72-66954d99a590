package com.mediacomm.protocol;

import lombok.Getter;

/**
 * 云台控制.

 * <AUTHOR>
 */
public interface PtzCommand<T> {

  /**
   * 云台控制指令.
   */
  enum Action {
    START(0),     // 开始移动
    STOP(1);      // 停止移动

    @Getter
    private int action;

    Action(int action) {
      this.action = action;
    }
  }

  /**
   * 云台控制指令.
   */
  enum Command {
    LEFT("LEFT"),
    RIGHT("RIGHT"),
    UP("UP"),
    DOWN("DOWN"),
    ZOOM_IN("ZOOM_IN"),         // 焦距变大
    ZOOM_OUT("ZOOM_OUT"),       // 焦距变小
    LEFT_UP("LEFT_UP"),
    LEFT_DOWN("LEFT_DOWN"),
    RIGHT_UP("RIGHT_UP"),
    RIGHT_DOWN("RIGHT_DOWN"),
    GOTO_PRESET("GOTO_PRESET");  // 到预置点

    @Getter
    private String command;

    Command(String command) {
      this.command = command;
    }
  }

  /**
   * 云台左转.
   *
   * @return 是否成功
   */
  boolean execLeft(T t) throws Exception;

  /**
   * 云台右转.
   *
   * @return 是否成功
   */
  boolean execRight(T t) throws Exception;

  /**
   * 云台上转.
   *
   * @return 是否成功
   */
  boolean execUp(T t) throws Exception;

  /**
   * 云台下转.
   *
   * @return 是否成功
   */
  boolean execDown(T t) throws Exception;

  /**
   * 云台左上转.
   *
   * @return 是否成功
   */
  boolean execLeftUp(T t) throws Exception;

  /**
   * 云台左下转.
   *
   * @return 是否成功
   */
  boolean execLeftDown(T t) throws Exception;

  /**
   * 云台右上转.
   *
   * @return 是否成功
   */
  boolean execRightUp(T t) throws Exception;

  /**
   * 云台右下转.
   *
   * @return 是否成功
   */
  boolean execRightDown(T t) throws Exception;

  /**
   * 云台停止转动.
   *
   * @return 是否成功
   */
  boolean execStopMove(T t) throws Exception;

  /**
   * 焦距变大.
   *
   * @return 是否成功
   */
  boolean execZoomIn(T t) throws Exception;

  /**
   * 焦距变小.
   *
   * @return 是否成功
   */
  boolean execZoomOut(T t) throws Exception;

  /**
   * 调用预置位.
   *
   * @return 是否成功
   */
  boolean execLoadPreset(T t, int preset) throws Exception;

  /**
   * 保存预置位.
   *
   * @return 是否成功
   */
  boolean execSavePreset(T t, int preset) throws Exception;

}
