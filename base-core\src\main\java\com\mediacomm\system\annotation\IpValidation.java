package com.mediacomm.system.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Ip地址校验.
 * 结合@Validated使用.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Constraint(validatedBy = IpValidator.class)
public @interface IpValidation {
  /**
   * 校验失败的提示信息.
   *
   * @return .
   */
  String message() default "Incorrect ip address format.";

  /**
   * 分组校验.
   *
   * @return .
   */
  Class<?>[] groups() default {};

  /**
   * .
   *
   * @return .
   */
  Class<? extends Payload>[] payload() default {};
}
