package com.mediacomm.system.variable;

/**
 * RabbitMQ的路由的后缀.
 */
public class RoutingOperation {
  public static final String ADD = "add";
  public static final String DELETE = "delete";
  public static final String REBOOT = "reboot";
  public static final String EXT_REFRESH = "ext.refresh";
  public static final String CONFIG_REFRESH = "config.refresh";
  public static final String SNAPSHOT_TX_GET = "snapshot.tx.get";
  public static final String VW_SCENE_GET = "vw.scene.get";
  public static final String VW_PANELS_GET = "vw.panels.get";
  public static final String VW_PANEL_OPEN = "vw.panel.open";
  public static final String VW_PANEL_CLOSE = "vw.panel.close";
  public static final String VW_PANELS_CLOSE = "vw.panels.close";
  public static final String VW_PANELS_OPEN = "vw.panels.open";
  public static final String VW_PANEL_MOVE = "vw.panel.move";
  public static final String VW_PANEL_LAYER_SWAP = "vw.panel.layer.swap";
  public static final String VW_UPDATE = "vw.update";
  public static final String VW_BOTTOM_IMAGE_SET = "vw.bottom.image.set";
  public static final String VW_BOTTOM_IMAGE_ENABLE = "vw.bottom.image.enable";
  public static final String ASSET_UPDATE = "asset.update";
  public static final String ASSET_VERSION_GET = "asset.version.get";
  public static final String SEAT_PANELS_GET = "seat.panels.get";
  public static final String SEAT_PANEL_OPEN = "seat.panel.open";
  public static final String SEAT_PANELS_OPEN = "seat.panels.open";
  public static final String SEAT_PANEL_CLOSE = "seat.panel.close";
  public static final String SEAT_PANELS_CLOSE = "seat.panels.close";
  public static final String TX_RX_CONNECT = "txrx.connect";
  public static final String TX_ENCODER_CONNECT = "tx.encoder.connect";
  public static final String SNAPSHOT_GET = "snapshot.get";
  public static final String CLIENT_HEARTBEAT = "client.heartbeat";
  public static final String BANNER_GET = "banner.get";
  public static final String BANNER_ENABLE = "banner.enable";
  public static final String BANNER_SET = "banner.set";
  public static final String BANNER_BG_COLOR_ENABLE = "banner.bg.color.enable";
  public static final String VS_OPEN = "vs.open";
  public static final String EXECUTE_COMMAND = "command.execute";
  public static final String SCAN = "scan";
  public static final String SET_IP = "set.ip";
  public static final String REGISTER = "register";
  public static final String UNREGISTER = "unregister";
  public static final String START_UPGRADE_PACKAGE = "package.upgrade.start";
  public static final String CHECK_UPGRADE_PACKAGE = "package.upgrade.check";
  public static final String STOP_UPGRADE_PACKAGE = "package.upgrade.stop";
  public static final String DELETE_UPGRADE_PACKAGE = "package.upgrade.delete";
  public static final String GET_UPGRADE_LIST = "package.upgrade.list";
}
