package com.mediacomm.aircon;

import com.mediacomm.aircon.controller.AirconCmdServer;
import com.mediacomm.entity.Result;
import com.mediacomm.system.base.kvm.KvmRunner;
import com.mediacomm.system.variable.MessageType;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingKey;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.boot.CommandLineRunner;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * AirconRunner.
 */
@Slf4j
@Component
public class AirconRunner extends KvmRunner implements CommandLineRunner {
  @Resource
  private AirconCmdServer airconCmdServer;

  /**
   * 初始化.
   */
  @Override
  public void run(String... args) {
    log.info("Aircon service running!");
  }

  /**
   * 指定消费者的线程数量,一个线程会打开一个Channel，
   * 一个队列上的消息只会被消费一次（不考虑消息重新入队列的情况）,下面的表示至少开启5个线程，最多10个。
   * 线程的数目需要根据你的任务来决定，如果是计算密集型，线程的数目就应该少一些.
   *
   * @param msg        负载信息
   * @param headers    头部信息
   * @param routingKey 路由信息
   */
  @RabbitListener(queues = MessageType.AIRCON_KVM, concurrency = "5-10")
  public String receiver(@Payload String msg,
                         @Headers Map<String, Object> headers,
                         @Header(value = AmqpHeaders.RECEIVED_ROUTING_KEY) String routingKey) {
    return switch (routingKey) {
      case RoutingKey.AIRCON_KVM_ADD -> "CAESAR_KVM_ADD";
      case RoutingKey.AIRCON_KVM_DELETE -> "CAESAR_KVM_DELETE";
      case RoutingKey.AIRCON_KVM_REFRESH_EXTEND_DEVICE -> airconCmdServer.refreshExtendDevice(msg);
      case RoutingKey.AIRCON_KVM_REFRESH_CONFIG -> airconCmdServer.refreshConfig(msg);
      case RoutingKey.AIRCON_KVM_GET_VW_PANELS -> airconCmdServer.getVwPanels(msg);
      case RoutingKey.AIRCON_KVM_OPEN_VW_PANEL -> airconCmdServer.openVwPanel(msg);
      case RoutingKey.AIRCON_KVM_CLOSE_VW_PANEL -> airconCmdServer.closeVwPanel(msg);
      case RoutingKey.AIRCON_KVM_CLOSE_ALL_VW_PANEL -> airconCmdServer.closeAllVwPanel(msg);
      case RoutingKey.AIRCON_KVM_OPEN_VW_PANELS -> airconCmdServer.openVwPanels(msg);
      case RoutingKey.AIRCON_KVM_MOVE_VW_PANEL -> airconCmdServer.moveVwPanels(msg);
      case RoutingKey.AIRCON_KVM_SWAP_VW_PANEL_LAYER -> airconCmdServer.swapVwPanelLayer(msg);
      case RoutingKey.AIRCON_KVM_GET_SEAT_PANELS -> airconCmdServer.getSeatPanels(msg);
      case RoutingKey.AIRCON_KVM_CLOSE_ALL_SEAT_PANEL -> airconCmdServer.closeAllSeatPanel(msg);
      case RoutingKey.AIRCON_KVM_CLOSE_SEAT_PANEL -> airconCmdServer.closeSeatPanel(msg);
      case RoutingKey.AIRCON_KVM_OPEN_SEAT_PANELS -> airconCmdServer.openSeatPanels(msg);
      case RoutingKey.AIRCON_KVM_SEAT_OPEN_TX -> airconCmdServer.seatOpenTx(msg);
      case RoutingKey.AIRCON_KVM_GET_TX_SNAPSHOT -> airconCmdServer.getTxSnapshot(msg);
      case RoutingKey.AIRCON_CONNECT_TX_ENCODER -> airconCmdServer.connectToEncoder(msg);
      case RoutingKey.AIRCON_KVM_GET_SNAPSHOT -> airconCmdServer.getSnapshot(msg);
      default -> Result.failureStr("no such command", ResponseCode.EX_NOTFOUND_404);
    };
  }
}
