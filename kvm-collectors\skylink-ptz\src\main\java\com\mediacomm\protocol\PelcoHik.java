package com.mediacomm.protocol;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.mediacomm.domain.Operate;
import com.mediacomm.domain.OperateParam;
import com.mediacomm.domain.Ptz;
import com.mediacomm.hikvision.api.video.PresetsFunctionApi;
import com.mediacomm.hikvision.api.video.PtzsFunctionApi;
import com.mediacomm.hikvision.entity.resource.camera.SearchCameraResponse;
import com.mediacomm.hikvision.entity.video.presets.AdditionRequest;
import com.mediacomm.hikvision.entity.video.ptzs.ControllingRequest;
import com.mediacomm.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 海康安防监控平台云台指令.
 *
 * <AUTHOR>
 */
@Slf4j
public class PelcoHik implements Protocol, PtzCommand<Ptz> {

  private boolean sendCmd(Ptz ptz, String operate, OperateParam param) {
    try {
      return switch (operate) {
        case "left-up" -> execLeftUp(ptz);
        case "left-down" -> execLeftDown(ptz);
        case "right-up" -> execRightUp(ptz);
        case "right-down" -> execRightDown(ptz);
        case "up" -> execUp(ptz);
        case "down" -> execDown(ptz);
        case "left" -> execLeft(ptz);
        case "right" -> execRight(ptz);
        case "zoom-out" -> execZoomOut(ptz);
        case "zoom-in" -> execZoomIn(ptz);
        case "stop-zoom", "stop-move" -> execStopMove(ptz);
        case "load-preset" -> execLoadPreset(ptz, param.getPreset());
        case "save-preset" -> execSavePreset(ptz, param.getPreset());
        default -> false;
      };
    } catch (Exception ex) {
      log.error(ex.getMessage(), ex);
    }
    return false;
  }

  @Override
  public boolean doOperate(Ptz ptz, Operate opt) {
    return sendCmd(ptz, opt.getOperate(), opt.getParam());
  }

  @Override
  public boolean execLeft(Ptz ptz) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode(), Command.LEFT)), ptz);
  }

  @Override
  public boolean execRight(Ptz ptz) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode(), Command.RIGHT)), ptz);
  }

  @Override
  public boolean execUp(Ptz ptz) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode(), Command.UP)), ptz);
  }

  @Override
  public boolean execDown(Ptz ptz) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode(), Command.DOWN)), ptz);
  }

  @Override
  public boolean execLeftUp(Ptz ptz) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode(), Command.LEFT_UP)), ptz);
  }

  @Override
  public boolean execLeftDown(Ptz ptz) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode(), Command.LEFT_DOWN)), ptz);
  }

  @Override
  public boolean execRightUp(Ptz ptz) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode(), Command.RIGHT_UP)), ptz);
  }

  @Override
  public boolean execRightDown(Ptz ptz) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode(), Command.RIGHT_DOWN)), ptz);
  }

  @Override
  public boolean execStopMove(Ptz ptz) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode())), ptz);
  }

  @Override
  public boolean execZoomIn(Ptz ptz) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode(), Command.ZOOM_IN)), ptz);
  }

  @Override
  public boolean execZoomOut(Ptz ptz) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode(), Command.ZOOM_OUT)), ptz);
  }

  @Override
  public boolean execLoadPreset(Ptz ptz, int preset) throws Exception {
    return executeRes(PtzsFunctionApi.controlling(createArtemisConfig(ptz),
        createControllingRequest(ptz.getCameraIndexCode(), preset)), ptz);
  }

  @Override
  public boolean execSavePreset(Ptz ptz, int preset) throws Exception {
    return executeRes(
        PresetsFunctionApi.addition(createArtemisConfig(ptz), createAdditionRequest(ptz, preset)),
        ptz);
  }

  /**
   * 生成安防监控平台的连接参数.
   *
   * @param ptz 云台设备.
   * @return ArtemisConfig.
   */
  private ArtemisConfig createArtemisConfig(Ptz ptz) {
    ArtemisConfig config = new ArtemisConfig();
    config.setHost(String.format("%s:%d", ptz.getIp(), ptz.getPort()));
    config.setAppKey(ptz.getAppKey());
    config.setAppSecret(ptz.getAppSecret());
    return config;
  }

  /**
   * 生成云台转动指令请求.
   *
   * @param command 方向指令.
   * @return ControllingRequest.
   */
  private ControllingRequest createControllingRequest(String cameraIndexCode, Command command) {
    ControllingRequest request = new ControllingRequest();
    request.setSpeed(40);
    request.setCameraIndexCode(cameraIndexCode);
    request.setAction(Action.START.getAction());
    request.setCommand(command.getCommand());
    return request;
  }

  /**
   * 生成云台调用预置位指令请求.
   *
   * @param presetIndex 预置位.
   * @return ControllingRequest.
   */
  private ControllingRequest createControllingRequest(String cameraIndexCode, int presetIndex) {
    ControllingRequest request = new ControllingRequest();
    request.setSpeed(40);
    request.setCameraIndexCode(cameraIndexCode);
    request.setPresetIndex(presetIndex);
    request.setAction(Action.START.getAction());
    request.setCommand(Command.GOTO_PRESET.getCommand());
    return request;
  }

  /**
   * 生成停止转动指令请求.
   *
   * @return ControllingRequest.
   */
  private ControllingRequest createControllingRequest(String cameraIndexCode) {
    ControllingRequest request = new ControllingRequest();
    request.setCameraIndexCode(cameraIndexCode);
    request.setAction(Action.STOP.getAction());
    request.setCommand(Command.DOWN.getCommand());
    return request;
  }

  /**
   * 生成保存预置位的请求.
   *
   * @param ptz    云台设备.
   * @param preset 预置位.
   * @return AdditionRequest.
   */
  private AdditionRequest createAdditionRequest(Ptz ptz, int preset) {
    AdditionRequest request = new AdditionRequest();
    request.setCameraIndexCode(ptz.getCameraIndexCode());
    request.setPresetName(String.valueOf(preset));
    request.setPresetIndex(preset);
    return request;
  }

  /**
   * 解析执行结果.
   *
   * @param res 接口返回值.
   * @param ptz 云台设备.
   * @return 请求成功时返回true.
   */
  private boolean executeRes(String res, Ptz ptz) {
    SearchCameraResponse response = JsonUtils.decode(res, SearchCameraResponse.class);
    if (response.getMsg().equals("success")) {
      return true;
    }
    log.error(
        "Device " + ptz.getId() + "(" + ptz.getCameraIndexCode() + ")" + " command execute failed!"
            + " -> " + res);
    return false;
  }
}
