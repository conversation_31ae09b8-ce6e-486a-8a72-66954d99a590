version: "3.8"

services:
  rabbitmq:
    image: rabbitmq:${RABBITMQ_VERSION}
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=123456
      - RABBITMQ_ERLANG_COOKIE=MEDIACOMM_SKYLINK_SECRET_COOKIE_STRING_FOR_RABBITMQ
    ports:
      - "${RABBITMQ_HOST_PORT}:${RABBITMQ_HOST_PORT}" #JMS Port
      - "${RABBITMQ_MAG_PORT}:${RABBITMQ_MAG_PORT}" #Management Port
      - "${RABBITMQ_MQTT_PORT}:${RABBITMQ_MQTT_PORT}" #MQTT Port
    command: "/bin/bash -c \"rabbitmq-plugins enable --offline rabbitmq_mqtt; rabbitmq-server\""
    container_name: rabbitmq
    hostname: my-rabbit
    restart: always

  mysql:
    container_name: mysql
    image: skylink/mysql:${MYSQL_VERSION}
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      TZ: Asia/Shanghai
      LANG: C.UTF_8
    #volumes:
      #- ${MYSQL_DATA_DIR}:/var/lib/mysql #数据文件挂载
      #- ${MYSQL_CONF_DIR}:/etc/mysql/conf.d #配置文件挂载
      #- ${MYSQL_CONF_MY_FILE}:/etc/mysql/my.cnf #配置文件挂载
      #- ${MYSQL_CONF_MYSQL_FILE}:/etc/mysql/mysql.cnf #配置文件挂载
      #- ${MYSQL_LOG_DIR}:/var/log/mysql #日志文件挂载
    ports:
      - "${MYSQL_HOST_PORT}:${MYSQL_HOST_PORT}"
    healthcheck:
       test: [ "CMD", "mysqladmin" ,"ping", "-h", "localhost" ]
       interval: 5s
       timeout: 10s
       retries: 10
    restart: always

  kingbase:
    container_name: kingbase
    image: skylink/kingbase:${KINGBASE_VERSION}
    environment:
      - "DB_USER=${KINGBASE_USER_NAME}"
      - "DB_PASSWORD=${KINGBASE_SKYLINK_PASSWORD}"
    ports:
      - "${KINGBASE_HOST_PORT}:${KINGBASE_HOST_PORT}"
    restart: always

  redis:
    image: redis/redis-stack-server:${REDIS_VERSION}
    container_name: redis
    environment:
      TZ: Asia/Shanghai
    restart: always
    #volumes:
      #- ${REDIS_DATA_DIR}:/data #数据文件挂载
      #- ${REDIS_CONF_FILE}:/etc/redis/redis.conf #配置文件挂载
    ports:
      - "${REDIS_HOST_PORT}:${REDIS_HOST_PORT}"

  #nacos:
    #image: nacos/nacos-server:${NACOS_VERSION}
    #container_name: nacos
    #environment:
      #- "TZ=Asia/Shanghai"
      #- "PREFER_HOST_MODE=${PREFER_HOST_MODE}"
      #- "MODE=${MODE}"
      #- "SPRING_DATASOURCE_PLATFORM=${SPRING_DATASOURCE_PLATFORM}"
      #- "MYSQL_SERVICE_HOST=${MYSQL_SERVICE_HOST}"
      #- "MYSQL_SERVICE_DB_NAME=${MYSQL_SERVICE_DB_NAME}"
      #- "MYSQL_SERVICE_PORT=${MYSQL_HOST_PORT}"
      #- "MYSQL_SERVICE_USER=${MYSQL_USER_NAME}"
      #- "MYSQL_SERVICE_PASSWORD=${MYSQL_ROOT_PASSWORD}"
#    volumes:
#      - ./standalone-logs/:/home/<USER>/logs
    #ports:
      #- "${NACOS_SERVICE_PORT}:${NACOS_SERVICE_PORT}"
      #- "9848:9848"
    #depends_on:
      #mysql:
        #condition: service_healthy
    #restart: always

volumes:
  mysql-data:
