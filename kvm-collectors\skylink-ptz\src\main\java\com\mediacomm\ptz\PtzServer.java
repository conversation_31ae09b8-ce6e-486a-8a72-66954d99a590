package com.mediacomm.ptz;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.mediacomm.domain.Operate;
import com.mediacomm.domain.Ptz;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.protocol.ProtocolContext;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.variable.MessageType;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.util.InspectValue;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.TransferFormat;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Slf4j
@Component
public class PtzServer {
  @Resource
  KvmAssetService assetService;

  private Map<String, ProtocolContext> protocolMap = new HashMap<>();

  /**
   * .
   *
   * @param msg        .
   * @param headers    .
   * @param routingKey .
   * @return .
   */
  @RabbitListener(queues = MessageType.PTZ_SERVER, concurrency = "5-10")
  public String receiver(@Payload String msg,
                         @Headers Map<String, Object> headers,
                         @Header(value = AmqpHeaders.RECEIVED_ROUTING_KEY) String routingKey) {
    return switch (routingKey) {
      case RoutingKey.PTZ_CONTROL -> doCmd(msg);
      default -> Result.failureStr("Ptz no such command", ResponseCode.EX_NOTFOUND_404);
    };
  }

  private String doCmd(String msg) {
    MqRequest<Operate> mqRequest = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (mqRequest == null) {
      return Result.failureStr("Request parameter error.", ResponseCode.EX_FAILURE_400);
    }
    Operate opt = mqRequest.getBody();
    Ptz ptz = findIpc(opt.getTxId());
    if (ptz != null && !Strings.isNullOrEmpty(ptz.getProtocol())
        && InspectValue.isDefinedProtocol(ptz.getProtocol())) {
      if (!protocolMap.containsKey(ptz.getProtocol())) {
        protocolMap.put(ptz.getProtocol(), new ProtocolContext(ptz.getProtocol()));
      }
      return JsonUtils.encode(Result.ok(protocolMap.get(ptz.getProtocol()).doOperate(ptz, opt)));
    }
    return JsonUtils.encode(Result.failure("no found " + opt.getTxId() + " ptz!",
        ResponseCode.EX_NOTFOUND_404));
  }

  private synchronized Ptz findIpc(String id) {
    KvmAsset asset = assetService.getById(id);
    return TransferFormat.assetToPtz(asset);
  }
}
