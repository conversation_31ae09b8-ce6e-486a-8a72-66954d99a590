package com.mediacomm.util;

import com.google.common.collect.Maps;
import com.mediacomm.handler.websocket.WebSocketHandler;
import com.mediacomm.system.variable.ResUrlDef;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Component
public class WebSocketHandlerSendNoticeUtils {
  @Autowired
  private WebSocketHandler handler;

  /**
   * 发送banner变更通知.
   *
   * @param vwId 电视墙ID.
   */
  public void sendBannerChangeNotice(Integer vwId) {
    Map<String, Object> wsMsg = Maps.newHashMap();
    wsMsg.put("vwId", vwId);
    handler.sendMessage(ResUrlDef.WS_TERMINAL_NOTICE,
            BuildWsEventNoticeBodyUtils
                    .buildWsEventNoticeBody(WebSocketTopic.EVENT_CHANGE_VW_BANNER, wsMsg));
  }

  /**
   * 发送电视墙变更通知.
   *
   * @param eventType 事件类型.
   * @param vwId 电视墙ID.
   */
  public void sendVideoWallChangeNotice(String eventType, Integer vwId) {
    Map<String, Integer> msg = Maps.newHashMap();
    msg.put("vwId", vwId);
    handler.sendMessage(ResUrlDef.WS_TERMINAL_NOTICE,
            BuildWsEventNoticeBodyUtils.buildWsEventNoticeBody(eventType, msg));
  }

  /**
   * 发送场景变更通知.
   *
   * @param eventType 事件类型.
   * @param sceneId 场景ID.
   */
  public void sendSceneChangeNotice(String eventType, Integer sceneId) {
    Map<String, Integer> msg = Maps.newHashMap();
    msg.put("sceneId", sceneId);
    handler.sendMessage(ResUrlDef.WS_TERMINAL_NOTICE,
            BuildWsEventNoticeBodyUtils.buildWsEventNoticeBody(eventType, msg));
  }
}
