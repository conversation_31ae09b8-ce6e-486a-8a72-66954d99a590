package com.mediacomm.entity.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.variable.sysenum.LayerStatusType;
import com.mediacomm.system.variable.sysenum.LayoutType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PanelRect.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PanelRect implements Serializable {
  @Builder.Default
  private int panelId = -1;
  private int seq;
  private int xpos;
  private int ypos;
  private int width;
  private int height;
  private List<LayerData> childPanels;
  private LayoutType layoutType;
  private LayerStatusType status = LayerStatusType.NORMAL; // 在凯撒的全光屏中表示承接这个窗口解码的设备的状态

  /**
   * 窗口内置一个铺满的图层.
   *
   * @param asset 图层对应的外设.
   * @param ctrlMode 图层的控制模式.
   */
  public void addFullScreenLayer(KvmAssetVo asset, int ctrlMode) {
    if (asset != null) {
      LayerData layer = new LayerData();
      layer.setSeq(0);
      layer.setCtrlMode(ctrlMode);
      layer.setDeviceType(asset.getDeviceType());
      layer.setVideoSrcName(asset.getName());
      layer.setVideoSrcId(asset.getAssetId());
      childPanels = Collections.singletonList(layer);
    }
    layoutType = LayoutType.FULL_SCREEN;
  }

  /**
   * 添加四屏图层.
   */
  public void addFourScreenLayers(Collection<KvmAssetVo> assets) {
    childPanels = new ArrayList<>(4);
    int seq = 0;
    for (KvmAssetVo asset : assets) {
      LayerData layer = LayerData.builder()
              .seq(seq)
              .videoSrcId(asset.getAssetId())
              .videoSrcName(asset.getName())
              .deviceType(asset.getDeviceType())
              .build();
      seq += 1;
      childPanels.add(layer);
    }
    layoutType = LayoutType.FOUR_SCREEN;
  }

  /**
   * 用于FULL_SCREEN布局下获取视频源id.
   */
  public String getVideoSrcId() {
    if (childPanels != null && !childPanels.isEmpty()) {
      return childPanels.getFirst().getVideoSrcId();
    }
    return "";
  }

  /**
   * 用于FULL_SCREEN布局下获取音频控制.
   */
  public boolean isEnableAudio() {
    if (childPanels != null && !childPanels.isEmpty()) {
      return childPanels.getFirst().isEnableAudio();
    }
    return false;
  }

  /**
   * 获取指定序号的图层.
   */
  public LayerData getLayerData(int seq) {
    if (childPanels != null && !childPanels.isEmpty()) {
      for (LayerData layer : childPanels) {
        if (layer.getSeq() == seq) {
          return layer;
        }
      }
    }
    return null;
  }
}
