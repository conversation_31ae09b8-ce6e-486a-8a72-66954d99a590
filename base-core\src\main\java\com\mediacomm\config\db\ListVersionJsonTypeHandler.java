package com.mediacomm.config.db;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.entity.dao.Version;
import com.mediacomm.util.JsonUtils;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import java.util.List;

/**
 * .
 */
@MappedTypes({List.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class ListVersionJsonTypeHandler extends AbstractJsonTypeHandler<List<Version>> {
  @Override
  protected List<Version> parse(String json) {
    return JsonUtils.decode(json, new TypeReference<>() {});
  }

  @Override
  protected String toJson(List<Version> obj) {
    return JsonUtils.encode(obj);
  }
}
