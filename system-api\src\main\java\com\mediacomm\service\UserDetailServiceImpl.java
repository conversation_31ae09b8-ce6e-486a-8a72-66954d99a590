package com.mediacomm.service;

import com.mediacomm.entity.dao.Permission;
import com.mediacomm.entity.dao.Role;
import com.mediacomm.entity.vo.AccountVo;
import com.mediacomm.system.service.AccountService;
import com.mediacomm.system.service.RoleService;
import java.util.ArrayList;
import java.util.Collection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class UserDetailServiceImpl implements UserDetailsService {
  @Autowired
  private AccountService accountService;
  @Autowired
  private RoleService roleService;

  @Override
  public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
    AccountVo account = accountService.oneByName(username);
    Collection<GrantedAuthority> grantedAuthorities = new ArrayList<>();
    if (account != null) {
      Collection<Role> roles = roleService.allByAccountId(account.getAccountId());
      Collection<Permission> permissions = new ArrayList<>();
      for (Role role : roles) {
        if (role.getPermissions() != null && role.getPermissions().size() > 0) {
          permissions.addAll(role.getPermissions());
        }
      }
      for (Permission permission : permissions) {
        GrantedAuthority grantedAuthority = new SimpleGrantedAuthority(permission.getName());
        grantedAuthorities.add(grantedAuthority);
      }
      return new User(account.getAccountName(), account.getAccountPassword(), grantedAuthorities);
    }
    throw new UsernameNotFoundException(username + " not exist!");
  }
}
