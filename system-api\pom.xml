<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.mediacomm</groupId>
    <artifactId>skylink-server</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>

  <artifactId>system-api</artifactId>

  <properties>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <!-- flyway初始化数据 -->
    <dependency>
      <groupId>org.flywaydb</groupId>
      <artifactId>flyway-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.flywaydb</groupId>
      <artifactId>flyway-mysql</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-log4j12</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- JWT 相关 -->
    <dependency>
      <groupId>com.nimbusds</groupId>
      <artifactId>nimbus-jose-jwt</artifactId>
      <version>${jwt.version}</version>
    </dependency>
    <dependency>
      <groupId>com.mediacomm</groupId>
      <artifactId>base-core</artifactId>
      <version>${artifact.version}</version>
    </dependency>
    <dependency>
      <groupId>com.mediacomm</groupId>
      <artifactId>skylink-caesar</artifactId>
      <version>${artifact.version}</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.mediacomm</groupId>
          <artifactId>base-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.mediacomm</groupId>
      <artifactId>skylink-aircon</artifactId>
      <version>${artifact.version}</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.mediacomm</groupId>
          <artifactId>base-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.mediacomm</groupId>
      <artifactId>system-monitor</artifactId>
      <version>${artifact.version}</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.mediacomm</groupId>
          <artifactId>base-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.mediacomm</groupId>
      <artifactId>skylink-ptz</artifactId>
      <version>${artifact.version}</version>
    </dependency>
    <dependency>
      <groupId>com.mediacomm</groupId>
      <artifactId>skylink-kaito</artifactId>
      <version>${artifact.version}</version>
    </dependency>
    <dependency>
      <groupId>com.mediacomm</groupId>
      <artifactId>skylink-tsingli</artifactId>
      <version>${artifact.version}</version>
    </dependency>
    <dependency>
      <groupId>com.mediacomm</groupId>
      <artifactId>skylink-exchange</artifactId>
      <version>${artifact.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mediacomm</groupId>
      <artifactId>skylink-switcher</artifactId>
      <version>${artifact.version}</version>
    </dependency>
    <!-- protobuf -->
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>${protcol-buffer.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java-util</artifactId>
      <version>${protcol-buffer.version}</version>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <!-- docker插件 微服务多模块使用-->
      <!--<plugin>
        <groupId>com.spotify</groupId>
        <artifactId>dockerfile-maven-plugin</artifactId>
        <version>${dockerfile-maven.version}</version>
        <configuration>
          <repository>mediacomm/skylink-server</repository>
          <tag>${project.version}</tag>
          &lt;!&ndash; 指定了传递给Dockerfile的参数 &ndash;&gt;
          <buildArgs>
            <JAR_FILE_PATH>target/</JAR_FILE_PATH>
            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
            <VERSION>${artifact.version}</VERSION>
          </buildArgs>
        </configuration>
      </plugin>-->
      <!-- 原生编译插件 -->
      <!--<plugin>
        <groupId>org.graalvm.buildtools</groupId>
        <artifactId>native-maven-plugin</artifactId>
        <configuration>
          <imageName>${project.artifactId}</imageName>
          <mainClass>com.mediacomm.SkyLinkServerApp</mainClass>
        </configuration>
        <executions>
          <execution>
            <id>build-image</id>
            <goals>
              <goal>compile</goal>
            </goals>
            <phase>package</phase>
          </execution>
        </executions>
      </plugin>-->
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
            </exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>${maven.jacoco.version}</version>
        <executions>
          <!-- attached to Maven test phase -->
          <execution>
            <id>report-aggregate</id>
            <phase>verify</phase>
            <goals>
              <goal>report-aggregate</goal>
            </goals>
            <configuration>
              <!--包含当前module的聚合报告-->
              <includeCurrentProject>true</includeCurrentProject>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
