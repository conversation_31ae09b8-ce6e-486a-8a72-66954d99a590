package com.mediacomm.entity.message;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.Data;

/**
 * SeatPanels.
 */
@Data
public class SeatPanels {
  private boolean linkStatus;
  private boolean isPush;
  private int height;
  private int width;
  private List<LayerData> panels = new ArrayList<>();
  private LayoutData layoutData;

  /**
   * 初始化布局为全屏.
   */
  public SeatPanels() {
    LayoutRect rect = new LayoutRect(1, 0, 0, 1920, 1080);
    layoutData = new LayoutData(1920, 1080, Collections.singletonList(rect));
  }
}
