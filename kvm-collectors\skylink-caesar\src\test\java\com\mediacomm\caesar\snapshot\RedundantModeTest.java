package com.mediacomm.caesar.snapshot;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.caesar.controller.CaesarCmdServer;
import com.mediacomm.caesar.domain.RedundantModeEnum;
import com.mediacomm.caesar.preview.PreviewManager;
import com.mediacomm.caesar.util.InspectCaesarUtil;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.entity.message.reqeust.body.SnapshotStatus;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.SnapshotType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.SkyLinkStringUtil;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.Getter;
import org.junit.Assert;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runners.MethodSorters;

/**
 * .
 */
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class RedundantModeTest {

  private final CaesarCmdServerMock caesarCmdServerMock = new CaesarCmdServerMock();
  private final CaesarCmdServer cmdServer = new CaesarCmdServer();
  private final RedisUtil redisUtil = mock(RedisUtil.class);
  private final PreviewManager previewManager = mock(PreviewManager.class);
  private MqRequest<ObjectIds> requestToTxSnapshot;

  @Before
  public void init() throws IllegalAccessException {
    ObjectIds ids = new ObjectIds();
    ids.getActiveIds().addAll(caesarCmdServerMock.idTxMap.keySet());
    ids.getIds().addAll(caesarCmdServerMock.idTxMap.keySet());
    ids.setImportant(true);
    ids.setRequestKey(SkyLinkStringUtil.uuid());
    requestToTxSnapshot = new MqRequest<>();
    requestToTxSnapshot.setBody(ids);
    requestToTxSnapshot.setMasterId(caesarCmdServerMock.getMaster().getMasterId());
    FieldUtil.setPropertyValue(cmdServer, "redisUtil", redisUtil);
    FieldUtil.setPropertyValue(cmdServer, "kvmAssetService", caesarCmdServerMock.getKvmAssetService());
    FieldUtil.setPropertyValue(cmdServer, "kvmMasterService", caesarCmdServerMock.getKvmMasterService());
    FieldUtil.setPropertyValue(cmdServer, "previewManager", previewManager);
  }

  @Test
  public void testGetTxSnapshotFromBackMaster() {
    when(redisUtil.getStr(RedisUtil
            .redisKey(RedisKey.REDUNDANT_STATUS, requestToTxSnapshot.getMasterId())))
            .thenReturn(Optional.of(String.valueOf(RedundantModeEnum.BACKUP)));

    String res = cmdServer.getTxSnapshot(JsonUtils.encode(requestToTxSnapshot));
    Result<List<SnapshotStatus>> result = JsonUtils.decode(res, new TypeReference<>() {
    });
    Assert.assertNotNull(result);
    Assert.assertEquals(200, (int) result.getCode());
    for (SnapshotStatus snapshotStatus : result.getResult()) {
      Assert.assertEquals(SnapshotType.SKYLINK, snapshotStatus.getType());
      Assert.assertEquals(caesarCmdServerMock.getMaster().getMasterId(), snapshotStatus.getMasterId());
      Assert.assertNotNull(caesarCmdServerMock.getIdTxMap().get(snapshotStatus.getId()));
      String relatedTxHardcode =
              snapshotStatus.getPath().substring(snapshotStatus.getPath().lastIndexOf("/") + 1,
                      snapshotStatus.getPath().lastIndexOf("."));
      KvmAsset relatedTx = caesarCmdServerMock.getKvmAssetService().oneByHardcode(relatedTxHardcode);
      Assert.assertNotNull(relatedTx);
      Assert.assertEquals(caesarCmdServerMock.getRelatedMaster().getMasterId(), relatedTx.getMasterId());
    }
  }

  /**
   * 冗余凯撒主机.
   */
  @Getter
  static class CaesarCmdServerMock {
    private final KvmMaster master;
    private final KvmMaster relatedMaster;
    private final Map<String, KvmAsset> idTxMap = new HashMap<>();
    private final Map<String, KvmAsset> idRelatedTxMap = new HashMap<>();
    private KvmAssetService kvmAssetService;
    private KvmMasterService kvmMasterService;

    public CaesarCmdServerMock() {
      master = new KvmMaster();
      master.setMasterId(SkyLinkStringUtil.uuid());
      master.setCollectorProperties(new ArrayList<>());
      relatedMaster = new KvmMaster();
      relatedMaster.setMasterId(SkyLinkStringUtil.uuid());
      relatedMaster.setCollectorProperties(new ArrayList<>());
      // collectorProperties
      master.getCollectorProperties().add(new Property("associateHost", relatedMaster.getMasterId()));
      relatedMaster.getCollectorProperties().add(new Property("associateHost", master.getMasterId()));
      for (int i = 0; i < 20; i++) {
        KvmAsset tx = new KvmAsset();
        tx.setAssetId(SkyLinkStringUtil.uuid());
        tx.setMasterId(master.getMasterId());
        tx.setDeviceId(i);
        tx.setDeviceModel(DeviceType.CAESAR_TX.getDeviceTypeId());
        tx.setHardcode(InspectCaesarUtil
                .getCaesarDeviceHardCode(master.getMasterId(), "hardcode." + i, i));
        KvmAsset relatedTx = new KvmAsset();
        relatedTx.setAssetId(SkyLinkStringUtil.uuid());
        relatedTx.setMasterId(relatedMaster.getMasterId());
        relatedTx.setDeviceId(i);
        relatedTx.setDeviceModel(DeviceType.CAESAR_TX.getDeviceTypeId());
        relatedTx.setHardcode(InspectCaesarUtil
                .getCaesarDeviceHardCode(relatedMaster.getMasterId(), "hardcode." + i, i));
        idTxMap.put(tx.getAssetId(), tx);
        idRelatedTxMap.put(relatedTx.getAssetId(), relatedTx);
        kvmMasterService = new KvmMasterServiceMock(master, relatedMaster);
        Collection<KvmAsset> kvmAssets = new ArrayList<>();
        kvmAssets.addAll(idTxMap.values());
        kvmAssets.addAll(idRelatedTxMap.values());
        kvmAssetService = new KvmAssetServiceMock(kvmAssets);
      }
    }
  }

  static class KvmMasterServiceMock extends KvmMasterService {
    Map<String, KvmMaster> masterMap = new HashMap<>();

    public KvmMasterServiceMock(KvmMaster... masters) {
      for (KvmMaster master : masters) {
        masterMap.put(master.getMasterId(), master);
      }
    }

    public KvmMaster getById(Serializable id) {
      return masterMap.get(id);
    }
  }

  static class KvmAssetServiceMock extends KvmAssetService {
    Map<String, KvmAsset> kvmAssetMap = new HashMap<>();

    public KvmAssetServiceMock(Collection<KvmAsset> assets) {
      for (KvmAsset asset : assets) {
        kvmAssetMap.put(asset.getAssetId(), asset);
      }
    }

    public KvmAsset getById(Serializable id) {
      return kvmAssetMap.get(id);
    }

    public KvmAssetVo oneByDeviceId(Integer deviceId, String masterId) {
      for (Map.Entry<String, KvmAsset> entry : kvmAssetMap.entrySet()) {
        KvmAsset asset = entry.getValue();
        if (asset.getDeviceId().equals(deviceId) && asset.getMasterId().equals(masterId)) {
          return new KvmAssetVo(asset);
        }
      }
      return null;
    }

    public KvmAssetVo oneByHardcode(String hardcode) {
      for (Map.Entry<String, KvmAsset> entry : kvmAssetMap.entrySet()) {
        KvmAsset asset = entry.getValue();
        if (asset.getHardcode().equals(hardcode)) {
          return new KvmAssetVo(asset);
        }
      }
      return null;
    }
  }
}
