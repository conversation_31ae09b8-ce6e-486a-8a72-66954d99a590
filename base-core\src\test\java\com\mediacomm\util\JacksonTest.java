package com.mediacomm.util;

import com.mediacomm.entity.DeviceInstance;
import com.mediacomm.entity.ServiceInstance;
import com.mediacomm.system.variable.sysenum.PositionType;
import com.mediacomm.system.variable.sysenum.ServiceName;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * .
 *
 * @author: WuZeJie
 */
public class JacksonTest {
  ServiceInstance instance;
  Set<DeviceInstance> collection = new HashSet<>();

  /**
   * .
   */
  @Before
  public void init() {
    List<String> function = new ArrayList<>();
    function.add("VIS");
    function.add("monitor");

    DeviceInstance de1 = new DeviceInstance();
    de1.setDeviceId("b7cbc986-020a-4372-9262-d0ead7478903");
    de1.setDeviceType("Caesar");
    de1.setFunction(function);

    DeviceInstance de2 = new DeviceInstance();
    de2.setDeviceId("7903d248-a607-4885-98c8-734cc55f17c7");
    de2.setDeviceType("Aircon");
    de2.setFunction(function);

    instance = ServiceInstance.createServiceInstance(ServiceName.KVM_SERVER, "***********", 8888);
    collection.add(de1);
    collection.add(de2);
    instance.setDevices(collection);
  }

  @Test
  public void testObjToMap() {
    /*String str = "{serviceId=Kvm, ip=***********, port=8888, operation=START, " +
            "devices=[{\"deviceId\":\"e77bfdb4-40b2-457a-9cfe-9cbbc90a49d9\"," +
            "\"deviceType\":\"Caesar\",\"function\":[\"VIS\",\"monitor\"]}," +
            "{\"deviceId\":\"a9dfb4a4-f96a-457d-a95c-e74dea1d5e2c\"," +
            "\"deviceType\":\"Aircon\",\"function\":[\"VIS\",\"monitor\"]}]}";*/
    Map<String, String> map = JsonUtils.objToMap(instance);
    Assert.assertNotNull(map.get("devices"));
  }

  @Test
  public void testEncodeCollection() {
    System.out.println(PositionType.SEAT_GROUP);
    String str = JsonUtils.encodeCollection(collection);
    Assert.assertEquals(str, "[{\"deviceId\":\"7903d248-a607-4885-98c8-734cc55f17c7\","
        + "\"deviceType\":\"Aircon\",\"function\":[\"VIS\",\"monitor\"]},"
        + "{\"deviceId\":\"b7cbc986-020a-4372-9262-d0ead7478903\","
        + "\"deviceType\":\"Caesar\",\"function\":[\"VIS\",\"monitor\"]}]");
  }

}
