package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mediacomm.config.db.ListVersionJsonTypeHandler;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.DeviceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 外设升级任务.
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "peripheral_upgrade_task", description = "外设升级任务")
public class PeripheralUpgradeTask extends SkyLinkDbEntity {
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  private Integer packageId; // 升级包ID
  @TableField(typeHandler = ListVersionJsonTypeHandler.class)
  private List<Version> packageVersion; // 升级包版本
  private String deviceId; // 设备ID
  private String deviceName; // 设备名称
  private DeviceType deviceType; // 设备类型
  private Integer status; // 状态：0-等待升级，1-升级中，2-升级成功，3-升级失败，4-升级取消，5-升级超时
  private Integer progress; // 升级进度(0-100)
  private String errorMessage; // 错误信息
  private Long startTime; // 开始时间
  private Long endTime; // 结束时间
}
