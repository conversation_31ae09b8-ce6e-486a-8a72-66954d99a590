package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.AlarmRepairDesc;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.AlarmRepairDescMapper;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class AlarmRepairDescService extends
    SkyLinkServiceImpl<AlarmRepairDescMapper, AlarmRepairDesc> {

  /**
   * 根据警报ID查询维修描述信息.
   *
   * @param alarmId 警报ID.
   * @return 维修描述信息.
   */
  public Collection<AlarmRepairDesc> allByAlarmId(Integer alarmId) {
      LambdaQueryWrapper<AlarmRepairDesc> wrapper = Wrappers.lambdaQuery(AlarmRepairDesc.class)
              .eq(AlarmRepairDesc::getAlarmId, alarmId);
      return list(wrapper);
  }

}
