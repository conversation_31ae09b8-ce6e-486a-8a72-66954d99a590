package com.mediacomm.system.service;

import cn.hutool.core.util.HashUtil;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Picture;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.PictureMapper;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.FileType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;

/**
 * .
 */
@Service
@Slf4j
public class PictureService extends SkyLinkServiceImpl<PictureMapper, Picture> {

  public Result<String> saveMultipartFile(MultipartFile multipartFile) throws IOException {
    if (multipartFile != null && !multipartFile.isEmpty()) {
      if (multipartFile.getContentType() == null) {
        return Result.failure("No content-type!", ResponseCode.EX_FAILURE_400);
      }
      String contentTypeStr = multipartFile.getContentType();
      if (contentTypeStr == null) {
        return Result.failure("No content-type!", ResponseCode.EX_FAILURE_400);
      }
      String[] contentType = contentTypeStr.split("/");
      if (contentType.length > 1) {
        String imageContentType = contentType[0];
        FileType type = Enum.valueOf(FileType.class, imageContentType.toUpperCase());
        if (type != FileType.IMAGE) {
          return Result.failure("No image type!", ResponseCode.EX_FAILURE_400);
        }
        long pictureId = Math.abs(HashUtil.cityHash64(multipartFile.getBytes()));
        Picture picture = getById(pictureId);
        if (picture == null) {
          picture = new Picture();
          picture.setId(pictureId);
          picture.setContent(multipartFile.getBytes());
          save(picture);
        }
        return Result.ok("success", String.valueOf(pictureId));
      }
    }
    return Result.failure("Empty multipartFile", ResponseCode.EX_FAILURE_400);
  }
}
