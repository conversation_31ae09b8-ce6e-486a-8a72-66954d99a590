package com.mediacomm.caesar.snapshot;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.google.common.collect.Maps;
import com.mediacomm.caesar.domain.CaesarConstants;
import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.domain.RedundantModeEnum;
import com.mediacomm.caesar.preview.PreviewManager;
import com.mediacomm.caesar.preview.device.PreviewDeviceOperator;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.preview.strategy.PreviewStrategy;
import com.mediacomm.caesar.preview.strategy.PreviewStrategyFactory;
import com.mediacomm.caesar.preview.strategy.impl.SimplePreviewStrategy;
import com.mediacomm.caesar.task.PollingPreviewRunner;
import com.mediacomm.caesar.task.PreviewThread;
import com.mediacomm.caesar.task.RealTimePreviewRunner;
import com.mediacomm.caesar.task.RefreshPreviewRunner;
import com.mediacomm.caesar.util.CaesarDbServiceUtil;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.task.SkyLinkTaskPool;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.junit.Assert;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runners.MethodSorters;

/**
 * .
 */
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class PreviewManagerTest {
  private final RedisUtil redisUtil = mock(RedisUtil.class);

  public ObjectIds makeRandomRequest(Map<String, KvmAsset> assets, boolean important, int cnt) {
    ObjectIds request = new ObjectIds();
    request.setImportant(important);
    request.setRequestKey(UUID.randomUUID().toString());
    request.getIds().addAll(
            PreviewTestHelper.getRandomElements(assets.entrySet(), cnt).stream()
                    .map((item) -> item.getValue().getAssetId()).toList());
    return request;
  }

  @Before
  public void before() {
    Path path = Paths.get("/tmp/snapshot/caesar/");
    if (!path.toFile().exists()) {
      return;
    }
    for (File file : path.toFile().listFiles()) {
      file.delete();
    }
  }

  @Test
  public void testCommonSingleThread() throws IOException, InterruptedException, IllegalAccessException {
    String masterId = UUID.randomUUID().toString();
    when(redisUtil.getStr(RedisKey.KC_SC_SS)).thenReturn(Optional.of(String.valueOf(0)));
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, masterId)))
            .thenReturn(Optional.of(String.valueOf(RedundantModeEnum.ALONE)));
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, masterId)))
            .thenReturn(Optional.of(String.valueOf(RedundantModeEnum.ALONE)));
    when(redisUtil.lock(RedisKey.KC_SC_SL, String.valueOf(System.currentTimeMillis()))).thenReturn(true);
    PreviewTestHelper.CaesarRunnerForeignMock runner =
            new PreviewTestHelper.CaesarRunnerForeignMock(2, 100, masterId);
    runner.setRedisUtil(redisUtil);
    DeviceDataGetter deviceDataGetter = new PreviewTestHelper.DeviceDataGetterImplMock(runner, masterId, runner.reviewAssoMap);
    SkyLinkTaskPool taskPool = new PreviewTestHelper.TaskRunnerMock();
    PreviewManager manager = mockVP6PreviewManager(runner, taskPool);

    Map<Integer, Collection<KvmPreviewAsso>> previewChannels =
            deviceDataGetter.getAllPreviewChannels(true);
    for (Map.Entry<Integer, Collection<KvmPreviewAsso>> entry : previewChannels.entrySet()) {
      for (KvmPreviewAsso kvmPreviewAsso : entry.getValue()) {
        kvmPreviewAsso.setUrl(PreviewTestHelper
                .getPreviewUrl(entry.getKey(), kvmPreviewAsso.getSeq()));
      }
    }
    changePreviewOpt(masterId, manager, deviceDataGetter, taskPool);

    int previewTxCnt = 10;
    int maxPollingChannelCnt = previewTxCnt * 2 * 2; // 全部都是4K，两次预览的TX合在一起轮询
    int pollingChannel =
            (int) (runner.videoWallMap.size() * CaesarConstants.PREVIEW_CHANNEL_LIMIT
                    * SimplePreviewStrategy.POLLING_CHANNEL_PERCENTAGE);
    int maxWaitTime = (int) (maxPollingChannelCnt * 1.0 / pollingChannel * 500);
    for (int iterator = 0; iterator < 4; iterator++) {
      ObjectIds request = makeRandomRequest(runner.idKvmAssetMap, true, previewTxCnt);
      manager.addPreview(masterId, request);
      // 等待足够时间获取第一张图
      int waitTime = maxWaitTime;
      while (waitTime > 0) {
        int sleepTime = Math.min(waitTime, SimplePreviewStrategy.PREVIEW_TIME_OUT_MILLI / 2);
        Thread.sleep(sleepTime);
        waitTime -= sleepTime;
        request.getIds().forEach((tx) -> manager.onGetSnapshot(masterId, tx));
      }
      for (int i = 0; i < 60; i++) {
        Thread.sleep(100);
        PreviewTestHelper
                .checkTxPreview(request.getIds(), true, (tx) -> deviceDataGetter.makeTxSavePath(tx),
                        maxWaitTime);
        request.getIds().forEach((tx) -> manager.onGetSnapshot(masterId, tx));
      }
    }
    System.out.println(String
            .format("%dms waiting time for %d preview tx and %d polling channel.", maxWaitTime,
                    previewTxCnt, pollingChannel));
    manager.refreshSnapshotPreviewStatus(Collections.emptySet());
  }

  @Test
  public void testCommonMultiThread() throws IllegalAccessException {
    String masterId = UUID.randomUUID().toString();
    when(redisUtil.getStr(RedisKey.KC_SC_SS)).thenReturn(Optional.of(String.valueOf(0)));
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, masterId)))
            .thenReturn(Optional.of(String.valueOf(RedundantModeEnum.ALONE)));
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, masterId)))
            .thenReturn(Optional.of(String.valueOf(RedundantModeEnum.ALONE)));
    when(redisUtil.lock(RedisKey.KC_SC_SL, String.valueOf(System.currentTimeMillis()))).thenReturn(true);
    PreviewTestHelper.CaesarRunnerForeignMock runner =
            new PreviewTestHelper.CaesarRunnerForeignMock(3, 100, masterId);
    DeviceDataGetter deviceDataGetter = new PreviewTestHelper.DeviceDataGetterImplMock(runner, masterId, runner.reviewAssoMap);
    runner.setRedisUtil(redisUtil);
    SkyLinkTaskPool taskPool = new PreviewTestHelper.TaskRunnerMock();
    PreviewManager manager = mockVP6PreviewManager(runner, taskPool);

    Map<Integer, Collection<KvmPreviewAsso>> previewChannels =
            deviceDataGetter.getAllPreviewChannels(true);
    for (Map.Entry<Integer, Collection<KvmPreviewAsso>> entry : previewChannels.entrySet()) {
      for (KvmPreviewAsso kvmPreviewAsso : entry.getValue()) {
        kvmPreviewAsso.setUrl(PreviewTestHelper
                .getPreviewUrl(entry.getKey(), kvmPreviewAsso.getSeq()));
      }
    }
    changePreviewOpt(masterId, manager, deviceDataGetter, taskPool);

    int previewTxCnt = 10;
    int taskCnt = 4;
    int maxPollingChannelCnt = previewTxCnt * taskCnt * 2 * 2; // 全部都是4K，两次预览的TX合在一起轮询
    int pollingChannel =
            (int) (runner.videoWallMap.size() * CaesarConstants.PREVIEW_CHANNEL_LIMIT
                    * SimplePreviewStrategy.POLLING_CHANNEL_PERCENTAGE);
    int maxWaitTime = (int) (maxPollingChannelCnt * 1.0 / pollingChannel * 500);

    Runnable runnable = () -> {
      try {
        for (int iterator = 0; iterator < 4; iterator++) {
          ObjectIds request = makeRandomRequest(runner.idKvmAssetMap, true, previewTxCnt);
          manager.addPreview(masterId, request);
          // 等待足够时间获取第一张图
          int waitTime = maxWaitTime;
          while (waitTime > 0) {
            int sleepTime = Math.min(waitTime, SimplePreviewStrategy.PREVIEW_TIME_OUT_MILLI / 2);
            Thread.sleep(sleepTime);
            waitTime -= sleepTime;
            request.getIds().forEach((tx) -> manager.onGetSnapshot(masterId, tx));
          }
          for (int i = 0; i < 60; i++) {
            Thread.sleep(100);
            PreviewTestHelper
                    .checkTxPreview(request.getIds(), true,
                            (tx) -> deviceDataGetter.makeTxSavePath(tx),
                            maxWaitTime);
            request.getIds().forEach((tx) -> manager.onGetSnapshot(masterId, tx));
          }
        }
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    };

    List<CompletableFuture<Void>> futures = new ArrayList<>();
    for (int i = 0; i < taskCnt; i++) {
      futures.add(CompletableFuture.runAsync(runnable));
    }
    for (CompletableFuture<Void> future : futures) {
      future.join();
    }
    manager.refreshSnapshotPreviewStatus(Collections.emptySet());
  }

  @Test
  public void testUpdateR2P4FStrategy() throws IllegalAccessException {
    String masterId = UUID.randomUUID().toString();
    Map<String, CaesarPreviewStrategyMode> currentMode = new HashMap<>();
    currentMode.put(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId), CaesarPreviewStrategyMode.PREVIEW_STRATEGY_R2P4F);
    when(redisUtil.getStr(RedisKey.KC_SC_SS)).thenReturn(Optional.of(String.valueOf(0)));
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, masterId)))
            .thenReturn(Optional.of(String.valueOf(RedundantModeEnum.ALONE)));
    when(redisUtil.lock(RedisKey.KC_SC_SL, String.valueOf(System.currentTimeMillis()))).thenReturn(true);
    when(redisUtil.set(anyString(), anyString())).thenAnswer(invocation -> {
      currentMode.put(invocation.getArgument(0), CaesarPreviewStrategyMode.valueOf(invocation.getArgument(1)));
      return true;
    });
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)))
            .thenReturn(Optional.of(String.valueOf(currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)))));
    KvmAssetService assetService = mock(KvmAssetService.class);
    KvmAssetVo asset = new KvmAssetVo();
    asset.setAssetId(UUID.randomUUID().toString());
    asset.setDeviceModel(DeviceType.CAESAR_R2P4F.getDeviceTypeId());
    asset.setDeviceType(DeviceType.CAESAR_R2P4F.getDeviceType());
    when(assetService.allByDeviceModelId(DeviceType.CAESAR_R2P4F.getDeviceTypeId(), masterId))
            .thenReturn(Collections.singleton(asset));
    CaesarDbServiceUtil runner = mock(CaesarDbServiceUtil.class);
    when(runner.getRedisUtil()).thenReturn(redisUtil);
    when(runner.getKvmAssetService()).thenReturn(assetService);
    when(runner.getStrategyMode(masterId)).thenReturn(currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));
    PreviewManager manager = new PreviewManager();
    SkyLinkTaskPool taskPool = new PreviewTestHelper.TaskRunnerMock();
    FieldUtil.setPropertyValue(manager, "caesarDbServiceUtil", runner);
    FieldUtil.setPropertyValue(manager, "taskPool", taskPool);
    manager.refreshSnapshotPreviewStatus(Collections.singleton(masterId));
    manager.updatePreviewStatus(DeviceType.CAESAR_R1C8_VIDEO_WALL, masterId);
    Assert.assertEquals(CaesarPreviewStrategyMode.PREVIEW_STRATEGY_R2P4F,
            currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));
    manager.updatePreviewStatus(DeviceType.CAESAR_FOUR_SCREEN_RX, masterId);
    Assert.assertEquals(CaesarPreviewStrategyMode.PREVIEW_STRATEGY_R2P4F,
            currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));
    // clear R2P4F
    when(assetService.allByDeviceModelId(DeviceType.CAESAR_R2P4F.getDeviceTypeId(), masterId))
            .thenReturn(Collections.emptyList());
    manager.updatePreviewStatus(DeviceType.CAESAR_R2P4F, masterId);
    Assert.assertEquals(CaesarPreviewStrategyMode.NONE,
            currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));
  }

  @Test
  public void testUpdate4CRXStrategy() throws IllegalAccessException {
    String masterId = UUID.randomUUID().toString();
    Map<String, CaesarPreviewStrategyMode> currentMode = new HashMap<>();
    currentMode.put(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId), CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX);
    when(redisUtil.getStr(RedisKey.KC_SC_SS)).thenReturn(Optional.of(String.valueOf(0)));
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, masterId)))
            .thenReturn(Optional.of(String.valueOf(RedundantModeEnum.ALONE)));
    when(redisUtil.lock(RedisKey.KC_SC_SL, String.valueOf(System.currentTimeMillis()))).thenReturn(true);
    when(redisUtil.set(anyString(), anyString())).thenAnswer(invocation -> {
      currentMode.put(invocation.getArgument(0), CaesarPreviewStrategyMode.valueOf(invocation.getArgument(1)));
      return true;
    });
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)))
            .thenReturn(Optional.of(String.valueOf(currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)))));
    KvmAssetService assetService = mock(KvmAssetService.class);
    KvmAssetVo asset = new KvmAssetVo();
    asset.setAssetId(UUID.randomUUID().toString());
    asset.setDeviceModel(DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId());
    asset.setCollectorProperties(Collections.singletonList(
            new Property(PropertyKeyConst.PREVIEW_ADDRESS, "**********")));

    CaesarDbServiceUtil runner = mock(CaesarDbServiceUtil.class);
    when(runner.getRedisUtil()).thenReturn(redisUtil);
    when(runner.getKvmAssetService()).thenReturn(assetService);
    when(runner.getStrategyMode(masterId)).thenReturn(currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));
    PreviewManager manager = new PreviewManager();
    SkyLinkTaskPool taskPool = new PreviewTestHelper.TaskRunnerMock();
    FieldUtil.setPropertyValue(manager, "caesarDbServiceUtil", runner);
    FieldUtil.setPropertyValue(manager, "taskPool", taskPool);
    manager.refreshSnapshotPreviewStatus(Collections.singleton(masterId));
    // clear 4CRX
    when(assetService.allByDeviceModelId(DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId(), masterId))
            .thenReturn(Collections.emptyList());
    manager.updatePreviewStatus(DeviceType.CAESAR_FOUR_SCREEN_RX, masterId);
    Assert.assertEquals(CaesarPreviewStrategyMode.NONE,
            currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));

    when(assetService.allByDeviceModelId(DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId(), masterId))
            .thenReturn(Collections.singleton(asset));
    manager.updatePreviewStatus(DeviceType.CAESAR_FOUR_SCREEN_RX, masterId);
    Assert.assertEquals(CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX,
            currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));
    manager.updatePreviewStatus(DeviceType.CAESAR_R1C8_VIDEO_WALL, masterId);
    Assert.assertEquals(CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX,
            currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));

    when(assetService.allByDeviceModelId(DeviceType.CAESAR_R2P4F.getDeviceTypeId(), masterId))
            .thenReturn(Collections.singleton(new KvmAssetVo()));
    manager.updatePreviewStatus(DeviceType.CAESAR_R2P4F, masterId);
    Assert.assertEquals(CaesarPreviewStrategyMode.PREVIEW_STRATEGY_R2P4F,
            currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));
  }

  @Test
  public void testUpdateR1C8Strategy() throws IllegalAccessException {
    String masterId = UUID.randomUUID().toString();
    Map<String, CaesarPreviewStrategyMode> currentMode = new HashMap<>();
    currentMode.put(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId), CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX);
    when(redisUtil.getStr(RedisKey.KC_SC_SS)).thenReturn(Optional.of(String.valueOf(0)));
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, masterId)))
            .thenReturn(Optional.of(String.valueOf(RedundantModeEnum.ALONE)));
    when(redisUtil.lock(RedisKey.KC_SC_SL, String.valueOf(System.currentTimeMillis()))).thenReturn(true);
    when(redisUtil.set(anyString(), anyString())).thenAnswer(invocation -> {
      currentMode.put(invocation.getArgument(0), CaesarPreviewStrategyMode.valueOf(invocation.getArgument(1)));
      return true;
    });
    when(redisUtil.getStr(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)))
            .thenReturn(Optional.of(String.valueOf(currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)))));
    KvmAssetService assetService = mock(KvmAssetService.class);
    KvmAssetVo asset = new KvmAssetVo();
    asset.setAssetId(UUID.randomUUID().toString());
    asset.setDeviceModel(DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId());
    asset.setDeviceType(DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceType());
    asset.setCollectorProperties(Collections.singletonList(
            new Property(PropertyKeyConst.PREVIEW_ADDRESS, "**********")));

    CaesarDbServiceUtil runner = mock(CaesarDbServiceUtil.class);
    when(runner.getRedisUtil()).thenReturn(redisUtil);
    when(runner.getKvmAssetService()).thenReturn(assetService);
    when(runner.getStrategyMode(masterId)).thenReturn(currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));
    PreviewManager manager = new PreviewManager();
    SkyLinkTaskPool taskPool = new PreviewTestHelper.TaskRunnerMock();
    FieldUtil.setPropertyValue(manager, "caesarDbServiceUtil", runner);
    FieldUtil.setPropertyValue(manager, "taskPool", taskPool);
    manager.refreshSnapshotPreviewStatus(Collections.singleton(masterId));

    when(assetService.allByDeviceModelId(DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId(), masterId))
            .thenReturn(Collections.singleton(asset));
    manager.updatePreviewStatus(DeviceType.CAESAR_FOUR_SCREEN_RX, masterId);
    Assert.assertEquals(CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX,
            currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));

    when(assetService.allByDeviceModelId(DeviceType.CAESAR_R2P4F.getDeviceTypeId(), masterId))
            .thenReturn(Collections.singleton(new KvmAssetVo()));
    manager.updatePreviewStatus(DeviceType.CAESAR_R2P4F, masterId);
    Assert.assertEquals(CaesarPreviewStrategyMode.PREVIEW_STRATEGY_R2P4F,
            currentMode.get(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId)));
  }

  private PreviewManager mockVP6PreviewManager(CaesarDbServiceUtil runner, SkyLinkTaskPool taskPool) throws IllegalAccessException {
    PreviewManager manager = new PreviewManager();
    FieldUtil.setPropertyValue(manager, "caesarDbServiceUtil", runner);
    FieldUtil.setPropertyValue(manager, "taskPool", taskPool);
    return manager;
  }

  private void changePreviewOpt(String masterId, PreviewManager manager, DeviceDataGetter deviceDataGetter,
                                SkyLinkTaskPool taskPool) throws IllegalAccessException {
    Map<String, PreviewStrategy> previewStrategyMap = Maps.newHashMap();
    Map<String, List<PreviewThread>> previewThreadMap = Maps.newHashMap();
    PreviewStrategy previewStrategy = PreviewStrategyFactory.createPreviewStrategy(deviceDataGetter,
            CaesarPreviewStrategyMode.PREVIEW_STRATEGY_VP6);  // 创建预览策略
    PreviewDeviceOperator previewDeviceOperator =
            new PreviewTestHelper.PreviewDeviceOperatorMock(
                    new PreviewTestHelper.TaskRunnerMock(), deviceDataGetter);
    previewStrategyMap.put(masterId, previewStrategy);
    previewThreadMap.put(masterId, List.of(
            new PollingPreviewRunner(previewStrategy.getPreviewInfoOpt(), deviceDataGetter, previewDeviceOperator),
            new RealTimePreviewRunner(previewStrategy.getPreviewInfoOpt(), deviceDataGetter, previewDeviceOperator),
            new RefreshPreviewRunner(previewStrategy, previewStrategy.getPreviewInfoOpt())));
    FieldUtil.setPropertyValue(manager, "previewStrategyMap", previewStrategyMap);
    FieldUtil.setPropertyValue(manager, "previewThreadMap", previewThreadMap);
    previewStrategy.resetChannels();
    previewThreadMap.values().forEach(previewThreads -> previewThreads.forEach(taskPool::addTask));
  }
}
