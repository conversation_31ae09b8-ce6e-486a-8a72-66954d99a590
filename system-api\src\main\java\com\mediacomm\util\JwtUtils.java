package com.mediacomm.util;

import com.google.common.base.Strings;
import com.mediacomm.entity.vo.AccountVo;
import com.mediacomm.pojo.PayloadDto;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSObject;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.Payload;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jose.crypto.MACVerifier;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * jwt.
 */
@Slf4j
public class JwtUtils {

  public static final String TOKEN_PREFIX = "Bearer ";
  public static final long EXPIRATION = 60 * 60 * 6;

  public static final String AUTHORIZATION = "Authorization"; // token header
  /**
   * jwt 密钥.
   * MEDIACOMM.SKYLINK.COM.CN. base64
   */
  private static final String JWT_SECRET_KEY = "TUVESUFDT01NLlNLWUxJTksuQ09NLkNO";

  /**
   * 生成jwt标准信息.
   *
   * @param vo .
   * @return .
   */
  public static String generateKey(PayloadDto vo) {
    try {
      // jws-header
      JWSHeader jwsHeader = new JWSHeader
          .Builder(JWSAlgorithm.HS256)
          .type(JOSEObjectType.JWT)
          .build();
      // 设置 payload and header
      Payload payload = new Payload(JsonUtils.encode(vo));
      JWSObject jwsObject = new JWSObject(jwsHeader, payload);
      // 创建HMAC签名
      JWSSigner jwsSigner = new MACSigner(JWT_SECRET_KEY);
      jwsObject.sign(jwsSigner);
      return TOKEN_PREFIX + jwsObject.serialize();
    } catch (JOSEException e) {
      log.error(e.getMessage(), e);
    }
    return null;
  }

  /**
   * 生成默认的jwt payload.
   *
   * @param account 账号.
   * @return .
   */
  public static PayloadDto getDefaultPayloadDto(AccountVo account) {
    return PayloadDto.builder()
        .sub("mc")
        .iat(System.currentTimeMillis() / 1000) // 秒
        .exp(System.currentTimeMillis() / 1000 + EXPIRATION) // 秒
        .jti(SkyLinkStringUtil.uuid())
        .username(account.getAccountName())
        .accountId(account.getAccountId())
        .build();
  }

  /**
   * HMAC方式解析token.
   *
   * @param token token.
   * @return .
   */
  public static PayloadDto verifyTokenByHmac(String token) {
    try {
      if (!Strings.isNullOrEmpty(token)) {
        JWSObject jwsObject = JWSObject.parse(token);
        JWSVerifier jwsVerifier = new MACVerifier(JWT_SECRET_KEY);
        if (jwsObject.verify(jwsVerifier)) {
          String payload = jwsObject.getPayload().toString();
          PayloadDto payloadDto = JsonUtils.decode(payload, PayloadDto.class);
          if (payloadDto != null && payloadDto.getExp() > System.currentTimeMillis() / 1000) {
            return payloadDto;
          }
        }
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return null;
  }

  /**
   * 从Http请求头Authorization中获取token.
   *
   * @param request .
   * @return token.
   */
  public static String parseJwt(HttpServletRequest request) {
    String headerAuth = request.getHeader(AUTHORIZATION);
    return parseJwt(headerAuth);
  }

  /**
   * 从Authorization中获取token.
   *
   * @param authorization .
   * @return token.
   */
  public static String parseJwt(String authorization) {
    if (StringUtils.hasText(authorization) && authorization.startsWith(TOKEN_PREFIX)) {
      return authorization.substring(TOKEN_PREFIX.length()).trim();
    }
    return null;
  }

}
