package com.mediacomm.entity.message.reqeust.body;

import com.mediacomm.entity.message.LayoutData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * LayoutDataRequestBody.
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LayoutDataRequestBody {
  @Schema(hidden = true)
  private Integer id;
  private LayoutData layoutData;
}
