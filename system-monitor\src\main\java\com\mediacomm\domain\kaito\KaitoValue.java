package com.mediacomm.domain.kaito;

import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.system.variable.sysenum.DeviceType;
import java.util.Collection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 */
@Data
public class KaitoValue {
  private String masterId;
  private String masterName;
  private String modelName;
  private DeviceType deviceType;
  private Collection<DeviceSignalValue> signalValues;
  private Collection<SlotSignalValue> slots;

  @Data
  @Builder
  @AllArgsConstructor
  @NoArgsConstructor
  public static class SlotSignalValue {
    private String slotId;
    private String slotName;
    private String modelName;
    private DeviceType deviceType;
    private CardCategoryEnum cardType;
    private Collection<DeviceSignalValue> signalValues;
    private Collection<ExtendSignalValue> extendList;
  }

  @Data
  @Builder
  @AllArgsConstructor
  @NoArgsConstructor
  public static class ExtendSignalValue {
    private String assetId;
    private String assetName;
    private String modelName;
    private DeviceType deviceType;
    private Collection<DeviceSignalValue> signalValues;
  }
}
