package com.mediacomm.controller.rpc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.VisualizationDevice;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.pojo.VisualizationDeviceStatus;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.VisualizationDeviceService;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.mq.RabbitSender;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.ArrayList;
import java.util.Collection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备监控.
 */
@Tag(name = "设备监控")
@RestController
@RequestMapping(ResUrlDef.MONITOR)
public class MonitorRpc extends SkyLinkController<KvmMaster, KvmMasterService> {

  @Autowired
  RabbitSender sender;
  @Autowired
  RedisUtil redisUtil;
  @Autowired
  VisualizationDeviceService visualizationDeviceService;

  @GetMapping("/visualization-devices/values")
  public Result<Collection<VisualizationDeviceStatus>> valuesOfVisualizationDevices() {
    Collection<VisualizationDevice> devices = visualizationDeviceService.list();
    Collection<VisualizationDeviceStatus> statuses = new ArrayList<>();
    for (VisualizationDevice device : devices) {
      String redisKey =
          RedisUtil.redisKey(RedisKey.VIS, "device.status", device.getHardcode());
      redisUtil.getStr(redisKey).ifPresent(status ->
              statuses.add(JsonUtils.decode(status, VisualizationDeviceStatus.class)));
    }
    return Result.ok(statuses);
  }

  @GetMapping("/device/values")
  public Result<?> valuesOfEnvDevice(@RequestParam("deviceId") String deviceId,
                                     @RequestParam("deviceType") DeviceType deviceType) {
    MqRequest<DeviceType> mq = new MqRequest<>();
    mq.setBody(deviceType);
    mq.setMasterId(deviceId);
    Object res = sender.syncSend(RoutingKey.MONITOR_DEVICE_GET_VALUE, mq);
    return res != null ? JsonUtils.decode(JsonUtils.encode(res), new TypeReference<>() {}) :
            Result.failure("Remote message time out", ResponseCode.REMOTE_MSG_TIME_OUT);
  }
}
