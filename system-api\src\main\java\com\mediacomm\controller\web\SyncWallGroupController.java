package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.SyncWallGroup;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.SyncWallGroupService;
import com.mediacomm.system.variable.ResUrlDef;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "同屏管理", description = "同步组内的大屏所有操作")
@RestController
@RequestMapping(ResUrlDef.SYNC_VIDEO_WALL)
public class SyncWallGroupController extends
    SkyLinkController<SyncWallGroup, SyncWallGroupService> {

  @Operation(summary = "获取指定同屏组内的所有电视墙信息")
  @GetMapping("/sync-video-walls")
  public Result<Collection<KvmVideoWall>> getVideoWalls(@RequestParam(name = "syncGroupId")
                                                          Integer syncGroupId) {

    return Result.ok(service.allVideoWallsBySyncGroupId(syncGroupId));
  }

  @Operation(summary = "获取所有同屏分组")
  @GetMapping("/sync-wall-groups")
  public Result<Collection<SyncWallGroup>> getGroups() {
    return Result.ok(service.allSyncWallGroups());
  }

  @Operation(summary = "修改同屏分组信息")
  @PutMapping("/sync-wall-group")
  public Result<?> updateSyncWallGroup(@RequestParam(name = "syncGroupId") Integer id,
                                       @RequestBody SyncWallGroup group) {
    group.setId(id);
    service.updateById(group);
    return Result.ok();
  }

  @Operation(summary = "根据Id删除同屏分组")
  @DeleteMapping("/sync-wall-group")
  public Result<?> delSyncWallGroup(@RequestParam(name = "syncGroupId") Integer id) {
    service.removeById(id);
    return Result.ok();
  }

  @Operation(summary = "创建同屏分组")
  @PostMapping("/sync-wall-group")
  public Result<?> addSyncWallGroup(@RequestBody SyncWallGroup group) {
    service.save(group);
    return Result.ok();
  }

  @Operation(summary = "批量添加大屏到同屏组中")
  @PostMapping("/sync-video-walls")
  public Result<?> addWallsToSyncGroup(@RequestParam("syncGroupId") Integer id,
                                       @RequestBody Collection<Integer> wallIds) {
    service.saveWallWithSyncGroup(id, wallIds);
    return Result.ok();
  }

  @Operation(summary = "删除同屏组中的大屏信息")
  @DeleteMapping
  public Result<?> delWallFromGroup(@RequestParam("syncGroupId") Integer groupId,
                                    @RequestParam("videoWallId") Integer wallId) {
    service.removeWallFromSyncGroup(groupId, wallId);
    return Result.ok();
  }
}
