package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.mediacomm.entity.message.reqeust.body.SeatOpenTxesRequestBody;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "seat_scene", description = "可视化坐席页面内当前所有坐席的预案")
public class SeatScene extends SkyLinkDbEntity {
  @TableId(value = "scene_id", type = IdType.AUTO)
  private Integer sceneId;
  private String name;
  private String uniqueSearchKey;
  @TableField(typeHandler = JacksonTypeHandler.class)
  private List<SeatOpenTxesRequestBody> data;
}
