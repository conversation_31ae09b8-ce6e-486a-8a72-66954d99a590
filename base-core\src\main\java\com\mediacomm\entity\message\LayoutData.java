package com.mediacomm.entity.message;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * LayoutData.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LayoutData implements Serializable {
  private int width;
  private int height;
  private List<LayoutRect> panels = new ArrayList<>();
}
