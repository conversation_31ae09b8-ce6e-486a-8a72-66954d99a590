package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.TinyIntToBooleanTypeHandler;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import com.mediacomm.system.variable.sysenum.SubSystemType.AlarmLevel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 告警表.
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "alarm", description = "告警对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Alarm extends SkyLinkDbEntity {
  @TableId(value = "alarm_id", type = IdType.AUTO)
  private Integer alarmId;
  private String masterId; // 告警设备所属父级设备ID
  private String deviceId; // 告警设备ID
  private String deviceName; // 告警设备名称
  private String signalId; // 告警信号ID
  private String signalName;
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private boolean ended = false; // 已结束否
  private Long endTime; // 结束时间
  private Long beginTime; // 开始时间
  private String endByPerson; // 结束人员名称
  private SubSystemType subSystem; // 所属系统级别
  private AlarmLevel alarmLevel; // 告警级别
  private String alarmDesc; // 告警描述
  @TableField(typeHandler = JacksonTypeHandler.class)
  private SignalValue signalValue; // 信号值
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private boolean acked = false; // 确认否
  private String ackByPerson; // 确认人员名称
  private String ackDesc; // 确认描述
  private Long ackTime; // 确认时间
  private Long muteTime; // 消音时间
  private String muteByPerson; // 消音人员
  private String suggestion; // 修复建议
}
