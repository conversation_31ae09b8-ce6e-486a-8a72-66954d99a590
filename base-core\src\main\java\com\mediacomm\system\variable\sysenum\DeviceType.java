package com.mediacomm.system.variable.sysenum;

import com.mediacomm.system.variable.RoutingKey;
import lombok.Getter;

/**
 * device_model.
 */
@Getter
public enum DeviceType {

  UNKNOWN(0, "UNKNOWN", "未知设备", SubSystemType.SELF_DIAGNOSIS),
  MONITOR_SERVER(13, "MONITOR_SERVER", "监控服务器", SubSystemType.SELF_DIAGNOSIS),
  AIRCON(1001, "AIRCON", "云控主机", SubSystemType.KVM, RoutingKey.AIRCON),
  AIRCON_VIDEO_WALL_RX(1202, "AIRCON_VIDEO_WALL_RX", "云控大屏Rx", SubSystemType.PER_EQUIPMENT_RX),
  AIRCON_SEAT_RX(1201, "AIRCON_SEAT_RX", "云控坐席Rx", SubSystemType.PER_EQUIPMENT_RX),
  AIRCON_ENCODER(1101, "AIRCON_ENCODER", "云控Tx", SubSystemType.PER_EQUIPMENT_TX),
  AIRCON_OTHER_SRC(1102, "AIRCON_OTHER_SRC", "云控IPC设备", SubSystemType.PER_EQUIPMENT_TX),
  AIRCON_VIDEO_WALL(1301, "AIRCON_VIDEO_WALL", "云控视频墙", SubSystemType.VIDEO_WALL),
  CAESAR(2101, "CAESAR", "凯撒中文主机", SubSystemType.KVM, RoutingKey.CAESAR),
  CAESAR_TX(2201, "CAESAR_TX", "凯撒中文Tx", SubSystemType.PER_EQUIPMENT_TX),
  CAESAR_RX(2301, "CAESAR_RX", "凯撒中文Rx", SubSystemType.PER_EQUIPMENT_RX),
  CAESAR_FOUR_SCREEN_RX(2302, "CAESAR_FOUR_SCREEN_RX", "凯撒中文四画面Rx", SubSystemType.PER_EQUIPMENT_RX),
  CAESAR_KAITO_SECOND_CARD(2303, "CAESAR_KAITO_SECOND_CARD", "凯撒中文嗨动子母卡", SubSystemType.PER_EQUIPMENT_RX),
  CAESAR_DECODE_DEVICE(2304, "CAESAR_DECODE_DEVICE", "凯撒中文融合通信终端", SubSystemType.PER_EQUIPMENT_RX),
  CAESAR_R2P4F(2305, "CAESAR_R2P4F", "R2P4F", SubSystemType.PER_EQUIPMENT_RX),
  CAESAR_VP6(2401, "CAESAR_VP6", "凯撒中文VP6", SubSystemType.PER_EQUIPMENT_RX),
  CAESAR_VP7(2402, "CAESAR_VP7", "凯撒中文VP7", SubSystemType.PER_EQUIPMENT_RX, Boolean.TRUE),
  CAESAR_SLOT(2501, "KVM_SLOT", "凯撒中文光纤板卡", SubSystemType.INTERNAL_BOARD),
  CAESAR_VP6_VIDEO_WALL(2601, "CAESAR_VP6_VIDEO_WALL", "凯撒中文VP6视频墙", SubSystemType.VIDEO_WALL),
  CAESAR_KAITO_VIDEO_WALL(2602, "CAESAR_KAITO_VIDEO_WALL", "凯撒中文全光拼接屏", SubSystemType.VIDEO_WALL),
  CAESAR_R1C8_VIDEO_WALL(2603, "CAESAR_R1C8_VIDEO_WALL", "R1C8", SubSystemType.VIDEO_WALL),
  CAESAR_VP7_VIDEO_WALL(2604, "CAESAR_VP7_VIDEO_WALL", "凯撒中文VP7视频墙", SubSystemType.VIDEO_WALL),
  KAITO02(4101, "KAITO02", "Kaito-02处理器", SubSystemType.KVM, RoutingKey.KAITO),
  KAITO02_INPUT(4201, "KAITO02_INPUT", "Kaito-02处理器输入卡", SubSystemType.PER_EQUIPMENT_TX),
  KAITO02_IPC(4202, "KAITO02_IPC", "Kaito-02处理器IPC", SubSystemType.PER_EQUIPMENT_TX),
  KAITO02_OUTPUT(4301, "KAITO02_OUTPUT", "Kaito-02处理器输出卡", SubSystemType.PER_EQUIPMENT_RX),
  KAITO02_SLOT(4401, "KAITO02_SLOT", "Kaito-02处理器插槽", SubSystemType.INTERNAL_BOARD),
  KAITO02_VIDEO_WALL(4501, "KAITO02_VIDEO_WALL", "Kaito-02视频墙", SubSystemType.VIDEO_WALL),
  HIKVISION_SECURE_HOST(6001, "HIKVISION_SECURE_HOST", "海康安防主机", SubSystemType.KVM),
  HIKVISION_SECURE_IPC(6101, "HIKVISION_SECURE_IPC", "海康安防IPC", SubSystemType.PER_EQUIPMENT_TX),
  NETGEAR(7001, "NETGEAR", "网件交换机", SubSystemType.SWITCH_DEVICE),
  TSINGLI(8001, "TSINGLI", "清立中控", SubSystemType.KVM, RoutingKey.TSINGLI),
  SWITCHER_TX(9001, "SWITCHER_TX", "kvm切换器接入端", SubSystemType.PER_EQUIPMENT_TX, RoutingKey.KVM_SWITCHER),
  SWITCHER_RX(9101, "SWITCHER_RX", "kvm切换器管控端", SubSystemType.PER_EQUIPMENT_RX, RoutingKey.KVM_SWITCHER);

  private Integer deviceTypeId;
  private String deviceType;
  private String deviceTypeTitle;
  private String rootRoute = "dlq-queue"; // 未定义的类型路由默认直接丢弃
  private SubSystemType subSystem;
  private boolean supportUpgrade = false;

  DeviceType(
      Integer deviceTypeId, String deviceType, String deviceTypeTitle, SubSystemType subSystem) {
    this.deviceTypeId = deviceTypeId;
    this.deviceType = deviceType;
    this.deviceTypeTitle = deviceTypeTitle;
    this.subSystem = subSystem;
  }

  DeviceType(
      Integer deviceTypeId, String deviceType, String deviceTypeTitle, SubSystemType subSystem, boolean supportUpgrade) {
    this.deviceTypeId = deviceTypeId;
    this.deviceType = deviceType;
    this.deviceTypeTitle = deviceTypeTitle;
    this.subSystem = subSystem;
    this.supportUpgrade = supportUpgrade;
  }

  DeviceType(
          Integer deviceTypeId, String deviceType, String deviceTypeTitle, SubSystemType subSystem, String rootRoute) {
    this.deviceTypeId = deviceTypeId;
    this.deviceType = deviceType;
    this.deviceTypeTitle = deviceTypeTitle;
    this.subSystem = subSystem;
    this.rootRoute = rootRoute;
  }
}
