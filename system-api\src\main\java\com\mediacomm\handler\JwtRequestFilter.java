package com.mediacomm.handler;

import com.google.common.base.Strings;
import com.mediacomm.pojo.PayloadDto;
import com.mediacomm.service.UserDetailServiceImpl;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.util.JwtUtils;
import com.mediacomm.util.RedisUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * token拦截器.
 */
@Slf4j
public class JwtRequestFilter extends OncePerRequestFilter {

  @Autowired
  private UserDetailServiceImpl detailService;
  @Autowired
  private RedisUtil redisUtil;

  @Override
  protected void doFilterInternal(HttpServletRequest request,
                                  HttpServletResponse response,
                                  FilterChain filterChain) {
    Optional<PayloadDto> opt = getOnlineToken(request);
    if (opt.isPresent()) {
      PayloadDto dto = opt.get();
      UserDetails details = detailService.loadUserByUsername(dto.getUsername());
      UsernamePasswordAuthenticationToken authenticationToken =
          new UsernamePasswordAuthenticationToken(details, null, details.getAuthorities());
      authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
      SecurityContextHolder.getContext().setAuthentication(authenticationToken);
    }
    try {
      filterChain.doFilter(request, response);
    } catch (Exception e) {
      log.error("filterChain doFilter failed.url:{}", request.getRequestURI());
      log.error(e.getMessage(), e);
    }

  }

  /**
   * 从redis中获取未过期的token.
   *
   * @param request .
   * @return .
   */
  private Optional<PayloadDto> getOnlineToken(HttpServletRequest request) {
    String token = JwtUtils.parseJwt(request);
    if (!Strings.isNullOrEmpty(token)) {
      PayloadDto payloadDto = JwtUtils.verifyTokenByHmac(token);
      if (payloadDto != null) {
        Optional<Map<String, String>> onlineTokenMap = redisUtil.hmget(RedisKey.SA_TK_ID);
        if (onlineTokenMap.isPresent()) {
          String existToken = onlineTokenMap.get().get(payloadDto.getJti());
          if (!Strings.isNullOrEmpty(existToken)) {
            return Optional.ofNullable(JwtUtils.verifyTokenByHmac(existToken));
          }
        }
      }
    }
    return Optional.empty();
  }
}
