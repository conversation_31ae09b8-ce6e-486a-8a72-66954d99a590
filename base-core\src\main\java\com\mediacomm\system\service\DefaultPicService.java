package com.mediacomm.system.service;

import com.mediacomm.entity.dao.DefaultPicture;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.DefaultPicMapper;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class DefaultPicService extends SkyLinkServiceImpl<DefaultPicMapper, DefaultPicture> {

  /**
   * 根据名称查询图片合并对象.
   */
  public DefaultPicture oneByName(String name) {
    return baseMapper.findByName(name);
  }
}
