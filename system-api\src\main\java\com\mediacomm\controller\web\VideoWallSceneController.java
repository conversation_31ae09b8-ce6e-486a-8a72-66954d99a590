package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.VideoWallScene;
import com.mediacomm.entity.message.reqeust.body.PollingScenesListRequestBody;
import com.mediacomm.handler.PollingScenesHandler;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.service.VideoWallSceneService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.WebSocketHandlerSendNoticeUtils;
import com.mediacomm.util.WebSocketTopic;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "大屏预案管理")
@RestController
@RequestMapping(ResUrlDef.VIS_WALL_SCENE)
public class VideoWallSceneController
    extends SkyLinkController<VideoWallScene, VideoWallSceneService> {

  @Autowired
  private KvmVideoWallService videoWallService;
  @Autowired
  private WebSocketHandlerSendNoticeUtils noticeUtils;
  @Autowired
  private PollingScenesHandler scenesHandler;

  @Operation(summary = "根据大屏Id获取相关的预案")
  @Parameter(name = "videoWallId", description = "大屏Id")
  @GetMapping("/scenes")
  public Result<Collection<VideoWallScene>> getScenes(@RequestParam("videoWallId")
                                                        Integer videoWallId) {
    return Result.ok(service.allByWallId(videoWallId));
  }

  @Operation(summary = "根据预览Id修改指定预案名称")
  @PutMapping("/{id}")
  public Result<?> updateSceneNameById(@PathVariable Integer id,
                                       @RequestParam("sceneName") String sceneName) {
    VideoWallScene scene = service.getById(id);
    if (scene == null) {
      return Result.failure("No exist scene!", ResponseCode.EX_FAILURE_400);
    }
    if (!scene.getName().equals(sceneName)) {
      scene.setName(sceneName);
      service.updateById(scene);
    }
    return Result.ok();
  }

  @OperationLogRecord(title = "删除预案", operateType = OperateType.DELETE)
  @Operation(summary = "根据预案Id删除相关的预案")
  @DeleteMapping("/{id}")
  public Result<String> delSceneById(@PathVariable Integer id) {
    VideoWallScene scene = service.getById(id);
    service.removeById(id);
    if (scene != null) {
      noticeUtils.sendVideoWallChangeNotice(WebSocketTopic.EVENT_CHANGE_VW_SCENE, scene.getWallId());
    }
    return Result.ok();
  }

  @OperationLogRecord(title = "批量删除预案", operateType = OperateType.DELETE,
      requestBody = "#{ids}")
  @Operation(summary = "批量根据预案Id进行删除")
  @DeleteMapping("/scenes")
  public Result<String> delSceneByIds(@RequestBody Collection<Integer> ids) {
    service.removeBatchByIds(ids);
    return Result.ok();
  }

  @OperationLogRecord(title = "创建预案", operateType = OperateType.INSERT,
      requestBody = "#{scene}")
  @Operation(summary = "创建预案")
  @PostMapping
  public Result<String> addScene(@RequestBody VideoWallScene scene) {
    service.save(scene);
    noticeUtils.sendVideoWallChangeNotice(WebSocketTopic.EVENT_CHANGE_VW_SCENE, scene.getWallId());
    return Result.ok();
  }

  @Operation(summary = "设置轮询预案")
  @PostMapping("/polling")
  public Result<?> setPollingScenes(@RequestBody PollingScenesListRequestBody requestBody) {
    KvmVideoWall videoWall = videoWallService.getById(requestBody.getVideoWallId());
    if (videoWall == null) {
      return Result.failure("No exist videoWall!", ResponseCode.EX_FAILURE_400);
    }
    if (!Objects.equals(videoWall.getPollingIntervalTime(), requestBody.getPollingIntervalTime())) {
      videoWall.setPollingIntervalTime(requestBody.getPollingIntervalTime());
      videoWallService.updateById(videoWall);
      scenesHandler.changePollingScenesIntervalTime(videoWall.getWallId(), requestBody.getPollingIntervalTime());
    }
    Collection<VideoWallScene> sceneList = service.allByWallId(requestBody.getVideoWallId());
    boolean changeSceneList = false;
    for (VideoWallScene videoWallScene : sceneList) {
      if (requestBody.getPollingScenes().contains(videoWallScene.getSceneId())) {
        if (!videoWallScene.isPollingScenesEnable()) {
          service.setPollingScenesEnableValue(videoWallScene.getSceneId(), true);
          changeSceneList = true;
        }
      } else if (videoWallScene.isPollingScenesEnable()) {
        service.setPollingScenesEnableValue(videoWallScene.getSceneId(), false);
        changeSceneList = true;
      }
    }
    if (changeSceneList) {
      scenesHandler.addPollingScenesToTask(videoWall.getWallId(),
              service.allByWallId(videoWall.getWallId()).stream()
                      .filter(VideoWallScene::isPollingScenesEnable).collect(Collectors.toSet()));
    }
    return Result.ok();
  }

}
