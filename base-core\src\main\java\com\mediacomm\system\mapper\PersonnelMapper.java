package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mediacomm.entity.dao.Personnel;
import java.util.Collection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 人员.
 */
@Mapper
public interface PersonnelMapper extends BaseMapper<Personnel> {
  @Select("select * from personnel as p where p.department = #{departmentId} and p.personnel_id "
      + "not in (select personnel from account)")
  Collection<Personnel> findPersonnelByDepartmentAndUnused(Integer departmentId);
}
