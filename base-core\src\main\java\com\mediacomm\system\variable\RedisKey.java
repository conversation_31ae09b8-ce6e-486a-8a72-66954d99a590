package com.mediacomm.system.variable;

/**
 * redis的key名规范.
 */
public class RedisKey {
  /**
   * kvm-collectors:skylink-caesar:.
   */
  public static final String KC_SC = "kc:sc:";
  /**
   * snapshot lock.
   */
  public static final String KC_SC_SL = KC_SC + "sl";
  /**
   * snapshot status.
   */
  public static final String KC_SC_SS = KC_SC + "ss";
  /**
   * strategy mode.
   */
  public static final String KC_SC_SM = KC_SC + "sm";
  /**
   * polling scenes status.
   */
  public static final String KC_SC_PS = KC_SC + "ps";
  /**
   * Redundant status.
   */
  public static final String REDUNDANT_STATUS = "redundant:status";
  /**
   * polling scenes status.
   */
  public static final String POLLING_SCENES_STATUS = "polling:scenes:status";
  /**
   * 轮询预览初次启动获取的锁.
   */
  public static final String POLLING_SCENES_LOCK = "polling:scenes:lock";
  /**
   * system-api:token:id.
   */
  public static final String SA_TK_ID = "sa:tk:id";
  /**
   * system-api:token:expired time.
   */
  public static final String SA_TK_ET = "sa:tk:et";
  public static final String KVM = "kvm";
  public static final String VIS = "vis";
  // global settings
  public static final String SYSTEM_INFO = "sys:info";
  public static final String SYSTEM_ALARM_SETTINGS = "sys:alarm:settings";
}
