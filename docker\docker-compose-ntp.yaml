version: "3.8"

services:
  ntp:
    image: 192.168.1.233/cturra/ntp:lastest
    environment:
      - "NTP_SERVERS=ntp.aliyun.com,ntp.tencent.com,time.windows.com"
      - "NOCLIENTLOG=true"
      - "LOG_LEVEL=0"
      - "TZ=Asia/Shanghai"
    ports:
      - "123:123/udp"
    read_only: true
    container_name: ntp
    restart: always
    tmpfs:
      - /etc/chrony:rw,mode=1750 # 用于存放运行时配置（如服务器同步状态）
      - /run/chrony:rw,mode=1750 # 用于存放 PID 文件或套接字
      - /var/lib/chrony:rw,mode=1750 # 用于持久化缓存数据（但这里使用 tmpfs 表示不保留历史）
