package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.entity.message.LayoutData;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.VisualizationLayoutType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "visualization_layout", description = "可视化布局")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VisualizationLayout extends SkyLinkDbEntity {
  @TableId(value = "layout_id", type = IdType.AUTO)
  private Integer layoutId; // 布局Id
  private Integer seq; // 布局序号
  private VisualizationLayoutType layoutType; // 使用范围
  private String name; // 布局名称
  @TableField(typeHandler = JacksonTypeHandler.class)
  private LayoutData layoutData; // 布局数据
}
