package com.mediacomm.caesar.snapshot;

import java.lang.reflect.Field;

/**
 * .
 */
public class FieldUtil {
  public static void setPropertyValue(Object obj, String propertyName, Object value) throws IllegalAccessException {
    Class<?> clazz = obj.getClass();
    try {
      Field field = clazz.getDeclaredField(propertyName);
      field.setAccessible(true);
      field.set(obj, value);
    } catch (NoSuchFieldException e) {
      clazz = clazz.getSuperclass();
      if (clazz != null) {
        Field[] fs = clazz.getDeclaredFields();
        for (Field f : fs) {
          if (f.getName().equals(propertyName)) {
            f.setAccessible(true);
            f.set(obj, value);
          }
        }
      }
    }
  }

  public static Object getPropertyValue(Object obj, String propertyName) throws IllegalAccessException {
    Class<?> clazz = obj.getClass();
    try {
      Field field = clazz.getDeclaredField(propertyName);
      field.setAccessible(true);
      return field.get(obj);
    } catch (NoSuchFieldException e) {
      clazz = clazz.getSuperclass();
      if (clazz != null) {
        Field[] fs = clazz.getDeclaredFields();
        for (Field f : fs) {
          if (f.getName().equals(propertyName)) {
            f.setAccessible(true);
          }
        }
      }
    }
    return null;
  }
}
