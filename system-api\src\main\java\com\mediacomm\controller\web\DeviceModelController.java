package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.DeviceModel;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.DeviceModelService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "设备型号", description = "设备型号配置的API")
@RestController
@RequestMapping(ResUrlDef.DEVICE_MODEL)
public class DeviceModelController extends SkyLinkController<DeviceModel, DeviceModelService> {

  @Operation(summary = "获取所有设备型号信息或按照系统分级过滤")
  @GetMapping("/device-models")
  public Result<Collection<DeviceModel>> getDeviceModels(@RequestParam(value = "subSystem",
      required = false) SubSystemType subSystem) {
    if (subSystem != null ) {
      return Result.ok(service.allBySubSystem(subSystem));
    }
    return Result.ok(service.list());
  }

  @Operation(summary = "通过Id获取指定设备型号信息")
  @GetMapping("/{id}")
  public Result<DeviceModel> getDeviceModel(@PathVariable Integer id) {
    return Result.ok(service.getById(id));
  }

  @Operation(summary = "分页查询获取设备型号信息")
  @Parameter(name = "currentPage", description = "当前页码", required = true)
  @Parameter(name = "pageSize", description = "每页行数", required = true)
  @GetMapping("/page")
  public Result<PageResult<DeviceModel>> getDeviceModels(@RequestParam("currentPage")
                                            Integer currentPage,
                                            @RequestParam("pageSize")
                                            Integer pageSize) {
    return Result.ok(service.allByPage(currentPage, pageSize));
  }

  /**
   * .
   */
  @OperationLogRecord(title = "修改设备型号", operateType = OperateType.UPDATE,
      requestBody = "#{deviceModel}")
  @Operation(summary = "通过Id修改指定设备型号")
  @PutMapping("/{id}")
  public Result<?> updateDeviceMode(@PathVariable Integer id,
                                              @RequestBody DeviceModel deviceModel) {
    deviceModel.setModelId(id);
    service.updateById(deviceModel);
    return Result.ok();
  }

  @OperationLogRecord(title = "删除设备型号", operateType = OperateType.DELETE)
  @Operation(summary = "通过Id删除指定设备型号")
  @DeleteMapping("/{id}")
  public Result<?> delDeviceModel(@PathVariable Integer id) {
    service.removeById(id);
    return Result.ok();
  }

  @OperationLogRecord(title = "批量删除设备型号", operateType = OperateType.DELETE,
      requestBody = "#{ids}")
  @Operation(summary = "通过Id批量删除设备型号")
  @DeleteMapping("/device-models")
  public Result<?> delDeviceModels(@RequestBody Collection<Integer> ids) {
    service.removeBatchByIds(ids);
    return Result.ok();
  }

  @OperationLogRecord(title = "删除指定设备型号", operateType = OperateType.INSERT,
      requestBody = "#{deviceModel}")
  @Operation(summary = "新增设备型号")
  @PostMapping
  public Result<?> addDeviceModel(@RequestBody DeviceModel deviceModel) {
    service.save(deviceModel);
    return Result.ok();
  }

}
