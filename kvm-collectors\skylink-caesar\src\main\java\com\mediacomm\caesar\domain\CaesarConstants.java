package com.mediacomm.caesar.domain;

/**
 * CaesarConstants.
 */
public class CaesarConstants {
  public static final int OPEN_OK = 3;      // 开窗成功
  public static final int OPEN_ERR = 4;     // 开窗失败
  public static final int CLOSE_OK = 5;     // 关窗成功
  public static final int CLOSE_ERR = 6;    // 关窗失败
  public static final int MOVE_OK = 7;      // 移动成功
  public static final int MOVE_ERR = 8;     // 移动失败
  public static final int PREVIEW_CHANNEL_LIMIT = 8;

  public static final String DECODER_START_URL = "/cgi-bin/decode_start.cgi";
  public static final String DECODER_STOP_URL = "/cgi-bin/decode_stop.cgi";
  public static final String DECODER_STATUS_URL = "/cgi-bin/decode_status.cgi";

  public static final String URI_FORMAT = "http://%s:%d";
  public static final int PORT = 8080;
}
