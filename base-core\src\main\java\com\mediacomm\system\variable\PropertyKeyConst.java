package com.mediacomm.system.variable;

/**
 * 其他定义的全局key.
 *
 * @author: <PERSON><PERSON>e<PERSON><PERSON>.
 */
public class PropertyKeyConst {
  public static final String LICENCE = "licence";
  /**
   * 凯撒特殊扩展属性.
   */
  public static final String SPECIAL_EXT_TYPE = "specialExtType";
    /**
   * 凯撒特殊子扩展属性.
   */
  public static final String SPECIAL_EXT_SUB_TYPE = "specialExtSubType";
  /**
   * 凯撒TX/Rx适用分辨率类型.
   */
  public static final String CAESAR_TX_RESOLUTION_TYPE_KEY = "videoResolutionType";
  /**
   * 坐标X.
   */
  public static final String POS_X = "posX";
  /**
   * 坐标Y.
   */
  public static final String POS_Y = "posY";
  /**
   * 预览地址.
   */
  public static final String PREVIEW_ADDRESS = "previewAddress";
  public static final String PREVIEW_TYPE = "previewType";
  public static final String PREVIEW_MODEL = "previewModel";
  public static final String PORT = "port";
  public static final String ADDRESS = "address";
  public static final String SECRETE_KEY = "secreteKey";
  public static final String LAN_NUMBER = "totalLanNumber"; // 网口
  public static final String VIDEO_DP_NUMBER = "totalVideoDpNumber"; // 视频口DP
  public static final String PORT_NUMBER = "totalPortNumber";
  public static final String SLOT_NUMBER = "totalSlotNumber";
  public static final String USB_NUMBER = "totalUsbNumber";
  /**
   * 底图Hash.
   */
  public static final String BOTTOM_IMAGE_HASH = "bottomImageHash";
  /**
   * ntp地址.
   */
  public static final String NTP_ADDR = "ntpAddress";
  /**
   * ntp端口.
   */
  public static final String NTP_PORT = "ntpPort";

  /**
   * 子码流 or 主码流.
   */
  public static final String STREAM_TYPE = "streamType";
  /**
   * 风扇数量.
   */
  public static final String FAN_NUMBER = "totalFanNumber";
  /**
   * 电源数量.
   */
  public static final String POWER_NUMBER = "totalPowerNumber";
  /**
   * 光口数量.
   */
  public static final String LINK_NUMBER = "totalLinkNumber";
  /**
   * 视频口数量.
   */
  public static final String VIDEO_NUMBER = "totalVideoNumber";
  /**
   * 是否支持音频.
   */
  public static final String SUPPORT_AUDIO = "supportAudio";
}
