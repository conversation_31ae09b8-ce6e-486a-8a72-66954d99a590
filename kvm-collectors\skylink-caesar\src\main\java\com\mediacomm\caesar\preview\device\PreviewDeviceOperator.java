package com.mediacomm.caesar.preview.device;

import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import java.io.Closeable;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * .
 */
public interface PreviewDeviceOperator extends Closeable {

  /**
   * 开启窗口.
   *
   * @param txChannelMap TX与开窗通道的对应关系
   * @return 开窗是否成功
   */
  CompletableFuture<Map<String, Boolean>> openPanel(Map<String, KvmPreviewAsso> txChannelMap);

  /**
   * 关闭窗口.
   *
   * @param channels 关窗的通道.
   * @return 关窗是否成功.
   */
  CompletableFuture<Boolean> closePanels(Collection<KvmPreviewAsso> channels);

  /**
   * 开窗并拿图.
   *
   * @param txChannelMap  TX与开窗通道的对应关系.
   * @param txSavePathMap TX与保存位置的对应关系.
   * @param unusedChannels 标记为使用但实际上没有使用的通道.
   * @return 是否开窗并拿图成功.
   */
  CompletableFuture<Map<String, Boolean>> openPanelAndGetPreview(
      Map<String, KvmPreviewAsso> txChannelMap, Map<String, String> txSavePathMap,
      Collection<KvmPreviewAsso> unusedChannels);

  /**
   * 获取预览图，保存到指定位置. 非阻塞
   *
   * @param txSavePathMap .
   * @return .
   */
  CompletableFuture<Map<String, Boolean>> getPreview(Map<String, String> txSavePathMap);

  CaesarPreviewStrategyMode getPreviewStrategyMode();
}
