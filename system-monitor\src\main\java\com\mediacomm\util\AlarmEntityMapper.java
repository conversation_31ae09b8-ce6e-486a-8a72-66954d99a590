package com.mediacomm.util;

import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.dao.Alarm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface AlarmEntityMapper {
  AlarmEntityMapper INSTANCE = Mappers.getMapper(AlarmEntityMapper.class);

  @Mapping(source = "statusName", target = "alarmDesc")
  @Mapping(source = "masterId", target = "masterId")
  @Mapping(source = "deviceId", target = "deviceId")
  @Mapping(source = "deviceName", target = "deviceName")
  @Mapping(source = "signalId", target = "signalId")
  @Mapping(source = "signalName", target = "signalName")
  @Mapping(source = "alarmLevel", target = "alarmLevel")
  @Mapping(source = "signalValue", target = "signalValue")
  @Mapping(source = "suggestion", target = "suggestion")
  Alarm toAlarm(DeviceSignalValue signalValue);
}
