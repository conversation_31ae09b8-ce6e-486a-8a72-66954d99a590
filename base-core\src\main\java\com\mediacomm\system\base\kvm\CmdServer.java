package com.mediacomm.system.base.kvm;

import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.system.service.EncoderAssoService;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmSeatService;
import com.mediacomm.system.service.KvmSlotService;
import com.mediacomm.system.service.KvmUserService;
import com.mediacomm.system.service.KvmVideoWallService;
import jakarta.annotation.Resource;

/**
 * CmdServer.
 */
public abstract class CmdServer {

  public static final String URI_FORMAT = "http://%s:%d";
  public static final String PARAM_ERR = "Request parameter error.";
  public static final String NO_HOST = "No such host.";
  public static final String ID_FORMAT_ERROR = "Id format error.";
  public static final String NO_VIDEO_SRC = "Not found video src.";
  public static final String NO_VIDEO_WALL = "Not found video-wall.";
  public static final String NO_SUPPORT = "This operation is not supported.";
  public static final String EXCEED_LIMIT = "Exceed the limit.";
  public static final String UNKNOWN_CODE_FROM_SERVER = "Unknown code from server.";
  public static final String INTERFACE_NOT_RETURN = "Interface did not return.";

  @Resource
  protected KvmVideoWallService videoWallService;

  @Resource
  protected KvmAssetService kvmAssetService;

  @Resource
  protected KvmSeatService kvmSeatService;

  @Resource
  protected KvmUserService kvmUserService;

  @Resource
  protected KvmMasterService kvmMasterService;

  @Resource
  protected EncoderAssoService encoderAssoService;

  @Resource
  protected KvmSlotService kvmSlotService;

  protected int getVideoWallDeviceIdByWallId(Integer videoWallId) {
    KvmVideoWall videoWall = videoWallService.getById(videoWallId);
    return videoWall != null ? videoWall.getDeviceId() : -1;
  }

  /**
   * 批量开窗.
   *
   * @param msg .
   * @return .
   */
  public abstract String openVwPanels(String msg);
}
