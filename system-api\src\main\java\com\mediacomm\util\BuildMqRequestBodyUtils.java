package com.mediacomm.util;

import com.mediacomm.caesar.domain.CaesarMqRequest;
import com.mediacomm.caesar.domain.KvmOperationFrom;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;

/**
 * rpc请求消息体生成工具.
 */
public class BuildMqRequestBodyUtils {


  /**
   * 生成大屏远程调用参数.
   *
   * @param videoWall 大屏.
   * @return 远程调用消息体.
   */
  public static MqRequest<PanelRectRequestBody> buildVideoWallMqBody(KvmVideoWall videoWall) {
    PanelRectRequestBody rectRequestBody = PanelRectRequestBody.builder()
        .id(videoWall.getWallId())
        .build();
    MqRequest<PanelRectRequestBody> mqRequest = new MqRequest<>();
    mqRequest.setMasterId(videoWall.getMasterId());
    mqRequest.setBody(rectRequestBody);
    return mqRequest;
  }

  /**
   * 生成大屏远程调用参数.
   */
  public static MqRequest<PanelRectRequestBody> buildVideoWallMqBody(KvmVideoWall videoWall,
                                                                     KvmOperationFrom from) {
    PanelRectRequestBody rectRequestBody = PanelRectRequestBody.builder()
        .id(videoWall.getWallId())
        .build();
    CaesarMqRequest<PanelRectRequestBody> mqRequest = new CaesarMqRequest<>();
    mqRequest.setMasterId(videoWall.getMasterId());
    mqRequest.setBody(rectRequestBody);
    mqRequest.setKvmOperationFrom(from);
    return mqRequest;
  }

  /**
   * 生成大屏远程调用参数.
   *
   * @param videoWall   大屏.
   * @param requestBody 请求体.
   * @param <T>         请求体类型.
   * @return 远程调用消息体.
   */
  public static <T> MqRequest<T> buildVideoWallMqBody(KvmVideoWall videoWall, T requestBody) {
    MqRequest<T> mqRequest = new MqRequest<>();
    mqRequest.setMasterId(videoWall.getMasterId());
    mqRequest.setBody(requestBody);
    return mqRequest;
  }

  /**
   * 生成大屏远程调用参数.
   */
  public static <T> MqRequest<T> buildVideoWallMqBody(KvmVideoWall videoWall, T requestBody,
                                                      KvmOperationFrom kvmOperationFrom) {
    CaesarMqRequest<T> caesarMqRequest = new CaesarMqRequest<>();
    caesarMqRequest.setMasterId(videoWall.getMasterId());
    caesarMqRequest.setBody(requestBody);
    caesarMqRequest.setKvmOperationFrom(kvmOperationFrom);
    return caesarMqRequest;
  }

  /**
   * 生成坐席远程调用参数.
   *
   * @param kvmSeat     坐席.
   * @param requestBody 请求体.
   * @param <T>         请求体类型.
   * @return 远程调用消息体.
   */
  public static <T> MqRequest<T> buildSeatMqBody(KvmSeat kvmSeat,
                                                 T requestBody) {
    MqRequest<T> mqRequest = new MqRequest<>();
    mqRequest.setMasterId(kvmSeat.getMasterId());
    mqRequest.setBody(requestBody);
    return mqRequest;
  }

  /**
   * 生成坐席远程调用参数.
   */
  public static <T> MqRequest<T> buildSeatMqBody(KvmSeat kvmSeat,
                                                 T requestBody,
                                                 KvmOperationFrom from) {
    CaesarMqRequest<T> mqRequest = new CaesarMqRequest<>();
    mqRequest.setMasterId(kvmSeat.getMasterId());
    mqRequest.setBody(requestBody);
    mqRequest.setKvmOperationFrom(from);
    return mqRequest;
  }

  /**
   * 生成主机远程调用参数.
   *
   * @param master 主机.
   * @return 远程调用消息体.
   */
  public static MqRequest<Void> buildKvmMasterMqBody(KvmMaster master) {
    MqRequest<Void> mqRequest = new MqRequest<>();
    mqRequest.setMasterId(master.getMasterId());
    return mqRequest;
  }

  /**
   * 生成主机远程调用参数.
   *
   * @param master 主机.
   * @return 远程调用消息体.
   */
  public static <T> MqRequest<T> buildKvmMasterMqBody(KvmMaster master, T requestBody) {
    MqRequest<T> mqRequest = new MqRequest<>();
    mqRequest.setMasterId(master.getMasterId());
    mqRequest.setBody(requestBody);
    return mqRequest;
  }

  /**
   * 生成外设远程调用参数.
   *
   * @param kvmAsset    外设.
   * @param requestBody 请求体.
   * @param <T>         .
   * @return 远程调用返回的消息体.
   */
  public static <T> MqRequest<T> buildKvmAssetMqBody(KvmAsset kvmAsset, T requestBody) {
    MqRequest<T> mqRequest = new MqRequest<>();
    mqRequest.setMasterId(kvmAsset.getMasterId());
    mqRequest.setBody(requestBody);
    return mqRequest;
  }

  /**
   * 生成远程调用参数.
   */
  public static <T> MqRequest<T> buildMqBody(T requestBody) {
    MqRequest<T> mqRequest = new MqRequest<>();
    mqRequest.setBody(requestBody);
    return mqRequest;
  }


}
