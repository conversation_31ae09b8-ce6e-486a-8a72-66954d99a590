package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.PositionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "position_group", description = "房间分组")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PositionGroup extends SkyLinkDbEntity {
  @Schema(hidden = true)
  @TableId(value = "position_id", type = IdType.AUTO)
  private Integer positionId;
  private String name; // 房间名称
  private Integer parentId; // 父级Id
  private Integer seqInParent; // 父级中的顺序
  private PositionType positionLevel; // 房间等级
  private Integer maxMeetPersonnel; // 最大人数
  private Long startUseTime; // 启用时间
  private Long finishUseTime; // 结束使用时间
}
