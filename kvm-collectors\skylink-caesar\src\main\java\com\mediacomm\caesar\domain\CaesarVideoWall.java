package com.mediacomm.caesar.domain;

import com.mediacomm.system.variable.sysenum.BannerType;
import com.mediacomm.system.variable.sysenum.DeviceType;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * CaesarVideoWall.
 */
@Data
public class CaesarVideoWall {
  private int id = -1;
  private String name;
  private int type; // 0 -> vp6, 1 -> vp7
  private int row;
  private int col;
  private boolean supportAudio; // 是否支持音频镜像
  private int totalWidth;
  private int totalHeight;
  private List<CaesarOnlineVideoWallDecoder> onlinedevices = new ArrayList<>();
  private List<CaesarDecoder> devices = new ArrayList<>();

  public DeviceType getDeviceType() {
    if (type == 1) {
      return DeviceType.CAESAR_VP7_VIDEO_WALL;
    }
    return DeviceType.CAESAR_VP6_VIDEO_WALL;
  }

  /**
   * 根据设备类型设置支持的横幅类型.
   */
  public BannerType getBannerType() {
    if (type == 1) {
      return BannerType.BANNER_VP7;
    }
    return BannerType.NON_SUPPORT;
  }
}
