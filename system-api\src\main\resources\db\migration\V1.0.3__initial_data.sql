update skylink.device_model_signal t
set  t.signals = '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查设备通信","thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"℃","statusThresholds":[]},{"signalId":"fanSpeed","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"风扇转速","signalType":"ANALOG","inputEnable":true,"signalGroup":"fan","aiLowerLimit":0,"aiThresholds":[],"aiUpperLimit":12000,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"rpm","statusThresholds":[]},{"signalId":"netStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"网口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"net","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已链接"},{"alarmLevel":"LEVEL_4","statusValue":"1","thresholdDesc":"","thresholdName":"未链接"}]},{"signalId":"acPowerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"交流电源状态","signalType":"STATUS","inputEnable":true,"signalGroup":"power","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"供电正常"},{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"检查电源线","thresholdName":"未供电"},{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"检查电源","thresholdName":"电源故障"}]},{"signalId":"dcPowerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"直流电源状态","signalType":"STATUS","inputEnable":true,"signalGroup":"power","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"供电正常"},{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"检查电源线","thresholdName":"未供电"},{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"检查电源","thresholdName":"电源故障"}]},{"signalId":"opticalStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"光口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"optical","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已链接"},{"alarmLevel":"LEVEL_4","statusValue":"1","thresholdDesc":"","thresholdName":"未链接"}]},{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态","signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已接入"},{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"","thresholdName":"未接入"}]},{"signalId":"videoResolution","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频分辨率","signalType":"STRING","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"usbStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"串口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已接入"},{"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"","thresholdName":"未接入"}]}]'
where t.device_model_id = 9001;

update skylink.device_model_signal t
set  t.signals = '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查设备通信","thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"℃","statusThresholds":[]},{"signalId":"fanSpeed","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"风扇转速","signalType":"ANALOG","inputEnable":true,"signalGroup":"fan","aiLowerLimit":0,"aiThresholds":[],"aiUpperLimit":12000,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"rpm","statusThresholds":[]},{"signalId":"netStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"网口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"net","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已链接"},{"alarmLevel":"LEVEL_4","statusValue":"1","thresholdDesc":"","thresholdName":"未链接"}]},{"signalId":"acPowerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"交流电源状态","signalType":"STATUS","inputEnable":true,"signalGroup":"power","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"供电正常"},{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"检查电源线","thresholdName":"未供电"},{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"检查电源","thresholdName":"电源故障"}]},{"signalId":"dcPowerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"直流电源状态","signalType":"STATUS","inputEnable":true,"signalGroup":"power","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"供电正常"},{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"检查电源线","thresholdName":"未供电"},{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"检查电源","thresholdName":"电源故障"}]},{"signalId":"opticalStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"光口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"optical","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已链接"},{"alarmLevel":"LEVEL_4","statusValue":"1","thresholdDesc":"","thresholdName":"未链接"}]},{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态","signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已接入"},{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"","thresholdName":"未接入"}]},{"signalId":"videoResolution","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频分辨率","signalType":"STRING","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"usbStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"串口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已接入"},{"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"","thresholdName":"未接入"}]}]'
where t.device_model_id = 9101;
