package com.mediacomm.protocol;

import com.mediacomm.domain.Operate;
import com.mediacomm.domain.OperateParam;
import com.mediacomm.domain.Ptz;
import com.mediacomm.util.TcpClient;
import java.io.IOException;

/**
 * Visca云台协议.
 */
public class Visca implements Protocol {

  private TcpClient getClient(Ptz ptz) throws IOException {
    return new TcpClient(ptz.getIp(), ptz.getPort());
  }

  private boolean sendCmd(TcpClient client, int address, String operate, OperateParam param)
      throws IOException {
    byte[] data = new byte[20];
    data[0] = (byte) (0x80 | address);
    byte speed = 0x10;
    data[1] = (byte) 0x01;
    int length;
    switch (operate) {
      case "reset":
        data[2] = (byte) 0x06;
        data[3] = (byte) 0x04;
        length = 4;
        break;
      case "left-up":
        data[2] = (byte) 0x06;
        data[3] = (byte) 0x01;
        data[4] = speed;
        data[5] = speed;
        data[6] = (byte) 0x01;
        data[7] = (byte) 0x01;
        length = 8;
        break;
      case "left-down":
        data[2] = (byte) 0x06;
        data[3] = (byte) 0x01;
        data[4] = speed;
        data[5] = speed;
        data[6] = (byte) 0x01;
        data[7] = (byte) 0x02;
        length = 8;
        break;
      case "right-up":
        data[2] = (byte) 0x06;
        data[3] = (byte) 0x01;
        data[4] = speed;
        data[5] = speed;
        data[6] = (byte) 0x02;
        data[7] = (byte) 0x01;
        length = 8;
        break;
      case "right-down":
        data[2] = (byte) 0x06;
        data[3] = (byte) 0x01;
        data[4] = speed;
        data[5] = speed;
        data[6] = (byte) 0x02;
        data[7] = (byte) 0x02;
        length = 8;
        break;
      case "up":
        data[2] = (byte) 0x06;
        data[3] = (byte) 0x01;
        data[4] = 0x00;
        data[5] = speed;
        data[6] = (byte) 0x03;
        data[7] = (byte) 0x01;
        length = 8;
        break;
      case "down":
        data[2] = (byte) 0x06;
        data[3] = (byte) 0x01;
        data[4] = 0x00;
        data[5] = speed;
        data[6] = (byte) 0x03;
        data[7] = (byte) 0x02;
        length = 8;
        break;
      case "left":
        data[2] = (byte) 0x06;
        data[3] = (byte) 0x01;
        data[4] = speed;
        data[5] = 0x00;
        data[6] = (byte) 0x01;
        data[7] = (byte) 0x03;
        length = 8;
        break;
      case "right":
        data[2] = (byte) 0x06;
        data[3] = (byte) 0x01;
        data[4] = speed;
        data[5] = 0x00;
        data[6] = (byte) 0x02;
        data[7] = (byte) 0x03;
        length = 8;
        break;
      case "stop-move":
        data[2] = (byte) 0x06;
        data[3] = (byte) 0x01;
        data[4] = 0x00;
        data[5] = 0x00;
        data[6] = (byte) 0x03;
        data[7] = (byte) 0x03;
        length = 8;
        break;
      case "zoom-out":
        data[2] = (byte) 0x04;
        data[3] = (byte) 0x07;
        data[4] = 0x02;
        length = 5;
        break;
      case "zoom-in":
        data[2] = (byte) 0x04;
        data[3] = (byte) 0x07;
        data[4] = 0x03;
        length = 5;
        break;
      case "stop-zoom":
        data[2] = (byte) 0x04;
        data[3] = (byte) 0x07;
        data[4] = 0x00;
        length = 5;
        break;
      case "load-preset":
        data[2] = (byte) 0x04;
        data[3] = (byte) 0x3f;
        data[4] = 0x02;
        data[5] = (byte) param.getPreset();
        length = 6;
        break;
      case "save-preset":
        data[2] = (byte) 0x04;
        data[3] = (byte) 0x3f;
        data[4] = 0x01;
        data[5] = (byte) param.getPreset();
        length = 6;
        break;
      case "home":
        data[2] = (byte) 0x06;
        data[3] = (byte) 0x04;
        length = 4;
        break;
      default:
        return false;
    }
    data[length] = (byte) 0XFF;
    length++;
    client.write(data, 0, length);
    client.close();
    return true;
  }

  @Override
  public boolean doOperate(Ptz ptz, Operate opt) {
    try {
      TcpClient client = getClient(ptz);
      return sendCmd(client, ptz.getAddress(), opt.getOperate(), opt.getParam());
    } catch (Exception e) {
      return false;
    }
  }

}
