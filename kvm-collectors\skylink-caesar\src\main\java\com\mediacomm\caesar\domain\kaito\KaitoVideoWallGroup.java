package com.mediacomm.caesar.domain.kaito;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import lombok.Data;

/**
 * .
 */
@Data
public class KaitoVideoWallGroup {
  private Integer id; // 组ID
  private String name;
  private String ip;
  private Integer port;
  @JsonProperty("pId")
  private String pId; // kaito接入ID
  @JsonProperty("videowalls")
  private Collection<DeviceIdAndName> videowalls;
}
