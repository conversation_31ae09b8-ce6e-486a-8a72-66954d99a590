package com.mediacomm.aircon;

import com.mediacomm.aircon.controller.AirconFeignClientApi;
import com.mediacomm.aircon.domain.AirconDeviceStatus;
import com.mediacomm.aircon.domain.AirconServerStatus;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.SignalValue;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.base.kvm.SignalCollector;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import feign.FeignException;
import jakarta.annotation.Resource;
import java.net.URI;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Slf4j
@Component
public class AirconSignalsCollector extends SignalCollector {
  @Resource
  private AirconFeignClientApi cli;
  @Resource
  private KvmMasterService kvmMasterService;
  @Resource
  private KvmAssetService kvmAssetService;

  /**
   * 每10秒刷新一次主机状态.
   */
  @Scheduled(cron = "*/10 * * * * ?")
  @SchedulerLock(name = "airconServer", lockAtLeastFor = "PT4S", lockAtMostFor = "PT4S")
  public void reloadAirconServer() {
    Collection<KvmMaster> kvmMasters = kvmMasterService.allByDeviceModel(
            DeviceType.AIRCON.getDeviceTypeId());
    // 批量缓存信号量数值
    Map<String, Map<String, String>> deviceSignalValueInCache = new HashMap<>();
    for (KvmMaster kvmMaster : kvmMasters) {
      // 缓存信号量数值
      Map<String, String> masterSignalValue = new HashMap<>();
      // 缓存描述的信息
      URI uri = URI.create(
              String.format("http://%s:%d", kvmMaster.getDeviceIp(), 80));
      List<AirconServerStatus> servers;
      String redisKey = RedisSignalKey.getDeviceStatusKey(DeviceType.AIRCON.getDeviceType(),
              kvmMaster.getMasterId());
      deviceSignalValueInCache.put(redisKey, masterSignalValue);
      try {
        servers = cli.getServersStatus(uri);
      } catch (FeignException e) {
        log.error(e.getMessage(), e);
        addDeviceSignalValue(kvmMaster, RedisSignalKey.LINK_STATUS,
                new SignalValue(Boolean.TRUE), masterSignalValue);
        continue;
      }
      addDeviceSignalValue(kvmMaster, RedisSignalKey.LINK_STATUS,
              new SignalValue(Boolean.FALSE), masterSignalValue);
      String sn = Property.findValueByKey(kvmMaster.getProperties(), "sn", "");
      for (AirconServerStatus server : servers) {
        if (server.getSn().equals(sn)) {
          // cpu
          addDeviceSignalValue(kvmMaster, RedisSignalKey.CPU_RATE,
                  new SignalValue(server.getCpuRate()), masterSignalValue);
          // men
          addDeviceSignalValue(kvmMaster, RedisSignalKey.MEM_RATE,
                  new SignalValue(server.getMemRate()), masterSignalValue);
          // disk
          addDeviceSignalValue(kvmMaster, RedisSignalKey.DISK_RATE,
                  new SignalValue(server.getDiskRate()), masterSignalValue);
          // temp
          addDeviceSignalValue(kvmMaster, RedisSignalKey.TEMPERATURE,
                  new SignalValue(server.getTemperature()), masterSignalValue);
          break;
        }
      }

      // asset signal
      List<AirconDeviceStatus> deviceStatuses = cli.getDevicesStatus(uri);
      // 与主机分开缓存写入，避免数据量太大占用网络带宽
      Map<String, Map<String, String>> assetSignalValueInCache = new HashMap<>();
      Collection<KvmAssetVo> assets = new ArrayList<>();
      for (AirconDeviceStatus deviceStatus : deviceStatuses) {
        KvmAssetVo asset = kvmAssetService.oneByHardcode(deviceStatus.getSn());
        if (asset != null) {
          assets.add(asset);
          Map<String, String> assetSignalValue = new HashMap<>();
          // link.status
          addDeviceSignalValue(asset, RedisSignalKey.LINK_STATUS,
                  new SignalValue(!deviceStatus.isLinkStatus()), assetSignalValue);
          // cpu
          addDeviceSignalValue(asset, RedisSignalKey.CPU_RATE,
                  new SignalValue(deviceStatus.getCpuRate()), assetSignalValue);
          // men
          addDeviceSignalValue(asset, RedisSignalKey.MEM_RATE,
                  new SignalValue(deviceStatus.getMemRate()), assetSignalValue);
          // disk
          addDeviceSignalValue(asset, RedisSignalKey.DISK_RATE,
                  new SignalValue(deviceStatus.getDiskRate()), assetSignalValue);
          // temp
          addDeviceSignalValue(asset, RedisSignalKey.TEMPERATURE,
                  new SignalValue(deviceStatus.getTemperature()), assetSignalValue);
          // resolution
          String resolution = String.format("%d x %d",
                  deviceStatus.getInputWidth(), deviceStatus.getInputHeight());
          addDeviceSignalValue(asset, RedisSignalKey.RESOLUTION,
                  new SignalValue(resolution), assetSignalValue);
          // video line status
          addDeviceSignalValue(asset, RedisSignalKey.VIDEO_LINE_STATUS,
                  new SignalValue(deviceStatus.getVideoLineStatus()), assetSignalValue); // 0:未检测，1:正常，2:故障
          assetSignalValueInCache.put(RedisSignalKey
                  .getDeviceStatusKey(asset.getDeviceType(), asset.getAssetId()), assetSignalValue);
        }
      }
      redisUtil.batchHashSet(assetSignalValueInCache);
      checkAssetSignalValue(assets);
    }
    redisUtil.batchHashSet(deviceSignalValueInCache);
    checkMasterSignalValue(kvmMasters, DeviceType.AIRCON);
  }

}
