package com.mediacomm.switcher.util.update;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.compress.archivers.cpio.CpioArchiveEntry;
import org.apache.commons.compress.archivers.cpio.CpioArchiveOutputStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

/**
 * 测试UpdateUtils类的功能.
 */
public class UpdateUtilsTest {

  @TempDir
  Path tempDir;

  private Path testFilePath;

  @BeforeEach
  void setUp() throws IOException {
    // 准备测试数据
    String versionContent = """
        product=dkm_cpu_con-hw5.1
        sys=V4.19-241024-git5c35ba571
        app=V4.0.30-241024-git28c52b1
        fpga_hdmi_tx=V4.04-240813
        fpga_hdmi_rx=V4.11-240820
        """;

    // 创建临时测试文件
    testFilePath = tempDir.resolve("test_update.swu");
    
    // 创建CPIO压缩包
    ByteArrayOutputStream cpioData = new ByteArrayOutputStream();
    try (CpioArchiveOutputStream cpioOut = new CpioArchiveOutputStream(cpioData)) {
      // 添加version.txt
      addCpioEntry(cpioOut, "version.txt", versionContent.getBytes(StandardCharsets.UTF_8));

      // 添加其他模拟文件
      addCpioEntry(cpioOut, "fpga.bin", new byte[1024]);
      addCpioEntry(cpioOut, "app.bin", new byte[2048]);
    }
    
    Files.write(testFilePath, cpioData.toByteArray());
  }

  @Test
  void testGetUpdatePkgInfoFromFile() throws IOException {
    // 执行测试（使用setUp创建的真实CPIO文件）
    SwitcherVersion result = UpdateUtils.getUpdatePkgInfo(testFilePath.toString());
    
    // 验证结果
    assertNotNull(result);
    assertEquals("dkm_cpu_con-hw5.1", result.getProduct());
    assertEquals("v4.19-241024-git5c35ba571", result.getSys());
    assertEquals("v4.0.30-241024-git28c52b1", result.getApp());
    assertEquals("v4.04-240813", result.getFpgaHdmiTx());
    assertEquals("v4.11-240820", result.getFpgaHdmiRx());
  }

  @Test
  void testGetUpdatePkgInfoFromInputStream() throws IOException {
    // 使用setUp创建的测试文件直接验证
    try (InputStream is = Files.newInputStream(testFilePath)) {
      SwitcherVersion result = UpdateUtils.getUpdatePkgInfo(is);
      
      // 验证结果
      assertNotNull(result);
      assertEquals("dkm_cpu_con-hw5.1", result.getProduct());
      assertEquals("v4.19-241024-git5c35ba571", result.getSys());
      assertEquals("v4.0.30-241024-git28c52b1", result.getApp());
      assertEquals("v4.04-240813", result.getFpgaHdmiTx());
      assertEquals("v4.11-240820", result.getFpgaHdmiRx());
    }
  }

  @Test
  void testGetUpdatePkgInfoWhenNoVersionFile() throws IOException {
    // 创建不含version.txt的CPIO包
    Path noVersionPath = tempDir.resolve("no_version.swu");
    
    ByteArrayOutputStream cpioData = new ByteArrayOutputStream();
    try (CpioArchiveOutputStream cpioOut = new CpioArchiveOutputStream(cpioData)) {
      // 只添加模拟文件（不添加version.txt）
      addCpioEntry(cpioOut, "fpga.bin", new byte[1024]);
      addCpioEntry(cpioOut, "app.bin", new byte[2048]);
    }
    
    Files.write(noVersionPath, cpioData.toByteArray());

    // 执行测试
    SwitcherVersion result = UpdateUtils.getUpdatePkgInfo(noVersionPath.toString());
    
    // 验证结果
    assertNull(result);
  }

  @Test
  void testReadVersionInfoMapWithInvalidLines() throws Exception {
    // 准备包含无效行的版本内容
    String invalidContent = """
        invalid line
        product=SwitcherTest
        invalid=format=value
        sys=v1.0-220101
        """;
    
    // 使用反射调用私有方法进行测试
    Method method = UpdateUtils.class.getDeclaredMethod(
        "readVersionInfoMap", BufferedReader.class);
    method.setAccessible(true);
    
    try (BufferedReader reader = new BufferedReader(new StringReader(invalidContent))) {
      
      @SuppressWarnings("unchecked")
      Map<String, String> result = (Map<String, String>) method.invoke(null, reader);
      
      // 验证结果
      assertNotNull(result);
      assertEquals(2, result.size());
      assertEquals("switchertest", result.get("product"));
      assertEquals("v1.0-220101", result.get("sys"));
    }
  }

  @Test
  void testApplyVersionInfo() throws Exception {
    // 准备测试数据
    Map<String, String> versionInfoMap = new HashMap<>();
    versionInfoMap.put("product", "switchertest");
    versionInfoMap.put("sys", "v1.0-220101");
    versionInfoMap.put("app", "v2.0-220202");
    versionInfoMap.put("fpga_hdmi_tx", "v3.0-220303");
    versionInfoMap.put("fpga_hdmi_rx", "v4.0-220404");
    versionInfoMap.put("unknown", "unknown-value");
    
    SwitcherVersion version = new SwitcherVersion();
    
    // 使用反射调用私有方法进行测试
    Method method = UpdateUtils.class.getDeclaredMethod(
        "applyVersionInfo", SwitcherVersion.class, Map.class);
    method.setAccessible(true);
    method.invoke(null, version, versionInfoMap);
    
    // 验证结果
    assertEquals("switchertest", version.getProduct());
    assertEquals("v1.0-220101", version.getSys());
    assertEquals("v2.0-220202", version.getApp());
    assertEquals("v3.0-220303", version.getFpgaHdmiTx());
    assertEquals("v4.0-220404", version.getFpgaHdmiRx());
  }

  @Test
  void testGetUpdatePkgInfoWithInvalidVersion() throws IOException {
    // 创建包含无效行的version.txt
    String invalidVersionContent = """
        product=invalid_product
        empty_line
        key_without_value=
        =value_without_key
        sys=v1.0-test
        """;
    
    Path invalidVersionPath = tempDir.resolve("invalid_version.swu");

    ByteArrayOutputStream cpioData = getByteArrayOutputStream(invalidVersionContent);

    Files.write(invalidVersionPath, cpioData.toByteArray());

    // 执行测试
    SwitcherVersion result = UpdateUtils.getUpdatePkgInfo(invalidVersionPath.toString());
    
    // 验证解析结果
    assertNotNull(result);
    assertEquals("invalid_product", result.getProduct());
    assertEquals("v1.0-test", result.getSys());
    assertNull(result.getApp());
    assertNull(result.getFpgaHdmiTx());
    assertNull(result.getFpgaHdmiRx());
  }

  private static ByteArrayOutputStream getByteArrayOutputStream(String invalidVersionContent)
      throws IOException {
    ByteArrayOutputStream cpioData = new ByteArrayOutputStream();
    try (CpioArchiveOutputStream cpioOut = new CpioArchiveOutputStream(cpioData)) {
      // 添加包含无效行的version.txt
      CpioArchiveEntry versionEntry = new CpioArchiveEntry("version.txt");
      byte[] versionBytes = invalidVersionContent.getBytes(StandardCharsets.UTF_8);
      versionEntry.setSize(versionBytes.length);
      cpioOut.putArchiveEntry(versionEntry);
      cpioOut.write(versionBytes);
      cpioOut.closeArchiveEntry();
    }
    return cpioData;
  }

  private void addCpioEntry(CpioArchiveOutputStream cpioOut, String entryName, byte[] content)
      throws IOException {
    CpioArchiveEntry entry = new CpioArchiveEntry(entryName);
    entry.setSize(content.length);
    cpioOut.putArchiveEntry(entry);
    cpioOut.write(content);
    cpioOut.closeArchiveEntry();
  }
}
