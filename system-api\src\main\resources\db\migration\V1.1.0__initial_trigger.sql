-- -----------------------------------------------------
-- TRIGGER `skylink`.`kvm_asset`
-- -----------------------------------------------------
DELIMITER $$

CREATE TRIGGER trg_after_kvm_asset_delete
    AFTER DELETE ON kvm_asset
    FOR EACH ROW
BEGIN
    -- 当 kvm_asset 表中的设备被删除时，
    -- 找到 alarm 表中所有与该设备相关的、尚未结束的告警
    -- 将它们的状态设置为 ended=true 并记录结束时间
    UPDATE skylink.alarm
    SET
        ended = 1,
        end_time = NOW()
    WHERE
        device_id = OLD.asset_id and ended = 0;
    END$$

DELIMITER ;

-- -----------------------------------------------------
-- TRIGGER `skylink`.`kvm_master`
-- -----------------------------------------------------
    DELIMITER $$

CREATE TRIGGER trg_after_kvm_master_delete
    AFTER DELETE ON kvm_master
    FOR EACH ROW
BEGIN
    UPDATE skylink.alarm
    SET
        ended = 1,
        end_time = NOW()
    WHERE
        device_id = OLD.master_id and ended = 0;
    END$$

DELIMITER ;

-- -----------------------------------------------------
-- TRIGGER `skylink`.`env_device`
-- -----------------------------------------------------
    DELIMITER $$

CREATE TRIGGER trg_after_env_device_delete
    AFTER DELETE ON env_device
    FOR EACH ROW
BEGIN
    UPDATE skylink.alarm
    SET
        ended = 1,
        end_time = NOW()
    WHERE
        device_id = OLD.id and ended = 0;
    END$$

DELIMITER ;
