package com.mediacomm.system.variable;

/**
 * 返回状态码的常量值.
 *
 * @author: WuZeJie.
 */
public interface ResponseCode {

  /**
   * 执行成功.
   */
  Integer EX_OK_200 = 200;

  /**
   * 内部程序执行失败.
   */
  Integer EX_FAILURE_500 = 500;

  /**
   * 请求参数错误.
   */
  Integer EX_FAILURE_400 = 400;

  /**
   * 用户验证失败.
   */
  Integer EX_UNAUTHORIZED_401 = 401;

  /**
   * 未找到指定资源.
   */
  Integer EX_NOTFOUND_404 = 404;

  /**
   * 请求方法不允许.
   */
  Integer EX_METHOD_NOT_ALLOW_405 = 405;

  /**
   * 请求与服务器端目标资源的当前状态相冲突.
   */
  Integer EX_CONFLICT_409 = 409;

  /**
   * 删除或更新父行存在外键约束.
   */
  Integer EX_SQL_CONSTRAINT_FOREIGN_1001 = 1001;

  /**
   * 权限不足.
   */
  Integer UNAUTHORIZED_3001 = 3001;
  /**
   * 超过窗口限制.
   */
  Integer OVER_OPEN_PANEL_LIMIT_13001 = 13001;

  /**
   * 屏幕分辨率不兼容.
   */
  Integer RESOLUTION_INCOMPATIBILITY_13002 = 13002;
  /**
   * 调用设备不在该主机上.
   */
  Integer DEVICE_NOT_IN_HOST_13003 = 13003;

  /**
   * 窗口不允许重叠.
   */
  Integer OVER_OPEN_PANEL_LAY = 13004;

  /**
   * 超出限制器/资源不足.
   */
  Integer OUT_OF_LIMIT_13005 = 13005;

  /**
   * 未知错误.
   */
  Integer UNKNOWN_ERROR_13006 = 13006;

  /**
   * 远程方法消息超时.
   */
  Integer REMOTE_MSG_TIME_OUT = 2001;

}
