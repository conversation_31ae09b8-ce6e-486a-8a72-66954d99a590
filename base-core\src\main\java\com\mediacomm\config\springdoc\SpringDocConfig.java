package com.mediacomm.config.springdoc;

import com.mediacomm.system.variable.ResUrlDef;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * .
 */
@Configuration
public class SpringDocConfig {

  /**
   * swagger doc.
   *
   * @return OpenAPI.
   */
  @Bean
  public OpenAPI restfulOpenApi() {
    return new OpenAPI()
        .info(new Info().title("SkyLink Server API")
            .description("SkyLink对外HTTP接口")
            .version(ResUrlDef.API_V))
        .externalDocs(new ExternalDocumentation()
            .description("SpringDoc")
            .url("https://springdoc.org/v2"))
        .components(new Components().addSecuritySchemes("token", new SecurityScheme().type(
            SecurityScheme.Type.APIKEY).in(SecurityScheme.In.HEADER).name("Authorization")))
        .addSecurityItem(new SecurityRequirement().addList("token"));
  }

}
