package com.mediacomm.entity;

import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.util.JsonUtils;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 最终返回呈现的结果.
 *
 * @author: WuZeJie.
 */
@Data
@NoArgsConstructor
public class Result<T> implements Serializable {
  @Serial
  private static final long serialVersionUID = 1L;

  private String message = "";

  private Integer code = ResponseCode.EX_OK_200;

  private T result;

  private long timestamp = System.currentTimeMillis();

  public Result(String message) {
    this.message = message;
  }

  public Result(Integer code, String message) {
    this.code = code;
    this.message = message;
  }

  public Result(String message, T result) {
    this.message = message;
    this.result = result;
  }

  /**
   * Result.
   */
  public Result(Integer code, String message, T result) {
    this.code = code;
    this.message = message;
    this.result = result;
  }

  public static <T> Result<T> ok() {
    return new Result<>();
  }

  /**
   * 不包含消息体的成功返回，附带提示信息.
   *
   * @param message 提示信息.
   * @param <T>     消息体类型.
   * @return Result.
   */
  public static <T> Result<T> ok(String message) {
    return new Result<>(message);
  }

  /**
   * 包含消息体的成功返回.
   *
   * @param data 消息体.
   * @param <T>  消息体类型.
   * @return T.
   */
  public static <T> Result<T> ok(T data) {
    return new Result<>("success", data);
  }

  /**
   * 包含消息体及提示信息的成功返回.
   *
   * @param message 提示信息.
   * @param data    消息体.
   * @param <T>     消息体类型.
   * @return T.
   */
  public static <T> Result<T> ok(String message, T data) {
    return new Result<>(message, data);
  }

  public static <T> String okStr(T data) {
    return JsonUtils.encode(new Result<>("success", data));
  }

  public static String okStr() {
    return JsonUtils.encode(new Result<>());
  }

  /**
   * 包含提示信息及code的失败返回.
   *
   * @param message 提示信息.
   * @param code    失败码.
   * @param <T>     消息体类型.
   * @return Result.
   */
  public static <T> Result<T> failure(String message, Integer code) {
    return new Result<>(code, message);
  }

  /**
   * 包含提示信息、code及消息体的失败返回.
   *
   * @param message 提示信息.
   * @param code    失败码.
   * @param data    消息体.
   * @param <T>     消息体类型.
   * @return Result.
   */
  public static <T> Result<T> failure(String message, Integer code, T data) {
    return new Result<>(code, message, data);
  }

  public static String failureStr(String message, Integer code) {
    return JsonUtils.encode(new Result<>(code, message));
  }
}
