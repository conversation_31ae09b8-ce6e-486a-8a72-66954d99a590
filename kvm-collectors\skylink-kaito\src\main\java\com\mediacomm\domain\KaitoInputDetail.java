package com.mediacomm.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoInputDetail {
  private KaitoInput.AudioInterface audioInterface;
  private int inputId;
  private int isSupportAudio;
  private int isUsed;
  private String name;
  private boolean online;
  private int slotId;
  private int cardCategory;
  @JsonProperty("iSignal")
  private int iSignal;
  private Resolution resolution;
  private KaitoOutputDetail.General general;
}
