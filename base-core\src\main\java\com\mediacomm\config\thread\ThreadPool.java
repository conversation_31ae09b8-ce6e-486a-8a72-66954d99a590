package com.mediacomm.config.thread;

import java.util.concurrent.ThreadPoolExecutor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * .
 *
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 */
@Configuration
@ConfigurationProperties("spring.thread")
@Setter
public class ThreadPool {
  private Integer core;
  private Integer seconds;
  private String prefix;

  /**
   * .
   */
  @Bean
  public ThreadPoolTaskExecutor createThreadPool() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(core);
    executor.setMaxPoolSize(core * 2 + 1);
    executor.setKeepAliveSeconds(seconds);
    executor.setThreadNamePrefix(prefix);
    executor.setRejectedExecutionHandler(
        new ThreadPoolExecutor.CallerRunsPolicy()); // 策略：由调用者线程直接执行任务
    return executor;
  }
}
