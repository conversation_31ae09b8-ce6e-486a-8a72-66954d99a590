<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.mediacomm</groupId>
    <artifactId>skylink-server</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>

  <artifactId>kvm-collectors</artifactId>
  <packaging>pom</packaging>
  <description>坐席管理平台采集模块</description>

  <modules>
    <module>skylink-caesar</module>
    <module>skylink-aircon</module>
    <module>skylink-hikvision</module>
    <module>skylink-ptz</module>
    <module>skylink-kaito</module>
    <module>skylink-tsingli</module>
    <module>skylink-switcher</module>
    <module>skylink-exchange</module>
  </modules>

  <dependencies>
    <dependency>
      <groupId>com.mediacomm</groupId>
      <artifactId>base-core</artifactId>
      <version>0.0.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mediacomm</groupId>
      <artifactId>base-integration-test</artifactId>
      <version>0.0.1-SNAPSHOT</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
